#! /usr/bin/env python
#  -*- coding: utf-8 -*-
#
# 现代化像素计算器 - Bootstrap风格UI
# 美化版本，保持原有功能

import sys

try:
    import Tkinter as tk
except ImportError:
    import tkinter as tk

try:
    import ttk
    py3 = False
except ImportError:
    import tkinter.ttk as ttk
    py3 = True

import pixel_calculation_support


def vp_start_gui():
    '''Starting point when module is the main routine.'''
    global val, w, root
    root = tk.Tk()
    root.resizable(False, False)
    root.update()
    screenwidth = root.winfo_screenwidth()
    screenheight = root.winfo_screenheight()
    # 适中的窗口尺寸，保持功能布局
    width = 480
    height = 200
    size = '%dx%d+%d+%d' % (width, height, (screenwidth - width) / 2, (screenheight - height) / 2)
    root.geometry(size)

    top = Toplevel1(root)
    pixel_calculation_support.init(root, top)
    root.mainloop()


w = None


def create_Toplevel1(root, *args, **kwargs):
    '''Starting point when module is imported by another program.'''
    global w, w_win, rt
    rt = root
    w = tk.Toplevel(root)
    top = Toplevel1(w)
    pixel_calculation_support.init(w, top, *args, **kwargs)
    return (w, top)


def destroy_Toplevel1():
    global w
    w.destroy()
    w = None


class Toplevel1:
    def __init__(self, top=None):
        '''现代化像素计算器 - Bootstrap风格UI'''
        # Bootstrap风格颜色方案
        self.bg_primary = '#007bff'      # Bootstrap蓝
        self.bg_light = '#f8f9fa'        # 浅灰背景
        self.bg_white = '#ffffff'        # 白色
        self.text_dark = '#212529'       # 深灰文字
        self.border_color = '#dee2e6'    # 浅灰边框
        self.text_muted = '#6c757d'      # 灰色文字

        top.title("现代化像素计算器")
        top.configure(background=self.bg_light)

        # 设置现代化字体
        self.title_font = ('Microsoft YaHei UI', 16, 'bold')
        self.label_font = ('Microsoft YaHei UI', 10)
        self.entry_font = ('Microsoft YaHei UI', 11)

        self.setup_ui(top)

    def setup_ui(self, top):
        '''设置Bootstrap风格的UI布局 - 保持原有4输入框功能'''
        # 配置ttk样式
        style = ttk.Style()
        style.theme_use('clam')  # 使用更现代的主题

        # 配置Entry样式
        style.configure('Modern.TEntry',
                       fieldbackground='white',
                       borderwidth=1,
                       relief='solid',
                       padding=6)

        # 主标题栏
        title_frame = tk.Frame(top, bg=self.bg_primary, height=45)
        title_frame.pack(fill='x', padx=0, pady=0)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="🔧 像素尺寸计算器",
                              font=('Microsoft YaHei UI', 14, 'bold'),
                              bg=self.bg_primary,
                              fg='white')
        title_label.pack(expand=True)

        # 主容器
        main_frame = tk.Frame(top, bg=self.bg_light)
        main_frame.pack(fill='both', expand=True, padx=15, pady=15)

        # 第一行：原始尺寸
        row1_frame = tk.Frame(main_frame, bg=self.bg_light)
        row1_frame.pack(fill='x', pady=(0, 10))

        # 原始宽度
        tk.Label(row1_frame, text="原始宽", font=self.label_font,
                bg=self.bg_light, fg=self.text_dark).pack(side='left', padx=(0, 5))
        self.TEntry1 = ttk.Entry(row1_frame, font=self.entry_font, style='Modern.TEntry', width=15)
        self.TEntry1.pack(side='left', padx=(0, 20))

        # 原始高度
        tk.Label(row1_frame, text="原始高", font=self.label_font,
                bg=self.bg_light, fg=self.text_dark).pack(side='left', padx=(0, 5))
        self.TEntry2 = ttk.Entry(row1_frame, font=self.entry_font, style='Modern.TEntry', width=15)
        self.TEntry2.pack(side='left')

        # 第二行：修改尺寸
        row2_frame = tk.Frame(main_frame, bg=self.bg_light)
        row2_frame.pack(fill='x', pady=(0, 10))

        # 修改宽度
        tk.Label(row2_frame, text="修改宽", font=self.label_font,
                bg=self.bg_light, fg=self.text_dark).pack(side='left', padx=(0, 5))
        self.TEntry3 = ttk.Entry(row2_frame, font=self.entry_font, style='Modern.TEntry', width=15)
        self.TEntry3.pack(side='left', padx=(0, 20))
        self.TEntry3.bind('<KeyRelease>', lambda event: self.update_dimensions(event, 'width'))

        # 修改高度
        tk.Label(row2_frame, text="修改高", font=self.label_font,
                bg=self.bg_light, fg=self.text_dark).pack(side='left', padx=(0, 5))
        self.TEntry4 = ttk.Entry(row2_frame, font=self.entry_font, style='Modern.TEntry', width=15)
        self.TEntry4.pack(side='left')
        self.TEntry4.bind('<KeyRelease>', lambda event: self.update_dimensions(event, 'height'))

        # 说明文字
        info_label = tk.Label(main_frame,
                             text="💡 输入原始尺寸后，在修改宽或修改高中输入数值，另一值将自动计算",
                             font=('Microsoft YaHei UI', 9),
                             bg=self.bg_light, fg=self.text_muted,
                             wraplength=400, justify='center')
        info_label.pack(pady=(10, 0))

    def update_dimensions(self, event, dimension):
        '''等比例计算尺寸 - 保持原有功能逻辑'''
        try:
            # 获取原始尺寸
            orig_width_str = self.TEntry1.get().strip()
            orig_height_str = self.TEntry2.get().strip()

            if not orig_width_str or not orig_height_str:
                return  # 需要输入原始尺寸

            orig_width = float(orig_width_str)
            orig_height = float(orig_height_str)

            if orig_width <= 0 or orig_height <= 0:
                return  # 需要有效的原始尺寸

            if dimension == 'width':
                # 输入了修改宽，计算修改高
                new_width_str = self.TEntry3.get().strip()
                if new_width_str:
                    new_width = float(new_width_str)
                    if new_width > 0:
                        # 计算对应的高度：新高 = 新宽 * (原高/原宽)
                        calculated_height = round(new_width * orig_height / orig_width)
                        self.TEntry4.delete(0, tk.END)
                        self.TEntry4.insert(0, str(calculated_height))

            elif dimension == 'height':
                # 输入了修改高，计算修改宽
                new_height_str = self.TEntry4.get().strip()
                if new_height_str:
                    new_height = float(new_height_str)
                    if new_height > 0:
                        # 计算对应的宽度：新宽 = 新高 * (原宽/原高)
                        calculated_width = round(new_height * orig_width / orig_height)
                        self.TEntry3.delete(0, tk.END)
                        self.TEntry3.insert(0, str(calculated_width))

        except ValueError:
            pass  # 忽略无效输入


if __name__ == '__main__':
    vp_start_gui()
