import customtkinter as ctk
from tkinter import filedialog
import threading
import os
from processor import separate_audio, get_optimal_codec

# 尝试导入拖拽支持
try:
    from tkinterdnd2 import DND_FILES, TkinterDnD
    DRAG_DROP_AVAILABLE = True
except ImportError:
    DRAG_DROP_AVAILABLE = False

# 导入系统相关模块用于拖拽
import tkinter as tk

# 创建支持拖拽的CustomTkinter类
if DRAG_DROP_AVAILABLE:
    class CTkDnD(ctk.CTk, TkinterDnD.DnDWrapper):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.TkdndVersion = TkinterDnD._require(self)
else:
    class CTkDnD(ctk.CTk):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)

class App(CTkDnD):
    def __init__(self):
        super().__init__()

        self.title("🎬 视频音频分离器 - 提取音频 & 生成静音视频")
        self.geometry("800x500")
        self.minsize(600, 400)
        ctk.set_appearance_mode("System")  # Modes: "System" (default), "Dark", "Light"
        ctk.set_default_color_theme("blue")  # Themes: "blue" (default), "green", "dark-blue"

        # 设置自定义颜色
        self.configure(fg_color=("#f0f0f0", "#1a1a1a"))

        # 先设置窗口位置，稍后居中
        self.after(10, self.center_window)

        # Configure main grid layout
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # Create main container frame
        self.main_frame = ctk.CTkFrame(self, corner_radius=15)
        self.main_frame.grid(row=0, column=0, padx=20, pady=20, sticky="nsew")

        # Configure main frame grid
        self.main_frame.grid_columnconfigure(1, weight=1)
        self.main_frame.grid_rowconfigure(4, weight=1)

        # --- Title ---
        self.title_label = ctk.CTkLabel(
            self.main_frame,
            text="🎬 视频音频分离器",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.title_label.grid(row=0, column=0, columnspan=3, padx=30, pady=(30, 20), sticky="ew")

        # --- Input Section ---
        self.input_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        self.input_frame.grid(row=1, column=0, columnspan=3, padx=30, pady=(10, 15), sticky="ew")
        self.input_frame.grid_columnconfigure(1, weight=1)

        self.label_file = ctk.CTkLabel(
            self.input_frame,
            text="📁 视频文件:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.label_file.grid(row=0, column=0, padx=20, pady=(15, 10), sticky="w")

        self.entry_file = ctk.CTkEntry(
            self.input_frame,
            placeholder_text="请选择一个视频文件或拖拽文件到此处...",
            height=35,
            font=ctk.CTkFont(size=12)
        )
        self.entry_file.grid(row=0, column=1, padx=(10, 15), pady=(15, 10), sticky="ew")

        self.browse_button = ctk.CTkButton(
            self.input_frame,
            text="📂 浏览",
            command=self.browse_file,
            width=80,
            height=35,
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.browse_button.grid(row=0, column=2, padx=(0, 20), pady=(15, 10))

        # --- Output Section ---
        self.output_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        self.output_frame.grid(row=2, column=0, columnspan=3, padx=30, pady=(0, 15), sticky="ew")
        self.output_frame.grid_columnconfigure(1, weight=1)

        self.label_output = ctk.CTkLabel(
            self.output_frame,
            text="💾 输出目录:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.label_output.grid(row=0, column=0, padx=20, pady=(15, 10), sticky="w")

        self.entry_output = ctk.CTkEntry(
            self.output_frame,
            placeholder_text="默认为视频文件所在目录",
            height=35,
            font=ctk.CTkFont(size=12)
        )
        self.entry_output.grid(row=0, column=1, padx=(10, 15), pady=(15, 10), sticky="ew")

        self.browse_output_button = ctk.CTkButton(
            self.output_frame,
            text="📂 浏览",
            command=self.browse_output_directory,
            width=80,
            height=35,
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.browse_output_button.grid(row=0, column=2, padx=(0, 20), pady=(15, 10))

        # --- Control Section ---
        self.control_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        self.control_frame.grid(row=3, column=0, columnspan=3, padx=30, pady=(0, 15), sticky="ew")
        self.control_frame.grid_columnconfigure(0, weight=1)

        self.start_button = ctk.CTkButton(
            self.control_frame,
            text="🚀 开始分离 (音频+静音视频)",
            command=self.start_separation_thread,
            height=45,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.start_button.grid(row=0, column=0, padx=20, pady=(20, 15), sticky="ew")

        # Progress section
        self.progress_frame = ctk.CTkFrame(self.control_frame, fg_color="transparent")
        self.progress_frame.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="ew")
        self.progress_frame.grid_columnconfigure(0, weight=1)

        self.progress_bar = ctk.CTkProgressBar(self.progress_frame, height=20)
        self.progress_bar.set(0)
        self.progress_bar.grid(row=0, column=0, sticky="ew")

        self.progress_label = ctk.CTkLabel(
            self.progress_frame,
            text="0%",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.progress_label.grid(row=0, column=1, padx=(10, 0))

        # --- Status Section ---
        self.status_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        self.status_frame.grid(row=4, column=0, columnspan=3, padx=30, pady=(0, 30), sticky="ew")

        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="✨ 欢迎使用！将同时生成音频文件和静音视频文件。",
            font=ctk.CTkFont(size=14),
            wraplength=600
        )
        self.status_label.grid(row=0, column=0, padx=20, pady=15, sticky="ew")

        # 设置拖拽功能
        self.setup_drag_and_drop()

        # 检测GPU并显示信息
        self.detect_gpu_and_show_info()

    def center_window(self):
        """将窗口居中显示在屏幕上"""
        # 强制更新窗口
        self.update()

        # 获取屏幕尺寸
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()

        # 设置窗口尺寸
        window_width = 800
        window_height = 500

        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # 直接设置几何位置
        self.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 确保窗口显示在前台
        self.lift()
        self.focus_force()

    def set_status_message(self, message, status_type="info", auto_clear_delay=0):
        """设置状态消息的通用方法

        Args:
            message (str): 要显示的消息
            status_type (str): 消息类型 - "success", "error", "info", "processing"
            auto_clear_delay (int): 自动清除延迟（毫秒），0表示不自动清除
        """
        # 定义不同状态的颜色和图标
        status_config = {
            "success": {"icon": "✅", "color": ("#2d8f2d", "#4ade80")},
            "error": {"icon": "❌", "color": ("#d32f2f", "#ef4444")},
            "info": {"icon": "✨", "color": ("gray10", "gray90")},
            "processing": {"icon": "⚡", "color": ("#1976d2", "#3b82f6")}
        }

        config = status_config.get(status_type, status_config["info"])
        full_message = f"{config['icon']} {message}"

        self.status_label.configure(
            text=full_message,
            text_color=config["color"]
        )

        # 自动清除消息
        if auto_clear_delay > 0:
            self.after(auto_clear_delay, lambda: self.set_status_message(
                "欢迎使用！请选择文件开始。", "info"
            ))

    def setup_drag_and_drop(self):
        """设置文件拖拽功能"""
        # 实现简单的拖拽功能
        # 绑定键盘事件来模拟拖拽
        self.bind("<Control-o>", self.quick_open_file)

        # 设置输入框的拖拽提示
        self.entry_file.bind("<Button-3>", self.show_drag_menu)  # 右键菜单

        # 尝试使用tkinterdnd2
        if DRAG_DROP_AVAILABLE:
            try:
                # 为输入框注册拖拽目标
                self.entry_file.drop_target_register(DND_FILES)
                self.entry_file.dnd_bind('<<Drop>>', self.on_file_drop)
                self.entry_file.dnd_bind('<<DragEnter>>', self.on_drag_enter)
                self.entry_file.dnd_bind('<<DragLeave>>', self.on_drag_leave)

                # 也为主窗口注册拖拽
                self.drop_target_register(DND_FILES)
                self.dnd_bind('<<Drop>>', self.on_file_drop)

                self.set_status_message("✅ 拖拽功能已启用！可以直接拖拽文件到窗口", "success", 4000)
                return
            except Exception as e:
                self.set_status_message(f"拖拽功能初始化失败: {str(e)}", "error", 3000)

        # 如果没有tkinterdnd2，提供替代方案
        self.set_status_message("提示：使用Ctrl+O快速打开文件，或右键输入框查看更多选项", "info", 5000)

    def on_drag_enter(self, event):
        """拖拽进入窗口"""
        _ = event  # 忽略未使用的参数
        self.entry_file.configure(border_color=("#3b82f6", "#60a5fa"))
        self.set_status_message("松开鼠标以放置文件", "info")

    def on_drag_leave(self, event):
        """拖拽离开窗口"""
        _ = event  # 忽略未使用的参数
        self.entry_file.configure(border_color=("gray70", "gray30"))
        self.set_status_message("欢迎使用！请选择文件开始。", "info")

    def on_file_drop(self, event):
        """处理文件拖拽"""
        try:
            # 重置边框颜色
            self.entry_file.configure(border_color=("gray70", "gray30"))

            # 获取拖拽的文件路径
            if DRAG_DROP_AVAILABLE:
                # 处理tkinterdnd2的数据格式
                raw_data = event.data

                # 多种格式的文件路径处理
                if raw_data.startswith('{') and raw_data.endswith('}'):
                    # 处理 {文件路径} 格式
                    file_path = raw_data.strip('{}')
                elif ' ' in raw_data and raw_data.count('{') > 0:
                    # 处理多个文件的情况，取第一个
                    files = []
                    parts = raw_data.split()
                    for part in parts:
                        if part.startswith('{') or (not part.startswith('{') and len(files) == 0):
                            files.append(part.strip('{}'))
                    file_path = files[0] if files else raw_data
                else:
                    # 直接使用原始数据
                    file_path = raw_data.strip()

                # 清理路径中的引号
                file_path = file_path.strip('"\'')

                # 调试信息
                print(f"拖拽原始数据: {repr(raw_data)}")
                print(f"处理后路径: {repr(file_path)}")
                print(f"文件是否存在: {os.path.exists(file_path)}")

                if not file_path:
                    self.set_status_message("未能获取拖拽的文件路径", "error", 3000)
                    return
            else:
                self.set_status_message("拖拽功能不可用", "error", 3000)
                return

            if self.is_valid_video_file(file_path):
                self.entry_file.delete(0, ctk.END)
                self.entry_file.insert(0, file_path)
                self.entry_output.delete(0, ctk.END)
                self.entry_output.insert(0, os.path.dirname(file_path))
                self.set_status_message(
                    f"已选择文件: {os.path.basename(file_path)}",
                    "success"
                )
            else:
                # 提供更详细的错误信息
                if not os.path.exists(file_path):
                    self.set_status_message(
                        f"文件不存在: {file_path}",
                        "error",
                        8000
                    )
                else:
                    file_ext = os.path.splitext(file_path)[1].lower()
                    self.set_status_message(
                        f"不支持的文件格式: {file_ext}。支持的格式: mp4, avi, mkv, mov, flv, wmv, webm, m4v",
                        "error",
                        8000
                    )
        except Exception as e:
            self.set_status_message(
                f"拖拽文件时出错: {str(e)}",
                "error",
                8000
            )

    def is_valid_video_file(self, file_path):
        """检查是否为有效的视频文件"""
        if not os.path.exists(file_path):
            return False

        valid_extensions = ['.mp4', '.avi', '.mkv', '.mov', '.flv', '.wmv', '.webm', '.m4v']
        file_ext = os.path.splitext(file_path)[1].lower()
        return file_ext in valid_extensions

    def browse_file(self):
        file_path = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=([
                ("视频文件", "*.mp4 *.avi *.mkv *.mov *.flv"),
                ("所有文件", "*.*"),
            ])
        )
        if file_path:
            self.entry_file.delete(0, ctk.END)
            self.entry_file.insert(0, file_path)
            self.set_status_message(
                f"已选择文件: {os.path.basename(file_path)}",
                "success"
            )
            # Auto-fill output directory
            self.entry_output.delete(0, ctk.END)
            self.entry_output.insert(0, os.path.dirname(file_path))

    def browse_output_directory(self):
        dir_path = filedialog.askdirectory(title="选择输出目录")
        if dir_path:
            self.entry_output.delete(0, ctk.END)
            self.entry_output.insert(0, dir_path)

    def update_progress(self, value, message=""):
        """更新进度条和状态信息"""
        self.progress_bar.set(value)
        self.progress_label.configure(text=f"{int(value*100)}%")
        if message:
            self.set_status_message(message, "processing")

    def start_separation_thread(self):
        video_path = self.entry_file.get()
        if not video_path or not os.path.exists(video_path):
            self.set_status_message("请输入一个有效的视频文件路径。", "error", 5000)
            return

        output_dir = self.entry_output.get()
        if not output_dir:
            output_dir = os.path.dirname(video_path)

        if not os.path.isdir(output_dir):
            self.set_status_message(f"指定的输出目录无效或不存在：{output_dir}", "error", 8000)
            return

        self.start_button.configure(state="disabled")
        self.set_status_message("正在分离音频和视频，请稍候...", "processing")
        self.progress_bar.set(0)

        thread = threading.Thread(
            target=self.run_separation,
            args=(video_path, output_dir)
        )
        thread.daemon = True
        thread.start()

    def run_separation(self, video_path, output_dir):
        success, message = separate_audio(video_path, output_dir, self.update_progress)

        self.after(0, self.on_separation_complete, success, message)

    def on_separation_complete(self, success, message):
        """处理完成后的状态更新"""
        if success:
            self.progress_bar.set(1)
            self.progress_label.configure(text="100%")
            self.set_status_message(message, "success", 5000)
        else:
            self.progress_bar.set(0)
            self.progress_label.configure(text="0%")
            self.set_status_message(message, "error", 8000)

        self.start_button.configure(state="normal")

    def detect_gpu_and_show_info(self):
        """检测GPU并显示信息"""
        try:
            _, codec_info = get_optimal_codec()  # 忽略codec变量
            if 'GPU' in codec_info:
                self.set_status_message(f"🚀 已检测到GPU加速：{codec_info}", "success", 5000)
            else:
                self.set_status_message(f"💻 将使用CPU处理：{codec_info}", "info", 5000)
        except Exception as e:
            self.set_status_message("⚠️ GPU检测失败，将使用CPU处理", "info", 3000)

    def quick_open_file(self, event):
        """快速打开文件的快捷键功能"""
        _ = event  # 忽略未使用的参数
        self.browse_file()

    def show_drag_menu(self, event):
        """显示右键菜单"""
        _ = event  # 忽略未使用的参数
        # 创建右键菜单
        context_menu = tk.Menu(self, tearoff=0)
        context_menu.add_command(label="📂 选择文件", command=self.browse_file)
        context_menu.add_command(label="📋 粘贴路径", command=self.paste_file_path)
        context_menu.add_separator()
        context_menu.add_command(label="🚀 检查GPU状态", command=self.detect_gpu_and_show_info)
        context_menu.add_command(label="💡 拖拽提示", command=self.show_drag_help)

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def paste_file_path(self):
        """从剪贴板粘贴文件路径"""
        try:
            clipboard_content = self.clipboard_get()
            if clipboard_content and os.path.exists(clipboard_content):
                if self.is_valid_video_file(clipboard_content):
                    self.entry_file.delete(0, ctk.END)
                    self.entry_file.insert(0, clipboard_content)
                    self.entry_output.delete(0, ctk.END)
                    self.entry_output.insert(0, os.path.dirname(clipboard_content))
                    self.set_status_message(f"已粘贴文件: {os.path.basename(clipboard_content)}", "success")
                else:
                    self.set_status_message("剪贴板中的路径不是有效的视频文件", "error", 3000)
            else:
                self.set_status_message("剪贴板中没有有效的文件路径", "error", 3000)
        except Exception as e:
            self.set_status_message(f"粘贴失败: {str(e)}", "error", 3000)

    def show_drag_help(self):
        """显示拖拽帮助信息"""
        help_info = "文件选择方式: 浏览按钮 | Ctrl+O快捷键 | 右键粘贴路径 | 拖拽文件"
        self.set_status_message(f"💡 {help_info}", "info", 6000)

    def test_file_validation(self, test_path):
        """测试文件验证功能"""
        print(f"测试文件路径: {repr(test_path)}")
        print(f"文件是否存在: {os.path.exists(test_path)}")
        if os.path.exists(test_path):
            file_ext = os.path.splitext(test_path)[1].lower()
            print(f"文件扩展名: {repr(file_ext)}")
            valid_extensions = ['.mp4', '.avi', '.mkv', '.mov', '.flv', '.wmv', '.webm', '.m4v']
            print(f"是否在支持列表中: {file_ext in valid_extensions}")
            print(f"支持的扩展名: {valid_extensions}")
        return self.is_valid_video_file(test_path)
