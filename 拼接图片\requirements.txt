# 图片拼接工具 - PySide6版本依赖包清单
# 使用命令安装: pip install -r requirements.txt

# === 核心UI框架 ===
PySide6>=6.5.0

# === 图像处理 ===
Pillow>=10.0.0

# === 系统工具 ===
pathlib2>=2.3.0  # Python 3.4+ 兼容性

# === 安装说明 ===
# 1. 确保Python版本 >= 3.8
# 2. 运行: pip install -r requirements.txt
# 3. 启动程序: python spliced_pictures_qt.py
#
# 注意事项：
# - PySide6需要较新的操作系统支持
# - Windows 10/11, macOS 10.14+, Linux (较新发行版)
# - 如果安装失败，请更新pip: python -m pip install --upgrade pip
