#!/usr/bin/env python3
"""
演员名称查询应用 - 主入口文件
重构后的模块化应用程序
"""

import sys
import traceback
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.components.main_window import MainWindow


def setup_application() -> QApplication:
    """
    设置应用程序
    
    Returns:
        QApplication: 配置好的应用程序实例
    """
    # 创建应用程序实例
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("演员名称查询")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("MyTools")
    app.setOrganizationDomain("mytools.local")
    
    # 设置应用程序级别的样式
    app.setStyle("Fusion")
    
    # 设置高DPI支持
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    return app


def show_error_dialog(title: str, message: str, details: str = None):
    """
    显示错误对话框
    
    Args:
        title: 错误标题
        message: 错误消息
        details: 详细错误信息
    """
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Critical)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    
    if details:
        msg_box.setDetailedText(details)
    
    msg_box.exec_()


def main():
    """主函数"""
    try:
        # 设置应用程序
        app = setup_application()
        
        # 创建并显示主窗口
        window = MainWindow()
        window.show()
        
        # 运行应用程序
        return app.exec_()
        
    except ImportError as e:
        error_msg = f"模块导入失败: {e}"
        print(f"错误: {error_msg}")
        
        # 如果PyQt5可用，显示错误对话框
        try:
            app = QApplication(sys.argv) if 'app' not in locals() else app
            show_error_dialog(
                "模块导入错误",
                "应用程序启动失败，缺少必要的模块。",
                error_msg
            )
        except:
            pass
        
        return 1
        
    except Exception as e:
        error_msg = f"应用程序启动失败: {e}"
        error_details = traceback.format_exc()
        
        print(f"错误: {error_msg}")
        print(f"详细信息:\n{error_details}")
        
        # 显示错误对话框
        try:
            app = QApplication(sys.argv) if 'app' not in locals() else app
            show_error_dialog(
                "应用程序错误",
                "应用程序启动时发生未知错误。",
                error_details
            )
        except:
            pass
        
        return 1


if __name__ == "__main__":
    sys.exit(main())
