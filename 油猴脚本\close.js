
// ==UserScript==
// @name         自动关闭弹窗
// @namespace    http://tampermonkey.net/
// @version      7.0
// @description  强制注入到页面的自动关闭脚本
// @match        https://madouqu9.xyz/*
// @match        https://madouqu*.xyz/*
// @match        https://*madouqu*/*
// @grant        unsafeWindow
// @run-at       document-body
// ==/UserScript==

(function() {
    'use strict';

    // 定义多个关闭按钮选择器，按优先级排序
    const CLOSE_SELECTORS = [
        '.swal2-close',           // SweetAlert2 关闭按钮
        '.modal-close',           // 通用模态框关闭按钮
        '.close-btn',             // 通用关闭按钮
        '[aria-label="Close"]',   // 带有关闭标签的按钮
        '.popup-close',           // 弹窗关闭按钮
        '.dialog-close'           // 对话框关闭按钮
    ];

    // 改进的关闭按钮点击函数，支持多选择器和错误处理
    const tryClickCloseButton = () => {
        try {
            for (const selector of CLOSE_SELECTORS) {
                const closeButton = document.querySelector(selector);
                if (closeButton) {
                    closeButton.click();
                    return true; // 成功点击，返回true
                }
            }
            return false; // 未找到关闭按钮
        } catch (error) {
            console.warn('[AutoClose] Error clicking close button:', error);
            return false; // 发生错误，返回false
        }
    };

    // 防抖定时器变量
    let debounceTimer = null;

    // 优化的MutationObserver，添加防抖和错误处理
    const observer = new MutationObserver((mutations) => {
        try {
            // 检查是否有新节点添加，使用some()提升性能
            const hasNewNodes = mutations.some(mutation => mutation.addedNodes.length > 0);

            if (hasNewNodes) {
                // 清除之前的定时器，实现防抖
                clearTimeout(debounceTimer);

                // 设置新的定时器，100ms后执行
                debounceTimer = setTimeout(() => {
                    tryClickCloseButton();
                }, 100);
            }
        } catch (error) {
            console.warn('[AutoClose] Observer error:', error);
        }
    });

    const config = {
        childList: true,
        subtree: true
    };

    // 在文档加载后立即执行关闭操作，并开始监控DOM变化
    document.addEventListener('DOMContentLoaded', () => {
        tryClickCloseButton();
        observer.observe(document.body, config);
    });
})();