ffmpeg -i input.mp4


Duration: 00:00:10.50, start: 0.000000, bitrate: 484 kb/s

Duration: 00:00:10.50：这表示视频的总时长是10.5秒。
start: 0.000000：这表示视频流的开始时间是从0秒开始的，即视频没有延迟开始。
bitrate: 484 kb/s：这是整个媒体文件的比特率，即文件在播放时的平均数据传输速率是484千比特每秒。



Stream #0:1[0x2](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 576x1024 [SAR 1:1 DAR 9:16], 411 kb/s, 30 fps, 30 tbr, 15360 tbn (default)

Stream #0:1[0x2](und)：这表示这是媒体文件中的第二个流（第一个流是0:0），通常第一个视频流是0:0，第二个视频流是0:1。[0x2] 是流的标识符，und 表示该流的语言是未定义的。
Video: h264 (High) (avc1 / 0x31637661)：这表示视频流的编码格式是H.264，并且是高质量编码（High profile）。avc1 是H.264编码的FourCC代码，0x31637661 是十六进制表示的FourCC代码。
yuv420p(tv, bt709, progressive)：这表示视频的颜色空间是YUV 4:2:0平面格式，tv 表示它是针对电视播放优化的，bt709 是色彩空间的标准，progressive 表示视频是逐行扫描的，而不是隔行扫描。
576x1024 [SAR 1:1 DAR 9:16]：这表示视频的分辨率是576像素宽和1024像素高。SAR（Sample Aspect Ratio）是采样宽高比，这里为1:1，表示每个像素是正方形的。DAR（Display Aspect Ratio）是显示宽高比，这里为9:16，表示视频的显示比例。
411 kb/s：这是视频流的比特率，即视频部分在播放时的平均数据传输速率是411千比特每秒。
30 fps：这表示视频的帧率是每秒30帧。
30 tbr：这是推荐播放速率，与帧率相同，也是每秒30帧。
15360 tbn (default)：这是时间戳的单位，表示1秒被分成15360份，用于计算时间戳。default 表示这是默认的时间戳单位。