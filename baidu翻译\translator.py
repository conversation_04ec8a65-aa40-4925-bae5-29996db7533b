# -*- coding: utf-8 -*-
"""
百度翻译API模块
"""

import random
import requests
import hashlib
from config import BAIDU_API, LANGUAGES

class BaiduTranslator:
    """百度翻译API封装类"""
    
    def __init__(self):
        """初始化翻译器"""
        self.appid = BAIDU_API['appid']
        self.appkey = BAIDU_API['appkey']
        self.endpoint = BAIDU_API['endpoint']
        self.path = BAIDU_API['path']
        self.url = self.endpoint + self.path
        # 禁用SSL警告
        from requests.packages.urllib3.exceptions import InsecureRequestWarning
        requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
        
    def make_md5(self, s, encoding='utf-8'):
        """生成MD5哈希值"""
        return hashlib.md5(s.encode(encoding)).hexdigest()
    
    def detect_language(self, text):
        """检测文本语言
        
        返回检测到的语言代码，如果检测失败则返回'en'
        """
        # 简单的语言检测逻辑
        # 如果包含中文字符，则认为是中文
        if any('\u4e00' <= char <= '\u9fff' for char in text):
            return 'zh'
        # 否则默认为英文
        return 'en'
    
    def translate(self, text, from_lang='auto', to_lang='zh'):
        """翻译文本"""
        print("\n=== 翻译开始 ===")
        print(f"原文长度: {len(text)} 字符")
        print(f"原文内容:\n{text}")
        
        # 如果源语言为自动检测，则进行语言检测
        if from_lang == 'auto':
            from_lang = self.detect_language(text)
            
        # 如果源语言和目标语言相同，则直接返回原文
        if from_lang == to_lang:
            return {
                'success': True,
                'text': text,
                'from_lang': from_lang,
                'to_lang': to_lang,
                'error': None
            }
            
        # 将长文本分段，每段不超过2000个字符，同时保留格式
        segments = []
        segment_formats = []  # 存储每个段落的格式信息
        
        # 按行分割文本，保留原始换行符
        lines = text.splitlines(keepends=True)
        print(f"\n总行数: {len(lines)}")
        
        current_segment = ""
        current_format = []  # 存储当前段落的格式信息
        
        for line in lines:
            # 分析当前行的格式
            leading_spaces = len(line) - len(line.lstrip())
            trailing_spaces = len(line) - len(line.rstrip())
            has_newline = line.endswith('\n')
            
            def save_current_segment():
                """保存当前段落"""
                nonlocal current_segment, current_format
                if current_segment:
                    segments.append(current_segment)
                    segment_formats.append(current_format)
                    current_segment = ""
                    current_format = []

            # 处理当前行
            line_content = line.strip()
            
            # 处理连续空行
            if not line_content:
                save_current_segment()
                segments.append(line)
                segment_formats.append([(leading_spaces, trailing_spaces, has_newline)])
                continue

            def is_list_item(text):
                """检查是否是列表项"""
                if not text:
                    return False
                # 检查常见的列表项格式
                if text.startswith(('-', '•', '*', '>', '+')):
                    return True
                # 检查 "word:" 格式
                if ':' in text[:20]:  # 限制在前20个字符内查找冒号
                    before_colon = text.split(':', 1)[0].strip()
                    if before_colon and before_colon.replace(' ', '').isalpha():
                        return True
                # 检查数字列表项
                if text[0].isdigit() and text[1:].lstrip().startswith(('.', ')')):
                    return True
                return False

            # 检查是否需要开始新段落
            start_new_paragraph = False
            if current_segment:
                # 如果当前行是列表项
                if is_list_item(line_content):
                    start_new_paragraph = True
                # 如果当前行以大写字母开头且前一段以句号结束
                elif line_content[0].isupper() and current_segment.rstrip().endswith('.'):
                    start_new_paragraph = True
                # 如果有明显的段落标记（如冒号结尾）
                elif current_segment.rstrip().endswith(':'):
                    start_new_paragraph = True
                # 如果当前行的缩进与前一行显著不同
                elif abs(leading_spaces - (len(current_segment) - len(current_segment.lstrip()))) > 2:
                    start_new_paragraph = True
                # 如果当前行看起来是独立的句子（不以小写字母开头）
                elif not line_content[0].islower():
                    start_new_paragraph = True

            # 如果需要开始新段落或当前段落加上新行会超出限制
            if start_new_paragraph or (current_segment and len(current_segment) + len(line) > 2000):
                if current_segment.strip():  # 只保存非空段落
                    save_current_segment()

            # 处理长行
            if len(line) > 2000:
                # 保存当前段落
                if current_segment:
                    save_current_segment()
                
                # 分割长行
                remaining_line = line
                while remaining_line:
                    # 查找合适的分割点
                    split_pos = min(2000, len(remaining_line))
                    if split_pos == 2000:
                        # 从后向前查找分割点，优先在标点符号处分割
                        for i in range(split_pos-1, max(split_pos-200, 0), -1):
                            if remaining_line[i] in '.,!?;:':
                                split_pos = i + 1
                                break
                            elif remaining_line[i].isspace():
                                split_pos = i + 1
                                break
                    
                    segment = remaining_line[:split_pos].rstrip()
                    if segment:  # 只保存非空段落
                        segments.append(segment + ('\n' if has_newline and not remaining_line[split_pos:] else ''))
                        segment_formats.append([(leading_spaces, trailing_spaces, bool(remaining_line) or has_newline)])
                    remaining_line = remaining_line[split_pos:].lstrip()
            else:
                # 处理普通行
                if not current_segment:
                    current_segment = line
                    current_format = [(leading_spaces, trailing_spaces, has_newline)]
                else:
                    current_segment += line
                    current_format.append((leading_spaces, trailing_spaces, has_newline))
        
        # 添加最后一个段落
        if current_segment:
            print(f"\n最后段落长度: {len(current_segment)}")
            print(f"最后段落内容:\n{current_segment}")
            segments.append(current_segment)
            segment_formats.append(current_format)
        
        print(f"\n总段落数: {len(segments)}")
        
        translated_segments = []
        
        # 翻译每个段落
        for i, segment in enumerate(segments):
            print(f"\n翻译段落 {i+1}/{len(segments)}")
            print(f"段落长度: {len(segment)}")
            print(f"段落内容:\n{segment}")
            
            # 处理段落
            segment_content = segment.strip()
            
            # 检查是否需要翻译
            needs_translation = True
            
            # 检查段落内容
            if segment_content:
                # 检查是否是列表项
                is_list = is_list_item(segment_content)
                
                # 检查内容是否只包含空白字符或标点符号
                only_spaces_and_punct = all(not c.isalnum() and not '\u4e00' <= c <= '\u9fff' for c in segment_content)
                
                # 如果是列表项，即使只包含标点符号也需要翻译
                # 如果不是列表项，且只包含空白字符或标点符号，则不需要翻译
                if not is_list and only_spaces_and_punct:
                    needs_translation = False
            else:
                needs_translation = False
                
            if not needs_translation:
                print("段落无需翻译，直接保留")
                translated_segments.append(segment)
                continue
            
            # 生成随机数和签名
            salt = random.randint(32768, 65536)
            sign = self.make_md5(self.appid + segment + str(salt) + self.appkey)
            
            # 构建请求参数
            payload = {
                'appid': self.appid,
                'q': segment,
                'from': from_lang,
                'to': to_lang,
                'salt': salt,
                'sign': sign
            }
            headers = {'Content-Type': 'application/x-www-form-urlencoded'}
            
            try:
                print(f"发送翻译请求: URL={self.url}")
                
                # 发送请求
                response = requests.post(self.url, headers=headers, data=payload, verify=False)
                print(f"响应状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                
                result = response.json()
                
                # 检查是否有错误
                if 'error_code' in result:
                    error_msg = f"翻译失败 - 错误代码: {result['error_code']}, 错误信息: {result.get('error_msg', '未知错误')}"
                    print(error_msg)
                    return {
                        'success': False,
                        'text': None,
                        'from_lang': from_lang,
                        'to_lang': to_lang,
                        'error': error_msg
                    }
                    
                # 提取翻译结果并应用格式
                translated_text = result['trans_result'][0]['dst']
                
                # 应用原始格式
                lines = translated_text.splitlines()
                formatted_lines = []
                format_info = segment_formats[i]
                
                # 确保有足够的格式信息
                for j, line in enumerate(lines):
                    if j < len(format_info):
                        leading_spaces, trailing_spaces, has_newline = format_info[j]
                        # 应用缩进
                        formatted_line = ' ' * leading_spaces + line.strip() + ' ' * trailing_spaces
                        # 应用换行符
                        if has_newline:
                            formatted_line += '\n'
                        formatted_lines.append(formatted_line)
                    else:
                        # 对于额外的行，使用默认格式
                        formatted_lines.append(line + '\n')
                
                formatted_text = ''.join(formatted_lines)
                translated_segments.append(formatted_text)
                print(f"翻译成功: {formatted_text}")
                
            except requests.RequestException as e:
                error_msg = f"网络请求错误: {str(e)}"
                print(error_msg)
                return {
                    'success': False,
                    'text': None,
                    'from_lang': from_lang,
                    'to_lang': to_lang,
                    'error': error_msg
                }
            except (KeyError, ValueError, IndexError) as e:
                error_msg = f"解析响应错误: {str(e)}"
                print(error_msg)
                return {
                    'success': False,
                    'text': None,
                    'from_lang': from_lang,
                    'to_lang': to_lang,
                    'error': error_msg
                }
        
        # 合并所有翻译结果
        final_text = ''.join(translated_segments)
        print("\n=== 翻译完成 ===")
        print(f"翻译结果长度: {len(final_text)} 字符")
        print(f"翻译结果:\n{final_text}")
        
        return {
            'success': True,
            'text': final_text,
            'from_lang': from_lang,
            'to_lang': to_lang,
            'error': None
        }
            
    def get_supported_languages(self):
        """获取支持的语言列表"""
        return LANGUAGES
