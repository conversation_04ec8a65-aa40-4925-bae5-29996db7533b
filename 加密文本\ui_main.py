import customtkinter as ctk
from ui_texts import TEXTS
from ui_widgets import create_button, create_input_section, create_output_section, create_control_section, create_status_bar
from ui_logic import process_text, handle_result, copy_result, paste_text, clear_input_and_output, clear_all, toggle_theme
import sys
import traceback
from jm import encrypt, decrypt

class ModernCryptoApp:
    def __init__(self):
        self.transparent_buttons = []
        self.setup_window()
        self.create_menu()
        self.create_widgets()
        self.setup_shortcuts()
        self.is_key_hidden = True
        # 其余初始化

    def setup_window(self):
        self.root = ctk.CTk()
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        self.root.title(TEXTS['title'])
        w, h = 1200, 800
        x = (self.root.winfo_screenwidth() - w) // 2
        y = (self.root.winfo_screenheight() - h) // 2
        self.root.geometry(f'{w}x{h}+{x}+{y}')
        self.root.minsize(1000, 700)
        try:
            self.root.iconbitmap("icon.ico")
        except: pass

    def create_menu(self):
        pass  # 可按需补充

    def create_widgets(self):
        """创建所有UI组件"""
        # 主容器
        main_container = ctk.CTkFrame(self.root, fg_color="transparent")
        main_container.pack(fill="both", expand=True)

        # 创建输入区域
        self.input_section = create_input_section(self, main_container)

        # 创建控制按钮区域
        self.control_section = create_control_section(self, main_container)

        # 创建输出区域
        self.output_section = create_output_section(self, main_container)

        # 创建状态栏
        self.status_section = create_status_bar(self, main_container)

    def setup_shortcuts(self):
        """设置键盘快捷键"""
        shortcuts = {
            '<Control-e>': lambda _: process_text(self, 'encrypt'),      # 加密
            '<Control-d>': lambda _: process_text(self, 'decrypt'),      # 解密
            '<Control-c>': lambda _: copy_result(self),                  # 复制结果
            '<Control-v>': lambda _: paste_text(self),                   # 粘贴文本
            '<Control-l>': lambda _: clear_input_and_output(self),       # 清空输入和输出
            '<Control-Shift-L>': lambda _: clear_all(self),              # 全部清空
            '<F1>': lambda _: toggle_theme(self)                         # 切换主题
        }

        for key, command in shortcuts.items():
            self.root.bind(key, command)

    def run(self):
        self.root.mainloop()

def main():
    ModernCryptoApp().run()

if __name__ == "__main__":
    main() 