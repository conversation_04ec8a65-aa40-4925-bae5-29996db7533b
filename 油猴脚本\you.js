// ==UserScript==
// @name         拼接360结果网址
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  添加一个按钮，用户点击后提取域名和网站路径并拼接
// <AUTHOR>
// @match        *://quake.360.net/quake/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 常量定义
    const CONFIG = {
        MAX_RESULTS: 10,
        BUTTON_ID: 'extractButton',
        SELECTORS: {
            IP_ELEMENTS: '.ip span, .ip .copy_btn',
            CONTAINER: '[class*="item-container"]',
            LABELS: '[class*="label"], .label',
            PATH_SPAN: '.ellipse-text',
            COPY_BTN: '[data-clipboard-text]'
        },
        COLORS: {
            PRIMARY: '#007BFF',
            SUCCESS: '#28a745'
        }
    };

    function extractAndCombine() {
        const results = [];
        const ipElements = document.querySelectorAll(CONFIG.SELECTORS.IP_ELEMENTS);

        for (let i = 0; i < Math.min(ipElements.length, CONFIG.MAX_RESULTS); i++) {
            const ipEl = ipElements[i];

            try {
                const domain = getDomain(ipEl);
                const path = getPath(ipEl);

                if (domain && path) {
                    const cleanDomain = domain.split(':')[0].trim();
                    const normalizedPath = path.startsWith('/') ? path : `/${path}`;
                    results.push(`https://${cleanDomain}${normalizedPath}`);
                }
            } catch (error) {
                console.error(`处理第${i + 1}项时出错:`, error);
            }
        }

        handleResults(results);
    }

    function getDomain(ipEl) {
        return ipEl.getAttribute('data-clipboard-text')?.trim() || ipEl.textContent?.trim();
    }

    function getPath(ipEl) {
        const container = ipEl.closest(CONFIG.SELECTORS.CONTAINER);
        if (!container) return null;

        const pathLabel = Array.from(container.querySelectorAll(CONFIG.SELECTORS.LABELS))
            .find(el => el.textContent.includes('网站路径'));

        if (!pathLabel) return null;

        const pathContainer = pathLabel.closest('[class*="item"], .item');
        if (!pathContainer) return null;

        const pathSpan = pathContainer.querySelector(CONFIG.SELECTORS.PATH_SPAN);
        if (pathSpan) return pathSpan.textContent.trim();

        const copyBtn = pathContainer.querySelector(CONFIG.SELECTORS.COPY_BTN);
        return copyBtn?.getAttribute('data-clipboard-text')?.trim() || null;
    }

    function handleResults(results) {
        if (results.length === 0) {
            console.log("未找到任何有效链接");
            return;
        }

        const resultText = results.join('\n');
        console.log('最终结果:', resultText);

        navigator.clipboard.writeText(resultText)
            .then(() => updateButtonStatus(results.length))
            .catch(err => console.error('复制到剪贴板失败:', err));
    }

    function updateButtonStatus(count) {
        const button = document.getElementById(CONFIG.BUTTON_ID);
        if (!button) return;

        const originalText = button.innerText;
        button.innerText = `已复制 ${count} 个链接`;
        button.style.backgroundColor = CONFIG.COLORS.SUCCESS;

        setTimeout(() => {
            button.innerText = originalText;
            button.style.backgroundColor = CONFIG.COLORS.PRIMARY;
        }, 2000);
    }

    function addButton() {
        if (document.getElementById(CONFIG.BUTTON_ID)) return;

        const button = document.createElement("button");
        Object.assign(button, {
            id: CONFIG.BUTTON_ID,
            innerText: "提取并拼接信息"
        });

        Object.assign(button.style, {
            position: "fixed",
            top: "6px",
            right: "265px",
            padding: "10px",
            backgroundColor: CONFIG.COLORS.PRIMARY,
            color: "#fff",
            border: "none",
            borderRadius: "5px",
            cursor: "pointer",
            zIndex: "2147483647"
        });

        button.addEventListener("click", extractAndCombine);
        document.body.appendChild(button);
    }

    function init() {
        addButton();

        // 监听DOM变化以确保按钮始终存在
        const observer = new MutationObserver(addButton);
        observer.observe(document.body, { childList: true, subtree: true });
    }

    // 初始化
    document.readyState === 'loading'
        ? document.addEventListener('DOMContentLoaded', init)
        : init();

})();