#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频转GIF工具
支持拖放视频文件，自定义参数，实时进度显示
使用现代化界面设计
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import os
import threading
import queue
import subprocess
import json
import time
import sys
import re
import tempfile
import shutil

from datetime import datetime
from pathlib import Path

# 尝试导入拖放功能
try:
    from tkinterdnd2 import DND_FILES, TkinterDnD
    DRAG_DROP_AVAILABLE = True
except ImportError:
    DRAG_DROP_AVAILABLE = False
    print("警告: 未安装tkinterdnd2，拖放功能不可用")


class VideoToGifConverter:
    def __init__(self, root):
        self.root = root
        self.root.title("🎬 视频转GIF工具")
        self.root.minsize(800, 600)
        
        # 设置深色主题
        self.style = ttk.Style("darkly")
        
        # 变量初始化
        self.video_path_var = tk.StringVar()
        self.output_folder_var = tk.StringVar()
        self.status_var = tk.StringVar(value="准备就绪")
        self.is_converting = False

        # 视频信息
        self.video_info = None
        self.original_width = 0
        self.original_height = 0

        # 转换参数
        self.scale_ratio_var = tk.StringVar(value="原始大小")  # 缩放比例
        self.fps_var = tk.StringVar(value="15")
        self.quality_var = tk.StringVar(value="medium")
        self.start_time_var = tk.StringVar(value="0")
        self.duration_var = tk.StringVar(value="")
        
        # 事件队列
        self.event_queue = queue.Queue()
        
        # 设置默认输出目录（初始为空，将根据视频文件动态设置）
        self.output_folder_var.set("")
        
        self.setup_ui()
        self.center_window(800, 600)
        
        # 启动事件处理
        self.root.after(100, self.process_events)
    
    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        main_container = ttk.Frame(self.root, padding="20")
        main_container.pack(fill=BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_container,
            text="🎬 视频转GIF工具",
            font=("Microsoft YaHei UI", 18, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))
        
        # 文件选择区域
        self.create_file_section(main_container)
        
        # 参数设置区域
        self.create_params_section(main_container)
        
        # 控制按钮区域
        self.create_control_section(main_container)
        
        # 进度显示区域
        self.create_progress_section(main_container)
        
        # 状态栏
        self.create_status_bar(main_container)
    
    def create_file_section(self, parent):
        """创建文件选择区域"""
        file_frame = ttk.LabelFrame(parent, text="📁 视频文件", padding="15")
        file_frame.pack(fill=X, pady=(0, 15))
        
        # 拖放区域
        self.drop_frame = ttk.Frame(file_frame, height=100)
        self.drop_frame.pack(fill=X, pady=(0, 10))
        self.drop_frame.pack_propagate(False)
        
        # 拖放标签
        self.drop_label = ttk.Label(
            self.drop_frame,
            text="🎥 拖放视频文件到此处\n或点击下方按钮选择文件",
            font=("Microsoft YaHei UI", 12),
            bootstyle="info",
            anchor=CENTER
        )
        self.drop_label.pack(expand=True, fill=BOTH)
        
        # 如果支持拖放，设置拖放功能
        if DRAG_DROP_AVAILABLE:
            self.drop_frame.drop_target_register(DND_FILES)
            self.drop_frame.dnd_bind('<<Drop>>', self.on_drop)
        
        # 文件路径显示和选择按钮
        path_frame = ttk.Frame(file_frame)
        path_frame.pack(fill=X)
        
        ttk.Label(path_frame, text="当前文件:").pack(side=LEFT)
        
        self.path_entry = ttk.Entry(
            path_frame,
            textvariable=self.video_path_var,
            state="readonly",
            width=50
        )
        self.path_entry.pack(side=LEFT, fill=X, expand=True, padx=(5, 5))
        
        ttk.Button(
            path_frame,
            text="选择文件",
            command=self.select_video_file,
            bootstyle="outline-primary"
        ).pack(side=RIGHT)
        
        # 输出文件夹
        output_frame = ttk.Frame(file_frame)
        output_frame.pack(fill=X, pady=(10, 0))

        ttk.Label(output_frame, text="输出目录: (默认为视频所在目录)").pack(side=LEFT)
        
        self.output_entry = ttk.Entry(
            output_frame,
            textvariable=self.output_folder_var,
            width=50
        )
        self.output_entry.pack(side=LEFT, fill=X, expand=True, padx=(5, 5))
        
        ttk.Button(
            output_frame,
            text="选择目录",
            command=self.select_output_folder,
            bootstyle="outline-secondary"
        ).pack(side=RIGHT)
    
    def create_params_section(self, parent):
        """创建参数设置区域"""
        params_frame = ttk.LabelFrame(parent, text="⚙️ 转换参数", padding="15")
        params_frame.pack(fill=X, pady=(0, 15))

        # 视频信息显示
        info_frame = ttk.Frame(params_frame)
        info_frame.pack(fill=X, pady=(0, 10))

        ttk.Label(info_frame, text="原始分辨率:").pack(side=LEFT)
        self.resolution_label = ttk.Label(
            info_frame,
            text="请先选择视频文件",
            bootstyle="secondary"
        )
        self.resolution_label.pack(side=LEFT, padx=(10, 0))

        # 第一行：缩放比例设置
        scale_frame = ttk.Frame(params_frame)
        scale_frame.pack(fill=X, pady=(0, 10))

        ttk.Label(scale_frame, text="缩放比例:").pack(side=LEFT)

        scale_combo = ttk.Combobox(
            scale_frame,
            textvariable=self.scale_ratio_var,
            values=["原始大小", "1/2", "1/3", "1/4", "自定义"],
            state="readonly",
            width=12
        )
        scale_combo.pack(side=LEFT, padx=(10, 20))
        scale_combo.bind('<<ComboboxSelected>>', self.on_scale_change)

        # 显示计算后的尺寸
        ttk.Label(scale_frame, text="输出尺寸:").pack(side=LEFT)
        self.output_size_label = ttk.Label(
            scale_frame,
            text="--",
            bootstyle="info"
        )
        self.output_size_label.pack(side=LEFT, padx=(10, 0))

        # 自定义尺寸输入框（初始隐藏）
        self.custom_size_frame = ttk.Frame(params_frame)

        ttk.Label(self.custom_size_frame, text="自定义尺寸:").pack(side=LEFT)
        ttk.Label(self.custom_size_frame, text="宽度").pack(side=LEFT, padx=(20, 5))
        self.custom_width_var = tk.StringVar()
        self.custom_width_entry = ttk.Entry(
            self.custom_size_frame,
            textvariable=self.custom_width_var,
            width=8
        )
        self.custom_width_entry.pack(side=LEFT, padx=(0, 10))
        self.custom_width_entry.bind('<KeyRelease>', self.on_custom_size_change)

        ttk.Label(self.custom_size_frame, text="高度").pack(side=LEFT, padx=(0, 5))
        self.custom_height_var = tk.StringVar()
        self.custom_height_entry = ttk.Entry(
            self.custom_size_frame,
            textvariable=self.custom_height_var,
            width=8
        )
        self.custom_height_entry.pack(side=LEFT, padx=(0, 10))
        self.custom_height_entry.bind('<KeyRelease>', self.on_custom_size_change)
        
        # 第二行：帧率和质量
        quality_frame = ttk.Frame(params_frame)
        quality_frame.pack(fill=X, pady=(0, 10))
        
        ttk.Label(quality_frame, text="帧率(FPS):").pack(side=LEFT)
        fps_entry = ttk.Entry(quality_frame, textvariable=self.fps_var, width=8)
        fps_entry.pack(side=LEFT, padx=(5, 20))
        
        ttk.Label(quality_frame, text="质量:").pack(side=LEFT)
        quality_combo = ttk.Combobox(
            quality_frame,
            textvariable=self.quality_var,
            values=["low", "medium", "high"],
            state="readonly",
            width=10
        )
        quality_combo.pack(side=LEFT, padx=(5, 0))
        
        # 第三行：时间范围
        time_frame = ttk.Frame(params_frame)
        time_frame.pack(fill=X)
        
        ttk.Label(time_frame, text="开始时间(秒):").pack(side=LEFT)
        start_entry = ttk.Entry(time_frame, textvariable=self.start_time_var, width=8)
        start_entry.pack(side=LEFT, padx=(5, 20))
        
        ttk.Label(time_frame, text="持续时间(秒):").pack(side=LEFT)
        duration_entry = ttk.Entry(time_frame, textvariable=self.duration_var, width=8)
        duration_entry.pack(side=LEFT, padx=(5, 10))
        
        ttk.Label(time_frame, text="(留空表示到结尾)", bootstyle="secondary").pack(side=LEFT)
    
    def create_control_section(self, parent):
        """创建控制按钮区域"""
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=X, pady=(0, 15))
        
        # 开始转换按钮
        self.convert_button = ttk.Button(
            control_frame,
            text="🚀 开始转换",
            command=self.start_conversion,
            bootstyle="success",
            width=15
        )
        self.convert_button.pack(side=LEFT, padx=(0, 10))
        
        # 取消按钮
        self.cancel_button = ttk.Button(
            control_frame,
            text="❌ 取消",
            command=self.cancel_conversion,
            bootstyle="danger",
            width=15,
            state=DISABLED
        )
        self.cancel_button.pack(side=LEFT, padx=(0, 10))
        
        # 打开输出文件夹按钮
        ttk.Button(
            control_frame,
            text="📂 打开输出文件夹",
            command=self.open_output_folder,
            bootstyle="outline-primary",
            width=15
        ).pack(side=RIGHT)
    
    def create_progress_section(self, parent):
        """创建进度显示区域"""
        progress_frame = ttk.LabelFrame(parent, text="📊 转换进度", padding="15")
        progress_frame.pack(fill=X, pady=(0, 15))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            mode='determinate',
            bootstyle="success-striped"
        )
        self.progress_bar.pack(fill=X, pady=(0, 10))
        
        # 进度信息
        self.progress_info = ttk.Label(
            progress_frame,
            text="等待开始...",
            font=("Microsoft YaHei UI", 10)
        )
        self.progress_info.pack()
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=X, side=BOTTOM)
        
        self.status_label = ttk.Label(
            status_frame,
            textvariable=self.status_var,
            bootstyle="secondary"
        )
        self.status_label.pack(side=LEFT)
        
        # 时间显示
        self.time_label = ttk.Label(
            status_frame,
            text=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            bootstyle="secondary"
        )
        self.time_label.pack(side=RIGHT)
        
        # 更新时间
        self.update_time()
    
    def center_window(self, width, height):
        """窗口居中显示"""
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
    
    def get_video_info(self, video_path):
        """获取视频信息"""
        try:
            # 检查FFmpeg工具是否可用
            tools_available, ffmpeg_cmd, ffprobe_cmd = check_ffmpeg_tools()
            if not tools_available or not ffprobe_cmd:
                return False

            cmd = [
                ffprobe_cmd,
                '-v', 'error',
                '-select_streams', 'v:0',
                '-show_entries', 'stream=width,height,duration',
                '-of', 'json',
                video_path
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',
                timeout=10
            )

            if result.returncode == 0:
                import json
                data = json.loads(result.stdout)
                if 'streams' in data and len(data['streams']) > 0:
                    stream = data['streams'][0]
                    self.original_width = int(stream.get('width', 0))
                    self.original_height = int(stream.get('height', 0))
                    self.video_info = stream
                    return True
            return False
        except Exception as e:
            return False

    def update_video_info_display(self):
        """更新视频信息显示"""
        if self.original_width > 0 and self.original_height > 0:
            self.resolution_label.config(
                text=f"{self.original_width} x {self.original_height}"
            )
            self.update_output_size_display()
        else:
            self.resolution_label.config(text="无法获取分辨率")
            self.output_size_label.config(text="--")

    def on_scale_change(self, event=None):
        """缩放比例改变时的处理"""
        scale_ratio = self.scale_ratio_var.get()

        if scale_ratio == "自定义":
            self.custom_size_frame.pack(fill=X, pady=(5, 10))
            # 设置默认自定义值为1/2大小
            if self.original_width > 0:
                self.custom_width_var.set(str(self.original_width // 2))
                self.custom_height_var.set(str(self.original_height // 2))
        else:
            self.custom_size_frame.pack_forget()

        self.update_output_size_display()

    def on_custom_size_change(self, event=None):
        """自定义尺寸改变时的处理"""
        if self.scale_ratio_var.get() == "自定义":
            self.update_output_size_display()

    def get_output_size(self):
        """获取输出尺寸"""
        if self.original_width <= 0 or self.original_height <= 0:
            return 480, 270, False  # 默认尺寸，需要缩放

        scale_ratio = self.scale_ratio_var.get()

        if scale_ratio == "原始大小":
            return self.original_width, self.original_height, False  # 原始大小，不需要缩放
        elif scale_ratio == "1/2":
            return self.original_width // 2, self.original_height // 2, True
        elif scale_ratio == "1/3":
            return self.original_width // 3, self.original_height // 3, True
        elif scale_ratio == "1/4":
            return self.original_width // 4, self.original_height // 4, True
        elif scale_ratio == "自定义":
            try:
                width = int(self.custom_width_var.get() or "0")
                height = int(self.custom_height_var.get() or "0")
                if width > 0 and height > 0:
                    return width, height, True
            except ValueError:
                pass
            return self.original_width // 2, self.original_height // 2, True

        return self.original_width // 2, self.original_height // 2, True

    def update_output_size_display(self):
        """更新输出尺寸显示"""
        width, height, needs_scaling = self.get_output_size()
        self.output_size_label.config(text=f"{width} x {height}")
    
    def on_drop(self, event):
        """处理拖放事件"""
        files = self.root.tk.splitlist(event.data)
        if files:
            file_path = files[0]
            if self.is_video_file(file_path):
                self.video_path_var.set(file_path)
                self.status_var.set(f"已选择视频: {os.path.basename(file_path)}")
                self.update_drop_label()
                self.update_output_folder(file_path)
                self.load_video_info(file_path)
            else:
                messagebox.showwarning("警告", "请选择有效的视频文件！")
    
    def is_video_file(self, file_path):
        """检查是否为视频文件"""
        video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v'}
        return Path(file_path).suffix.lower() in video_extensions
    
    def select_video_file(self):
        """选择视频文件"""
        file_path = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.webm *.m4v"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.video_path_var.set(file_path)
            self.status_var.set(f"已选择视频: {os.path.basename(file_path)}")
            self.update_drop_label()
            self.update_output_folder(file_path)
            self.load_video_info(file_path)
    
    def select_output_folder(self):
        """选择输出文件夹"""
        folder_path = filedialog.askdirectory(title="选择输出文件夹")
        if folder_path:
            self.output_folder_var.set(folder_path)
    
    def update_drop_label(self):
        """更新拖放区域显示"""
        if self.video_path_var.get():
            filename = os.path.basename(self.video_path_var.get())
            self.drop_label.config(text=f"✅ 已选择文件:\n{filename}")
        else:
            self.drop_label.config(text="🎥 拖放视频文件到此处\n或点击下方按钮选择文件")

    def update_output_folder(self, video_path):
        """根据视频文件路径更新输出文件夹"""
        if video_path and os.path.exists(video_path):
            # 获取视频文件所在目录
            video_dir = os.path.dirname(video_path)
            # 如果当前输出目录为空或者是默认目录，则更新为视频所在目录
            current_output = self.output_folder_var.get()
            if not current_output or not os.path.exists(current_output):
                self.output_folder_var.set(video_dir)
                self.status_var.set(f"输出目录已设置为: {video_dir}")

    def load_video_info(self, video_path):
        """加载视频信息"""
        self.status_var.set("正在获取视频信息...")

        # 在新线程中获取视频信息，避免界面卡顿
        def get_info():
            if self.get_video_info(video_path):
                # 在主线程中更新界面
                self.root.after(0, self.update_video_info_display)
                self.root.after(0, lambda: self.status_var.set("视频信息获取成功"))
            else:
                self.root.after(0, lambda: self.status_var.set("无法获取视频信息"))
                self.root.after(0, lambda: self.resolution_label.config(text="无法获取分辨率"))

        threading.Thread(target=get_info, daemon=True).start()

    def start_conversion(self):
        """开始转换"""
        if not self.video_path_var.get():
            messagebox.showwarning("警告", "请先选择视频文件！")
            return

        if not os.path.exists(self.video_path_var.get()):
            messagebox.showerror("错误", "视频文件不存在！")
            return

        # 验证参数
        try:
            width, height, needs_scaling = self.get_output_size()
            fps = float(self.fps_var.get())
            start_time = float(self.start_time_var.get())

            if width <= 0 or height <= 0 or fps <= 0 or start_time < 0:
                raise ValueError("参数值必须大于0")

        except ValueError as e:
            messagebox.showerror("错误", f"参数设置错误: {str(e)}")
            return

        # 确定输出目录
        output_dir = self.output_folder_var.get()
        if not output_dir:
            # 如果没有设置输出目录，使用视频文件所在目录
            output_dir = os.path.dirname(self.video_path_var.get())
            self.output_folder_var.set(output_dir)

        # 创建输出目录（如果不存在）
        os.makedirs(output_dir, exist_ok=True)

        # 设置UI状态
        self.is_converting = True
        self.convert_button.config(state=DISABLED)
        self.cancel_button.config(state=NORMAL)
        self.progress_var.set(0)
        self.progress_info.config(text="正在准备转换...")
        self.status_var.set("转换中...")

        # 在新线程中执行转换
        threading.Thread(target=self.convert_video_to_gif, daemon=True).start()

    def cancel_conversion(self):
        """取消转换"""
        self.is_converting = False
        self.convert_button.config(state=NORMAL)
        self.cancel_button.config(state=DISABLED)
        self.progress_var.set(0)
        self.progress_info.config(text="转换已取消")
        self.status_var.set("已取消")

    def convert_video_to_gif(self):
        """视频转GIF的核心逻辑"""
        try:
            video_path = self.video_path_var.get()
            output_dir = self.output_folder_var.get()

            # 检查输入文件
            if not video_path:
                raise Exception("未选择视频文件")

            if not os.path.exists(video_path):
                raise Exception(f"输入文件不存在: {video_path}")

            # 检查文件是否可读
            try:
                with open(video_path, 'rb') as f:
                    f.read(1)
            except Exception as e:
                raise Exception(f"文件无法读取: {e}")

            # 检查输出目录
            if not output_dir:
                output_dir = os.path.dirname(video_path)
                self.output_folder_var.set(output_dir)

            # 确保输出目录存在
            try:
                os.makedirs(output_dir, exist_ok=True)
            except Exception as e:
                raise Exception(f"无法创建输出目录 {output_dir}: {str(e)}")

            # 生成输出文件名（避免特殊字符）
            video_name = Path(video_path).stem
            safe_name = re.sub(r'[^\w\s-]', '', video_name)
            safe_name = re.sub(r'[-\s]+', '_', safe_name)
            safe_name = safe_name.strip('_')

            if not safe_name or len(safe_name) < 3:
                safe_name = "video_output"

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"{safe_name}_{timestamp}.gif"

            # 确保输出路径安全
            safe_output_dir = output_dir
            if any(ord(c) > 127 or c in '，？！""''（）【】' for c in output_dir):
                safe_output_dir = os.path.expanduser("~/Desktop")
                if not os.path.exists(safe_output_dir):
                    safe_output_dir = os.path.expanduser("~")

            output_path = os.path.join(safe_output_dir, output_filename)

            # 获取转换参数
            width, height, needs_scaling = self.get_output_size()
            fps = float(self.fps_var.get())
            start_time = float(self.start_time_var.get())
            duration = self.duration_var.get().strip()
            quality = self.quality_var.get()

            # 更新进度
            self.event_queue.put({
                'type': 'progress',
                'value': 10,
                'message': '正在分析视频信息...'
            })

            # 检查FFmpeg工具可用性
            tools_available, ffmpeg_cmd, ffprobe_cmd = check_ffmpeg_tools()
            if not tools_available:
                raise Exception("FFmpeg或FFprobe不可用，请确保已正确安装FFmpeg并添加到PATH环境变量")

            # 构建ffmpeg命令
            video_path_abs = os.path.abspath(video_path)
            output_path_abs = os.path.abspath(output_path)

            # 检查路径是否包含特殊字符，如果有则复制到临时位置
            temp_input_path = None
            if any(ord(c) > 127 or c in '，？！""''（）【】' for c in video_path_abs):
                temp_dir = tempfile.mkdtemp()
                original_ext = Path(video_path).suffix
                if not original_ext:
                    original_ext = '.mp4'
                elif not original_ext.startswith('.'):
                    original_ext = '.' + original_ext
                temp_filename = f"temp_video_{timestamp}{original_ext}"
                temp_input_path = os.path.join(temp_dir, temp_filename)

                try:
                    shutil.copy2(video_path_abs, temp_input_path)
                    video_path_abs = temp_input_path
                except Exception as e:
                    temp_input_path = None

            # 基础命令 - 使用检测到的FFmpeg命令
            base_cmd = [ffmpeg_cmd, '-y']  # -y 表示覆盖已存在的文件

            # 输入文件和开始时间
            if start_time > 0:
                base_cmd.extend(['-ss', str(start_time)])
            base_cmd.extend(['-i', video_path_abs])

            # 如果指定了持续时间
            if duration:
                try:
                    duration_val = float(duration)
                    if duration_val > 0:
                        base_cmd.extend(['-t', str(duration_val)])
                except ValueError:
                    pass

            # 优化质量参数以提高速度
            if quality == "low":
                colors = 64  # 减少颜色数量提高速度
                scale_flags = "fast_bilinear"
            elif quality == "high":
                colors = 192  # 适度减少以平衡质量和速度
                scale_flags = "bilinear"  # 使用更快的算法
            else:  # medium
                colors = 128  # 减少默认颜色数量
                scale_flags = "fast_bilinear"

            self.event_queue.put({
                'type': 'progress',
                'value': 30,
                'message': '正在生成调色板...'
            })

            # 使用两步法生成高质量GIF
            # 第一步：生成调色板
            palette_path = os.path.join(safe_output_dir, f"temp_palette_{timestamp}.png")
            palette_path_abs = os.path.abspath(palette_path)

            palette_cmd = base_cmd.copy()

            # 根据是否需要缩放来构建视频滤镜
            if needs_scaling:
                vf_filter = f'fps={fps},scale={width}:{height}:flags={scale_flags}:force_original_aspect_ratio=decrease,palettegen=max_colors={colors}'
            else:
                vf_filter = f'fps={fps},palettegen=max_colors={colors}'

            palette_cmd.extend(['-vf', vf_filter, palette_path_abs])

            # 执行调色板生成
            try:
                process1 = subprocess.run(
                    palette_cmd,
                    capture_output=True,
                    text=False,
                    timeout=45,  # 减少超时时间
                    cwd=safe_output_dir
                )

                if process1.returncode != 0:
                    error_msg = f"调色板生成失败 (返回码: {process1.returncode})"
                    if process1.stderr:
                        stderr_text = process1.stderr.decode('utf-8', errors='ignore')
                        error_msg += f"\nFFmpeg错误信息: {stderr_text[:200]}"
                    raise Exception(error_msg)

                if not os.path.exists(palette_path):
                    raise Exception(f"调色板文件未生成: {palette_path}")

            except subprocess.TimeoutExpired:
                raise Exception("调色板生成超时")

            self.event_queue.put({
                'type': 'progress',
                'value': 60,
                'message': '正在应用调色板生成GIF...'
            })

            # 第二步：使用调色板生成GIF
            gif_cmd = base_cmd.copy()
            gif_cmd.extend(['-i', palette_path_abs])

            # 根据是否需要缩放来构建复合滤镜，优化抖动算法提高速度
            if needs_scaling:
                lavfi_filter = f'fps={fps},scale={width}:{height}:flags={scale_flags}:force_original_aspect_ratio=decrease[x];[x][1:v]paletteuse=dither=floyd_steinberg'
            else:
                lavfi_filter = f'fps={fps}[x];[x][1:v]paletteuse=dither=floyd_steinberg'

            gif_cmd.extend(['-lavfi', lavfi_filter, output_path_abs])

            # 更新进度
            self.event_queue.put({
                'type': 'progress',
                'value': 70,
                'message': '正在生成GIF...'
            })

            # 执行GIF生成
            try:
                process2 = subprocess.run(
                    gif_cmd,
                    capture_output=True,
                    text=False,
                    timeout=90,  # 减少超时时间
                    cwd=safe_output_dir
                )

                # 简化等待逻辑
                time.sleep(1)

                # 检查转换结果
                if process2.returncode == 0:
                    # 简化文件检查逻辑
                    max_wait = 5  # 减少等待时间
                    file_found = False

                    for i in range(max_wait):
                        if os.path.exists(output_path):
                            try:
                                file_size = os.path.getsize(output_path)
                                if file_size > 1000:  # 文件有合理大小
                                    file_found = True
                                    break
                            except OSError:
                                pass
                        time.sleep(1)

                    # 最终检查和处理
                    if file_found and os.path.exists(output_path):
                        try:
                            file_size = os.path.getsize(output_path)
                            if file_size > 0:
                                size_mb = file_size / (1024 * 1024)

                                # 清理临时文件
                                try:
                                    if os.path.exists(palette_path):
                                        os.remove(palette_path)
                                except:
                                    pass

                                # 发送成功事件
                                self.event_queue.put({
                                    'type': 'progress',
                                    'value': 100,
                                    'message': '🎉 转换完成！'
                                })

                                self.event_queue.put({
                                    'type': 'success',
                                    'output_path': output_path,
                                    'message': f'🎉 视频转GIF转换成功！\n\n📄 输出文件: {output_filename}\n📊 文件大小: {size_mb:.2f} MB\n📁 保存位置: {safe_output_dir}'
                                })
                            else:
                                try:
                                    if os.path.exists(palette_path):
                                        os.remove(palette_path)
                                except:
                                    pass
                                raise Exception(f"生成的GIF文件为空")
                        except OSError as e:
                            try:
                                if os.path.exists(palette_path):
                                    os.remove(palette_path)
                            except:
                                pass
                            raise Exception(f"无法访问生成的文件: {e}")
                    else:
                        try:
                            if os.path.exists(palette_path):
                                os.remove(palette_path)
                        except:
                            pass
                        raise Exception(f"GIF文件未生成: {output_path}")
                else:
                    # 清理临时文件
                    try:
                        if os.path.exists(palette_path):
                            os.remove(palette_path)
                    except:
                        pass

                    # 提供错误信息
                    error_msg = f"FFmpeg转换失败 (返回码: {process2.returncode})"
                    if process2.stderr:
                        try:
                            stderr_text = process2.stderr.decode('utf-8', errors='ignore')
                            error_msg += f"\nFFmpeg错误信息: {stderr_text[:200]}"
                        except:
                            pass
                    raise Exception(error_msg)

            except subprocess.TimeoutExpired:
                try:
                    if os.path.exists(palette_path):
                        os.remove(palette_path)
                except:
                    pass
                raise Exception("GIF生成超时")
            except Exception as e:
                try:
                    if os.path.exists(palette_path):
                        os.remove(palette_path)
                except:
                    pass
                raise e

        except Exception as e:
            self.event_queue.put({
                'type': 'error',
                'message': f'转换失败: {str(e)}'
            })
        finally:
            self.is_converting = False

            # 清理临时文件
            try:
                if 'temp_input_path' in locals() and temp_input_path and os.path.exists(temp_input_path):
                    shutil.rmtree(os.path.dirname(temp_input_path), ignore_errors=True)
            except:
                pass

            # 确保UI状态恢复
            try:
                self.event_queue.put({
                    'type': 'cleanup',
                    'message': '清理转换状态'
                })
            except:
                self.root.after(0, self._force_ui_cleanup)

    def _force_ui_cleanup(self):
        """强制清理UI状态（在主线程中执行）"""
        try:
            self.convert_button.config(state=tk.NORMAL)
            self.cancel_button.config(state=tk.DISABLED)
            self.progress_var.set(0)
            self.progress_info.config(text="等待开始...")
            self.status_var.set("准备就绪")
            self.is_converting = False
        except:
            pass

    def open_output_folder(self):
        """打开输出文件夹"""
        output_dir = self.output_folder_var.get()
        if os.path.exists(output_dir):
            if os.name == 'nt':  # Windows
                os.startfile(output_dir)
            elif os.name == 'posix':  # macOS and Linux
                subprocess.run(['open' if sys.platform == 'darwin' else 'xdg-open', output_dir])
        else:
            messagebox.showwarning("警告", "输出文件夹不存在！")

    def process_events(self):
        """处理事件队列"""
        try:
            while True:
                event = self.event_queue.get_nowait()

                if event['type'] == 'progress':
                    self.progress_var.set(event['value'])
                    self.progress_info.config(text=event['message'])

                elif event['type'] == 'success':
                    self.progress_var.set(100)
                    self.progress_info.config(text="转换完成！")
                    self.convert_button.config(state=NORMAL)
                    self.cancel_button.config(state=DISABLED)
                    self.status_var.set("转换完成")

                    # 确保界面更新
                    self.root.update()

                    # 播放系统提示音（如果可用）
                    try:
                        import winsound
                        winsound.MessageBeep(winsound.MB_OK)
                    except:
                        pass

                    # 显示成功消息
                    messagebox.showinfo("🎉 转换成功", event['message'])

                    # 询问是否打开输出文件夹
                    if messagebox.askyesno("📂 打开文件夹", "转换完成！是否打开输出文件夹查看GIF文件？"):
                        self.open_output_folder()

                elif event['type'] == 'error':
                    self.progress_var.set(0)
                    self.progress_info.config(text="转换失败")
                    self.convert_button.config(state=NORMAL)
                    self.cancel_button.config(state=DISABLED)
                    self.status_var.set("转换失败")

                    messagebox.showerror("错误", event['message'])

                elif event['type'] == 'cleanup':
                    # 处理清理事件，确保UI状态恢复
                    print("🔧 处理清理事件")
                    self.convert_button.config(state=NORMAL)
                    self.cancel_button.config(state=DISABLED)
                    if self.progress_var.get() < 100:  # 如果不是成功完成，重置进度
                        self.progress_var.set(0)
                        self.progress_info.config(text="等待开始...")
                        self.status_var.set("准备就绪")

        except queue.Empty:
            pass

        # 继续处理事件
        self.root.after(100, self.process_events)


def check_ffmpeg_tools():
    """检查FFmpeg和FFprobe是否可用"""
    try:
        # 尝试常见的FFmpeg命令
        ffmpeg_commands = ['ffmpeg', 'ffmpeg.exe']
        ffprobe_commands = ['ffprobe', 'ffprobe.exe']

        ffmpeg_cmd = None
        ffprobe_cmd = None

        # 检查FFmpeg
        for cmd in ffmpeg_commands:
            try:
                result = subprocess.run([cmd, '-version'],
                                      capture_output=True,
                                      timeout=3)
                if result.returncode == 0:
                    ffmpeg_cmd = cmd
                    break
            except (subprocess.SubprocessError, FileNotFoundError, subprocess.TimeoutExpired):
                continue

        # 检查FFprobe
        for cmd in ffprobe_commands:
            try:
                result = subprocess.run([cmd, '-version'],
                                      capture_output=True,
                                      timeout=3)
                if result.returncode == 0:
                    ffprobe_cmd = cmd
                    break
            except (subprocess.SubprocessError, FileNotFoundError, subprocess.TimeoutExpired):
                continue

        # 如果直接调用失败，尝试常见路径
        if not ffmpeg_cmd or not ffprobe_cmd:
            common_paths = [
                r'C:\ffmpeg\bin',
                r'D:\ffmpeg\bin',
                r'C:\Program Files\ffmpeg\bin',
                r'C:\Program Files (x86)\ffmpeg\bin',
            ]

            for base_path in common_paths:
                if not ffmpeg_cmd:
                    ffmpeg_path = os.path.join(base_path, 'ffmpeg.exe')
                    if os.path.exists(ffmpeg_path):
                        try:
                            result = subprocess.run([ffmpeg_path, '-version'],
                                                  capture_output=True,
                                                  timeout=3)
                            if result.returncode == 0:
                                ffmpeg_cmd = ffmpeg_path
                        except:
                            pass

                if not ffprobe_cmd:
                    ffprobe_path = os.path.join(base_path, 'ffprobe.exe')
                    if os.path.exists(ffprobe_path):
                        try:
                            result = subprocess.run([ffprobe_path, '-version'],
                                                  capture_output=True,
                                                  timeout=3)
                            if result.returncode == 0:
                                ffprobe_cmd = ffprobe_path
                        except:
                            pass

        return (ffmpeg_cmd is not None and ffprobe_cmd is not None), ffmpeg_cmd, ffprobe_cmd
    except:
        return False, None, None

def check_ffmpeg():
    """保持向后兼容的FFmpeg检查函数"""
    available, ffmpeg_cmd, _ = check_ffmpeg_tools()
    return available, ffmpeg_cmd

def show_ffmpeg_help():
    """显示FFmpeg安装帮助"""
    help_window = tk.Toplevel()
    help_window.title("FFmpeg 安装指南")
    help_window.geometry("500x400")
    help_window.resizable(False, False)

    # 居中显示
    help_window.transient()
    help_window.grab_set()

    main_frame = ttk.Frame(help_window, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)

    # 标题
    title_label = ttk.Label(
        main_frame,
        text="🔧 FFmpeg 安装指南",
        font=("Microsoft YaHei UI", 14, "bold"),
        bootstyle="danger"
    )
    title_label.pack(pady=(0, 15))

    # 说明文本
    help_text = """FFmpeg 是视频处理的核心工具，程序需要它来转换视频。

📥 安装方法：

Windows 用户：
1. 访问 https://ffmpeg.org/download.html
2. 下载 Windows 版本
3. 解压到任意文件夹（如 C:\\ffmpeg）
4. 将 bin 文件夹添加到系统 PATH 环境变量
5. 重启命令行或程序

或者使用包管理器：
• Chocolatey: choco install ffmpeg
• Scoop: scoop install ffmpeg

验证安装：
在命令行运行 'ffmpeg -version' 应该显示版本信息。"""

    text_widget = tk.Text(
        main_frame,
        wrap=tk.WORD,
        height=15,
        width=60,
        font=("Microsoft YaHei UI", 10)
    )
    text_widget.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
    text_widget.insert(tk.END, help_text)
    text_widget.config(state=tk.DISABLED)

    # 按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X)

    def open_download():
        import webbrowser
        webbrowser.open("https://ffmpeg.org/download.html")

    ttk.Button(
        button_frame,
        text="🌐 打开下载页面",
        command=open_download,
        bootstyle="primary"
    ).pack(side=tk.LEFT)

    ttk.Button(
        button_frame,
        text="❌ 关闭",
        command=help_window.destroy,
        bootstyle="secondary"
    ).pack(side=tk.RIGHT)

def main():
    """主函数"""
    # 检查FFmpeg工具
    tools_available, ffmpeg_cmd, ffprobe_cmd = check_ffmpeg_tools()

    if not tools_available:
        # 创建一个临时窗口来显示错误
        temp_root = tk.Tk()
        temp_root.withdraw()  # 隐藏主窗口

        # 显示错误对话框，提供更多选项
        missing_tools = []
        if not ffmpeg_cmd:
            missing_tools.append("FFmpeg")
        if not ffprobe_cmd:
            missing_tools.append("FFprobe")

        missing_str = " 和 ".join(missing_tools)

        result = messagebox.askyesnocancel(
            "FFmpeg 工具检测",
            f"未能检测到{missing_str}！\n\n"
            "程序需要FFmpeg和FFprobe来处理视频文件。\n\n"
            "• 点击'是'查看安装指南\n"
            "• 点击'否'强制启动程序（如果确定已安装）\n"
            "• 点击'取消'退出程序",
            icon="warning"
        )

        if result is True:  # 是 - 查看安装指南
            show_ffmpeg_help()
            temp_root.mainloop()  # 等待帮助窗口关闭
            temp_root.destroy()
            return
        elif result is False:  # 否 - 强制启动
            print("⚠️ 用户选择强制启动，跳过FFmpeg检查")
            temp_root.destroy()
            # 继续启动程序
        else:  # 取消 - 退出
            temp_root.destroy()
            return

    # 创建主窗口
    try:
        if DRAG_DROP_AVAILABLE:
            root = TkinterDnD.Tk()
        else:
            root = ttk.Window(themename="darkly")

        # 创建应用
        VideoToGifConverter(root)

        # 运行主循环
        root.mainloop()

    except Exception as e:
        messagebox.showerror("启动错误", f"程序启动失败：\n{str(e)}")
        print(f"❌ 程序启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
