import time
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def get_ollama_hosts(url, max_retries=3):
    # 创建 Chrome 浏览器实例
    options = webdriver.ChromeOptions()
    options.add_argument('--headless')  # 无头模式，不显示浏览器窗口
    driver = webdriver.Chrome(options=options)
    
    try:
        # 访问页面
        driver.get(url)
        
        # 等待元素加载（最多等待20秒）
        hosts = []
        wait = WebDriverWait(driver, 20)
        elements = wait.until(
            EC.presence_of_all_elements_located((By.CLASS_NAME, "hsxa-host"))
        )
        
        # 提取链接
        for element in elements:
            links = element.find_elements(By.TAG_NAME, "a")
            for link in links:
                href = link.get_attribute('href')
                if href:
                    hosts.append(href)
                    print(f"找到链接: {href}")
        
        return hosts
        
    except Exception as e:
        print(f"发生错误: {e}")
        return []
        
    finally:
        # 关闭浏览器
        driver.quit()

if __name__ == "__main__":
    url = "https://fofa.info/result?qbase64=YXBwPSJPbGxhbWEiICYmIGNvdW50cnk9IkNOIiAmJiBpc19kb21haW49ZmFsc2UgJiYgcmVnaW9uPSJCZWlqaW5nIg%3D%3D"
    hosts = get_ollama_hosts(url)
    
    if hosts:
        print("\n找到的主机列表：")
        for host in hosts:
            print(host)
    else:
        print("未能获取到任何主机信息")