# -*- coding: utf-8 -*-
"""
百度翻译应用配置文件
"""

# 百度翻译API配置
BAIDU_API = {
    'appid': '20230725001756908',
    'appkey': 'kGPsNQ3t6gJJHVtCb9Kl',
    'endpoint': 'http://api.fanyi.baidu.com',
    'path': '/api/trans/vip/translate'
}

# 支持的语言
LANGUAGES = {
    'auto': '自动检测',
    'zh': '中文',
    'en': '英语',
    'yue': '粤语',
    'wyw': '文言文',
    'jp': '日语',
    'kor': '韩语',
    'fra': '法语',
    'spa': '西班牙语',
    'th': '泰语',
    'ara': '阿拉伯语',
    'ru': '俄语',
    'pt': '葡萄牙语',
    'de': '德语',
    'it': '意大利语',
    'el': '希腊语',
    'nl': '荷兰语',
    'pl': '波兰语',
    'bul': '保加利亚语',
    'est': '爱沙尼亚语',
    'dan': '丹麦语',
    'fin': '芬兰语',
    'cs': '捷克语',
    'rom': '罗马尼亚语',
    'slo': '斯洛文尼亚语',
    'swe': '瑞典语',
    'hu': '匈牙利语',
    'vie': '越南语',
}

# UI配置
UI = {
    'title': '百度翻译',
    'width': 900,
    'height': 600,
    'font': ('微软雅黑', 10),
    'text_font': ('微软雅黑', 11),
    'button_font': ('微软雅黑', 10),
    'padding': 10,
    'bg': '#f5f5f5',
    'fg': '#333333',
    'button_bg': '#e1e1e1',
    'button_fg': '#333333',
    'text_bg': '#ffffff',
    'text_fg': '#333333',
    'border': '#cccccc',
    'highlight_bg': '#4a86e8',
    'highlight_fg': '#ffffff',
}

# 历史记录最大数量
MAX_HISTORY = 50
