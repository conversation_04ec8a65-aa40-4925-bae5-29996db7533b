<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块 - 现代版</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            overflow: hidden;
            position: relative;
        }

        /* 背景动画效果 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            animation: backgroundMove 20s ease-in-out infinite;
        }

        @keyframes backgroundMove {
            0%, 100% { transform: translateX(0) translateY(0); }
            33% { transform: translateX(-20px) translateY(-20px); }
            66% { transform: translateX(20px) translateY(-10px); }
        }

        .game-container {
            display: grid;
            grid-template-columns: minmax(280px, 1fr) auto minmax(280px, 1fr);
            gap: 50px;
            align-items: start;
            background: rgba(255, 255, 255, 0.05);
            padding: 50px;
            padding-top: 80px;  /* 为标题留出空间 */
            border-radius: 30px;
            backdrop-filter: blur(20px);
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
            max-width: 1400px;
            width: 95%;
            margin: 100px auto 20px;  /* 增加顶部边距 */
        }

        .left-panel, .right-panel {
            display: flex;
            flex-direction: column;
            gap: 30px;
            min-width: 280px;
            height: 100%;
            justify-content: center;
        }

        .game-board {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 25px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 20px;
            box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.3);
        }

        #gameCanvas {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            background: linear-gradient(145deg, #0a0a0a, #1a1a1a);
            box-shadow: 
                0 0 30px rgba(0, 255, 255, 0.3),
                inset 0 0 20px rgba(0, 0, 0, 0.5);
            transition: all 0.3s ease;
        }

        #gameCanvas:hover {
            box-shadow: 
                0 0 40px rgba(0, 255, 255, 0.5),
                inset 0 0 20px rgba(0, 0, 0, 0.5);
        }

        .info-panel {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .info-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .info-panel:hover::before {
            left: 100%;
        }

        .info-panel:hover {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .info-panel h3 {
            font-family: 'Orbitron', monospace;
            font-size: 18px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 15px;
            color: #fff;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            letter-spacing: 1px;
        }

        .score-display {
            font-family: 'Orbitron', monospace;
            font-size: 36px;
            font-weight: 900;
            text-align: center;
            color: #00ff88;
            text-shadow: 
                0 0 20px rgba(0, 255, 136, 0.8),
                0 0 40px rgba(0, 255, 136, 0.4);
            animation: scoreGlow 3s ease-in-out infinite alternate;
            letter-spacing: 2px;
        }

        @keyframes scoreGlow {
            from { 
                text-shadow: 
                    0 0 20px rgba(0, 255, 136, 0.8),
                    0 0 40px rgba(0, 255, 136, 0.4);
            }
            to { 
                text-shadow: 
                    0 0 30px rgba(0, 255, 136, 1),
                    0 0 60px rgba(0, 255, 136, 0.6);
            }
        }

        .level-display {
            font-family: 'Orbitron', monospace;
            font-size: 28px;
            font-weight: 700;
            text-align: center;
            color: #ff6b35;
            text-shadow: 
                0 0 15px rgba(255, 107, 53, 0.8),
                0 0 30px rgba(255, 107, 53, 0.4);
            letter-spacing: 1px;
        }

        .lines-display {
            font-family: 'Orbitron', monospace;
            font-size: 24px;
            font-weight: 700;
            text-align: center;
            color: #4fc3f7;
            text-shadow: 
                0 0 15px rgba(79, 195, 247, 0.8),
                0 0 30px rgba(79, 195, 247, 0.4);
            letter-spacing: 1px;
        }

        #nextCanvas {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: linear-gradient(145deg, #0a0a0a, #1a1a1a);
            display: block;
            margin: 0 auto;
            box-shadow: 
                0 0 20px rgba(0, 255, 255, 0.2),
                inset 0 0 15px rgba(0, 0, 0, 0.5);
        }

        .controls {
            text-align: left;
            font-size: 15px;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            width: 100%;
        }

        .controls h3 {
            margin-bottom: 20px;
            text-align: center;
            grid-column: 1 / -1;  /* 标题占据整行 */
        }

        .control-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            transition: all 0.2s ease;
        }

        .control-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .control-label {
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        .key {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            padding: 6px 12px;
            border-radius: 8px;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            font-size: 12px;
            min-width: 60px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 
                0 4px 8px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .game-over, .pause-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(145deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 20, 0.95));
            color: white;
            padding: 50px;
            border-radius: 25px;
            text-align: center;
            display: none;
            z-index: 1000;
            border: 2px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.7),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            min-width: 400px;
        }

        .game-over h2, .pause-overlay h2 {
            font-family: 'Orbitron', monospace;
            margin-bottom: 25px;
            font-size: 36px;
            font-weight: 900;
            text-shadow: 0 0 20px currentColor;
            letter-spacing: 2px;
        }

        .game-over h2 {
            color: #ff4757;
            text-shadow: 0 0 20px rgba(255, 71, 87, 0.8);
        }

        .pause-overlay h2 {
            color: #ffa502;
            text-shadow: 0 0 20px rgba(255, 165, 2, 0.8);
        }

        .game-over p {
            font-family: 'Orbitron', monospace;
            font-size: 18px;
            margin-bottom: 15px;
            color: #ccc;
            font-weight: 500;
        }

        .pause-overlay p {
            font-size: 18px;
            margin-bottom: 25px;
            color: #ccc;
        }

        .restart-btn, .resume-btn {
            background: linear-gradient(145deg, #00d2ff, #3a7bd5);
            color: white;
            border: none;
            padding: 15px 35px;
            border-radius: 15px;
            cursor: pointer;
            font-family: 'Orbitron', monospace;
            font-size: 16px;
            font-weight: 700;
            transition: all 0.3s ease;
            box-shadow: 
                0 8px 16px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            margin: 0 10px;
            letter-spacing: 1px;
            text-transform: uppercase;
        }

        .restart-btn:hover, .resume-btn:hover {
            transform: translateY(-3px);
            box-shadow: 
                0 12px 24px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            background: linear-gradient(145deg, #00b8e6, #3a6bc5);
        }

        .restart-btn:active, .resume-btn:active {
            transform: translateY(-1px);
        }

        .status-indicator {
            position: absolute;
            top: -50px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 24px;
            border-radius: 25px;
            font-family: 'Orbitron', monospace;
            font-size: 14px;
            font-weight: 700;
            background: linear-gradient(145deg, #ff6b35, #f7931e);
            color: white;
            display: none;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            letter-spacing: 1px;
            animation: statusPulse 2s ease-in-out infinite;
        }

        @keyframes statusPulse {
            0%, 100% { transform: translateX(-50%) scale(1); }
            50% { transform: translateX(-50%) scale(1.05); }
        }

        .status-indicator.paused {
            display: block;
        }

        .particle {
            position: absolute;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            pointer-events: none;
            animation: particle-float 3s ease-out forwards;
        }

        @keyframes particle-float {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1) rotate(0deg);
            }
            100% {
                opacity: 0;
                transform: translateY(-120px) scale(0) rotate(360deg);
            }
        }

        .game-title {
            position: absolute;
            top: -40px;  /* 调整位置 */
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            padding: 15px 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            z-index: 2;  /* 确保标题在最上层 */
        }

        .game-title h1 {
            font-family: 'Orbitron', monospace;
            font-size: 32px;
            font-weight: 900;
            color: #fff;
            text-shadow: 
                0 0 20px rgba(0, 255, 255, 0.8),
                0 0 40px rgba(0, 255, 255, 0.4);
            letter-spacing: 3px;
            margin-bottom: 5px;
        }

        .game-title p {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 400;
        }

        /* 响应式设计优化 */
        @media (max-width: 1200px) {
            .game-container {
                grid-template-columns: 1fr auto 1fr;
                gap: 30px;
                padding: 40px;
                padding-top: 70px;
            }
            
            .controls {
                grid-template-columns: 1fr;  /* 在较小屏幕上改为单列 */
            }
        }

        @media (max-width: 1024px) {
            .game-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto;
                gap: 40px;
                padding: 40px;
                padding-top: 70px;
            }
            
            .left-panel, .right-panel {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                min-width: auto;
            }

            .controls {
                grid-template-columns: repeat(2, 1fr);  /* 恢复为两列 */
            }
        }

        @media (max-width: 768px) {
            .game-container {
                padding: 30px;
                padding-top: 60px;
                margin: 60px 15px 15px;
                width: auto;
            }

            .game-title {
                top: -30px;
                padding: 12px 30px;
                width: 90%;  /* 限制宽度 */
            }

            .controls {
                grid-template-columns: 1fr;  /* 在移动端使用单列 */
            }

            .control-item {
                padding: 6px;
            }

            .key {
                padding: 4px 8px;
                min-width: 50px;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-title">
            <h1>🎮 TETRIS</h1>
            <p>现代俄罗斯方块</p>
        </div>

        <div class="left-panel">
            <div class="info-panel">
                <h3>🏆 得分</h3>
                <div class="score-display" id="score">0</div>
            </div>
            
            <div class="info-panel">
                <h3>📈 等级</h3>
                <div class="level-display" id="level">1</div>
            </div>
            
            <div class="info-panel">
                <h3>📊 消除行数</h3>
                <div class="lines-display" id="lines">0</div>
            </div>
        </div>
        
        <div class="game-board">
            <canvas id="gameCanvas" width="300" height="600"></canvas>
            <div class="status-indicator" id="statusIndicator">⏸️ 游戏暂停</div>
            
            <div class="game-over" id="gameOver">
                <h2>🎮 游戏结束</h2>
                <p>最终得分: <span id="finalScore">0</span></p>
                <p>消除行数: <span id="finalLines">0</span></p>
                <button class="restart-btn" onclick="restartGame()">🔄 重新开始</button>
            </div>

            <div class="pause-overlay" id="pauseOverlay">
                <h2>⏸️ 游戏暂停</h2>
                <p>按空格键继续游戏</p>
                <button class="resume-btn" onclick="togglePause()">▶️ 继续游戏</button>
            </div>
        </div>
        
        <div class="right-panel">
            <div class="info-panel">
                <h3>🔮 下一个方块</h3>
                <canvas id="nextCanvas" width="120" height="120"></canvas>
            </div>
            
            <div class="info-panel">
                <div class="controls">
                    <h3>🎮 操作说明</h3>
                    <div class="control-item">
                        <span class="control-label">左移</span>
                        <span class="key">←</span>
                    </div>
                    <div class="control-item">
                        <span class="control-label">右移</span>
                        <span class="key">→</span>
                    </div>
                    <div class="control-item">
                        <span class="control-label">下降</span>
                        <span class="key">↓</span>
                    </div>
                    <div class="control-item">
                        <span class="control-label">旋转</span>
                        <span class="key">↑</span>
                    </div>
                    <div class="control-item">
                        <span class="control-label">暂停</span>
                        <span class="key">SPACE</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 游戏配置
        const BOARD_WIDTH = 10;
        const BOARD_HEIGHT = 20;
        const BLOCK_SIZE = 30;
        
        // 获取画布和上下文
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const nextCanvas = document.getElementById('nextCanvas');
        const nextCtx = nextCanvas.getContext('2d');
        
        // 游戏状态
        let board = [];
        let currentPiece = null;
        let nextPiece = null;
        let score = 0;
        let level = 1;
        let lines = 0;
        let gameRunning = true;
        let isPaused = false;
        let dropTime = 0;
        let dropInterval = 1000;
        
        // 方块形状定义 - 增强的颜色
        const PIECES = [
            // I 形状
            {
                shape: [
                    [1, 1, 1, 1]
                ],
                color: '#00f5ff',
                shadowColor: '#0080ff'
            },
            // O 形状
            {
                shape: [
                    [1, 1],
                    [1, 1]
                ],
                color: '#ffff00',
                shadowColor: '#ffcc00'
            },
            // T 形状
            {
                shape: [
                    [0, 1, 0],
                    [1, 1, 1]
                ],
                color: '#a000f0',
                shadowColor: '#7000b0'
            },
            // S 形状
            {
                shape: [
                    [0, 1, 1],
                    [1, 1, 0]
                ],
                color: '#00f000',
                shadowColor: '#00b000'
            },
            // Z 形状
            {
                shape: [
                    [1, 1, 0],
                    [0, 1, 1]
                ],
                color: '#f00000',
                shadowColor: '#b00000'
            },
            // J 形状
            {
                shape: [
                    [1, 0, 0],
                    [1, 1, 1]
                ],
                color: '#0080ff',
                shadowColor: '#0060cc'
            },
            // L 形状
            {
                shape: [
                    [0, 0, 1],
                    [1, 1, 1]
                ],
                color: '#ff8000',
                shadowColor: '#cc6000'
            }
        ];
        
        // 初始化游戏板
        function initBoard() {
            board = [];
            for (let y = 0; y < BOARD_HEIGHT; y++) {
                board[y] = [];
                for (let x = 0; x < BOARD_WIDTH; x++) {
                    board[y][x] = null;
                }
            }
        }
        
        // 创建新方块
        function createPiece() {
            const pieceType = PIECES[Math.floor(Math.random() * PIECES.length)];
            return {
                shape: pieceType.shape,
                color: pieceType.color,
                shadowColor: pieceType.shadowColor,
                x: Math.floor(BOARD_WIDTH / 2) - Math.floor(pieceType.shape[0].length / 2),
                y: 0
            };
        }
        
        // 绘制增强的方块
        function drawBlock(ctx, x, y, color, shadowColor) {
            const blockX = x * BLOCK_SIZE;
            const blockY = y * BLOCK_SIZE;
            
            // 创建渐变效果
            const gradient = ctx.createLinearGradient(blockX, blockY, blockX + BLOCK_SIZE, blockY + BLOCK_SIZE);
            gradient.addColorStop(0, color);
            gradient.addColorStop(0.5, color);
            gradient.addColorStop(1, shadowColor || color);
            
            // 绘制主体
            ctx.fillStyle = gradient;
            ctx.fillRect(blockX + 1, blockY + 1, BLOCK_SIZE - 2, BLOCK_SIZE - 2);
            
            // 绘制高光
            const highlightGradient = ctx.createLinearGradient(blockX, blockY, blockX + BLOCK_SIZE/2, blockY + BLOCK_SIZE/2);
            highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.6)');
            highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
            ctx.fillStyle = highlightGradient;
            ctx.fillRect(blockX + 2, blockY + 2, BLOCK_SIZE/2, BLOCK_SIZE/2);
            
            // 绘制边框
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.lineWidth = 1;
            ctx.strokeRect(blockX + 0.5, blockY + 0.5, BLOCK_SIZE - 1, BLOCK_SIZE - 1);
            
            // 内部边框
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
            ctx.strokeRect(blockX + 1.5, blockY + 1.5, BLOCK_SIZE - 3, BLOCK_SIZE - 3);
        }
        
        // 绘制游戏板
        function drawBoard() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制已放置的方块
            for (let y = 0; y < BOARD_HEIGHT; y++) {
                for (let x = 0; x < BOARD_WIDTH; x++) {
                    if (board[y][x]) {
                        drawBlock(ctx, x, y, board[y][x].color, board[y][x].shadowColor);
                    }
                }
            }
            
            // 绘制网格线
            ctx.strokeStyle = 'rgba(0, 255, 255, 0.1)';
            ctx.lineWidth = 0.5;
            for (let x = 0; x <= BOARD_WIDTH; x++) {
                ctx.beginPath();
                ctx.moveTo(x * BLOCK_SIZE, 0);
                ctx.lineTo(x * BLOCK_SIZE, canvas.height);
                ctx.stroke();
            }
            for (let y = 0; y <= BOARD_HEIGHT; y++) {
                ctx.beginPath();
                ctx.moveTo(0, y * BLOCK_SIZE);
                ctx.lineTo(canvas.width, y * BLOCK_SIZE);
                ctx.stroke();
            }
        }
        
        // 绘制当前方块
        function drawPiece(piece) {
            for (let y = 0; y < piece.shape.length; y++) {
                for (let x = 0; x < piece.shape[y].length; x++) {
                    if (piece.shape[y][x]) {
                        drawBlock(ctx, piece.x + x, piece.y + y, piece.color, piece.shadowColor);
                    }
                }
            }
        }
        
        // 绘制下一个方块
        function drawNextPiece() {
            nextCtx.clearRect(0, 0, nextCanvas.width, nextCanvas.height);
            
            if (nextPiece) {
                const offsetX = (nextCanvas.width / BLOCK_SIZE - nextPiece.shape[0].length) / 2;
                const offsetY = (nextCanvas.height / BLOCK_SIZE - nextPiece.shape.length) / 2;
                
                for (let y = 0; y < nextPiece.shape.length; y++) {
                    for (let x = 0; x < nextPiece.shape[y].length; x++) {
                        if (nextPiece.shape[y][x]) {
                            const blockX = (offsetX + x) * BLOCK_SIZE;
                            const blockY = (offsetY + y) * BLOCK_SIZE;
                            
                            // 创建渐变效果
                            const gradient = nextCtx.createLinearGradient(blockX, blockY, blockX + BLOCK_SIZE, blockY + BLOCK_SIZE);
                            gradient.addColorStop(0, nextPiece.color);
                            gradient.addColorStop(1, nextPiece.shadowColor || nextPiece.color);
                            
                            nextCtx.fillStyle = gradient;
                            nextCtx.fillRect(blockX + 1, blockY + 1, BLOCK_SIZE - 2, BLOCK_SIZE - 2);
                            
                            // 高光效果
                            const highlightGradient = nextCtx.createLinearGradient(blockX, blockY, blockX + BLOCK_SIZE/2, blockY + BLOCK_SIZE/2);
                            highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.6)');
                            highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
                            nextCtx.fillStyle = highlightGradient;
                            nextCtx.fillRect(blockX + 2, blockY + 2, BLOCK_SIZE/2, BLOCK_SIZE/2);
                            
                            nextCtx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
                            nextCtx.lineWidth = 1;
                            nextCtx.strokeRect(blockX + 0.5, blockY + 0.5, BLOCK_SIZE - 1, BLOCK_SIZE - 1);
                        }
                    }
                }
            }
        }
        
        // 检查碰撞
        function checkCollision(piece, dx = 0, dy = 0, rotation = null) {
            const shape = rotation || piece.shape;
            const newX = piece.x + dx;
            const newY = piece.y + dy;
            
            for (let y = 0; y < shape.length; y++) {
                for (let x = 0; x < shape[y].length; x++) {
                    if (shape[y][x]) {
                        const boardX = newX + x;
                        const boardY = newY + y;
                        
                        if (boardX < 0 || boardX >= BOARD_WIDTH || 
                            boardY >= BOARD_HEIGHT || 
                            (boardY >= 0 && board[boardY][boardX])) {
                            return true;
                        }
                    }
                }
            }
            return false;
        }
        
        // 旋转方块
        function rotatePiece(piece) {
            const rotated = [];
            const rows = piece.shape.length;
            const cols = piece.shape[0].length;
            
            for (let x = 0; x < cols; x++) {
                rotated[x] = [];
                for (let y = rows - 1; y >= 0; y--) {
                    rotated[x][rows - 1 - y] = piece.shape[y][x];
                }
            }
            
            return rotated;
        }
        
        // 放置方块
        function placePiece(piece) {
            for (let y = 0; y < piece.shape.length; y++) {
                for (let x = 0; x < piece.shape[y].length; x++) {
                    if (piece.shape[y][x]) {
                        const boardY = piece.y + y;
                        if (boardY >= 0) {
                            board[boardY][piece.x + x] = {
                                color: piece.color,
                                shadowColor: piece.shadowColor
                            };
                        }
                    }
                }
            }
        }
        
        // 创建增强的粒子效果
        function createParticles(x, y) {
            const colors = ['#ff6b35', '#f7931e', '#ffeb3b', '#4fc3f7', '#00ff88', '#ff4757'];
            for (let i = 0; i < 15; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = (x + Math.random() * 100 - 50) + 'px';
                particle.style.top = (y + Math.random() * 50) + 'px';
                particle.style.background = colors[Math.floor(Math.random() * colors.length)];
                particle.style.boxShadow = `0 0 10px ${particle.style.background}`;
                document.body.appendChild(particle);
                
                setTimeout(() => {
                    particle.remove();
                }, 3000);
            }
        }
        
        // 清除完整的行
        function clearLines() {
            let linesCleared = 0;
            const clearedRows = [];
            
            for (let y = BOARD_HEIGHT - 1; y >= 0; y--) {
                let isComplete = true;
                for (let x = 0; x < BOARD_WIDTH; x++) {
                    if (!board[y][x]) {
                        isComplete = false;
                        break;
                    }
                }
                
                if (isComplete) {
                    clearedRows.push(y);
                    board.splice(y, 1);
                    board.unshift(new Array(BOARD_WIDTH).fill(null));
                    linesCleared++;
                    y++; // 重新检查当前行
                }
            }
            
            if (linesCleared > 0) {
                // 创建粒子效果
                clearedRows.forEach(row => {
                    const rect = canvas.getBoundingClientRect();
                    createParticles(
                        rect.left + canvas.width / 2, 
                        rect.top + row * BLOCK_SIZE
                    );
                });
                
                lines += linesCleared;
                const lineBonus = [0, 100, 300, 500, 800][linesCleared] || 100;
                score += lineBonus * level;
                level = Math.floor(lines / 10) + 1;
                dropInterval = Math.max(50, 1000 - (level - 1) * 100);
                
                updateDisplay();
                
                // 添加得分动画效果
                animateScoreIncrease(lineBonus * level);
            }
        }
        
        // 得分增加动画
        function animateScoreIncrease(points) {
            const scoreElement = document.getElementById('score');
            const originalTransform = scoreElement.style.transform;
            
            scoreElement.style.transform = 'scale(1.2)';
            scoreElement.style.transition = 'transform 0.3s ease';
            
            setTimeout(() => {
                scoreElement.style.transform = originalTransform;
            }, 300);
        }
        
        // 更新显示
        function updateDisplay() {
            document.getElementById('score').textContent = score.toLocaleString();
            document.getElementById('level').textContent = level;
            document.getElementById('lines').textContent = lines;
        }
        
        // 检查游戏结束
        function checkGameOver() {
            return checkCollision(currentPiece);
        }
        
        // 游戏结束
        function gameOver() {
            gameRunning = false;
            document.getElementById('finalScore').textContent = score.toLocaleString();
            document.getElementById('finalLines').textContent = lines;
            document.getElementById('gameOver').style.display = 'block';
        }
        
        // 切换暂停状态
        function togglePause() {
            if (!gameRunning) return;
            
            isPaused = !isPaused;
            const statusIndicator = document.getElementById('statusIndicator');
            const pauseOverlay = document.getElementById('pauseOverlay');
            
            if (isPaused) {
                statusIndicator.classList.add('paused');
                pauseOverlay.style.display = 'block';
            } else {
                statusIndicator.classList.remove('paused');
                pauseOverlay.style.display = 'none';
            }
        }
        
        // 重新开始游戏
        function restartGame() {
            initBoard();
            score = 0;
            level = 1;
            lines = 0;
            dropInterval = 1000;
            gameRunning = true;
            isPaused = false;
            
            currentPiece = createPiece();
            nextPiece = createPiece();
            
            document.getElementById('gameOver').style.display = 'none';
            document.getElementById('pauseOverlay').style.display = 'none';
            document.getElementById('statusIndicator').classList.remove('paused');
            updateDisplay();
        }
        
        // 游戏主循环
        function gameLoop(timestamp) {
            if (!gameRunning) {
                requestAnimationFrame(gameLoop);
                return;
            }
            
            if (!isPaused && timestamp - dropTime > dropInterval) {
                if (!checkCollision(currentPiece, 0, 1)) {
                    currentPiece.y++;
                } else {
                    placePiece(currentPiece);
                    clearLines();
                    
                    currentPiece = nextPiece;
                    nextPiece = createPiece();
                    
                    if (checkGameOver()) {
                        gameOver();
                        return;
                    }
                }
                dropTime = timestamp;
            }
            
            drawBoard();
            if (currentPiece) {
                drawPiece(currentPiece);
            }
            drawNextPiece();
            
            requestAnimationFrame(gameLoop);
        }
        
        // 键盘事件处理
        document.addEventListener('keydown', (e) => {
            if (!gameRunning) return;
            
            // 暂停功能独立处理，不受isPaused状态影响
            if (e.key === ' ') {
                togglePause();
                e.preventDefault();
                return;
            }
            
            // 其他操作只在非暂停状态下生效
            if (isPaused) return;
            
            switch (e.key) {
                case 'ArrowLeft':
                    if (!checkCollision(currentPiece, -1, 0)) {
                        currentPiece.x--;
                    }
                    break;
                case 'ArrowRight':
                    if (!checkCollision(currentPiece, 1, 0)) {
                        currentPiece.x++;
                    }
                    break;
                case 'ArrowDown':
                    if (!checkCollision(currentPiece, 0, 1)) {
                        currentPiece.y++;
                        score += 1;
                        updateDisplay();
                    }
                    break;
                case 'ArrowUp':
                    const rotated = rotatePiece(currentPiece);
                    if (!checkCollision(currentPiece, 0, 0, rotated)) {
                        currentPiece.shape = rotated;
                    }
                    break;
            }
            e.preventDefault();
        });
        
        // 初始化游戏
        function initGame() {
            initBoard();
            currentPiece = createPiece();
            nextPiece = createPiece();
            updateDisplay();
            requestAnimationFrame(gameLoop);
        }
        
        // 启动游戏
        initGame();
    </script>
</body>
</html>
