import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import subprocess
import os
import threading
import time
import pygame
from PIL import Image, ImageTk
import json
import sys
import queue
from concurrent.futures import ThreadPoolExecutor

class AudioExtractor:
    def __init__(self, root):
        self.root = root
        self.root.title("音频提取工具")
        
        # 设置主题色
        self.colors = {
            'primary': '#2196F3',
            'secondary': '#FFC107',
            'background': '#F5F5F5',
            'text': '#212121',
            'success': '#4CAF50',
            'error': '#F44336'
        }
        
        # 变量初始化
        self.video_path = tk.StringVar()
        self.output_format = tk.StringVar(value='wav')
        self.audio_quality = tk.StringVar(value='192k')
        self.status = tk.StringVar(value='就绪')
        self.is_extracting = False
        self.is_playing = False
        self.current_audio = None
        self.audio_pos = 0  # 记录音频播放位置
        
        # 创建事件队列和线程池
        self.event_queue = queue.Queue()
        self.thread_pool = ThreadPoolExecutor(max_workers=2)
        
        # 音频预览初始化
        pygame.mixer.init()
        
        # 设置样式
        self.setup_styles()
        
        # 创建UI
        self.create_ui()
        
        # 设置窗口大小和位置
        self.center_window(800, 500)
        
        # 加载配置
        self.load_config()
        
        # 启动事件处理
        self.root.after(100, self.process_events)
        
        # 添加音频播放进度更新
        self.update_audio_progress()
        
    def setup_styles(self):
        style = ttk.Style()
        style.configure("Custom.TFrame", background=self.colors['background'])
        style.configure("Custom.TLabel",
                       background=self.colors['background'],
                       font=("Arial", 10))
        style.configure("Header.TLabel",
                       background=self.colors['background'],
                       font=("Arial", 12, "bold"))
        style.configure("Custom.Horizontal.TProgressbar",
                       background=self.colors['primary'],
                       troughcolor=self.colors['background'])

    def create_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, style="Custom.TFrame", padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 文件选择区域
        self.create_file_selection(main_frame)
        
        # 设置区域
        self.create_settings_panel(main_frame)
        
        # 控制和状态区域
        self.create_control_panel(main_frame)

    def create_file_selection(self, parent):
        file_frame = ttk.LabelFrame(parent, text="文件选择", padding="10")
        file_frame.pack(fill=tk.X, pady=(0, 20))

        # 视频文件选择
        video_frame = ttk.Frame(file_frame)
        video_frame.pack(fill=tk.X, pady=5)
        ttk.Label(video_frame, text="视频文件:", style="Custom.TLabel").pack(side=tk.LEFT)
        ttk.Entry(video_frame, textvariable=self.video_path, width=50).pack(side=tk.LEFT, padx=10)
        tk.Button(video_frame,
                 text="选择视频",
                 command=self.load_video,
                 bg=self.colors['primary'],
                 fg="white",
                 font=("Arial", 10)).pack(side=tk.LEFT)

        # 视频信息显示
        self.info_label = ttk.Label(file_frame,
                                  text="视频信息: 请选择视频文件",
                                  style="Custom.TLabel")
        self.info_label.pack(fill=tk.X, pady=5)

    def create_settings_panel(self, parent):
        settings_frame = ttk.LabelFrame(parent, text="提取设置", padding="10")
        settings_frame.pack(fill=tk.X, pady=(0, 20))

        # 输出格式选择
        format_frame = ttk.Frame(settings_frame)
        format_frame.pack(fill=tk.X, pady=5)
        ttk.Label(format_frame, text="输出格式:").pack(side=tk.LEFT)
        for fmt in ['wav', 'mp3', 'aac', 'flac']:
            ttk.Radiobutton(format_frame,
                          text=fmt.upper(),
                          variable=self.output_format,
                          value=fmt).pack(side=tk.LEFT, padx=10)

        # 音频质量选择
        quality_frame = ttk.Frame(settings_frame)
        quality_frame.pack(fill=tk.X, pady=5)
        ttk.Label(quality_frame, text="音频质量:").pack(side=tk.LEFT)
        quality_cb = ttk.Combobox(quality_frame,
                                textvariable=self.audio_quality,
                                values=['64k', '128k', '192k', '256k', '320k'],
                                width=10,
                                state='readonly')
        quality_cb.pack(side=tk.LEFT, padx=10)

    def create_control_panel(self, parent):
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, pady=(0, 20))

        # 提取按钮
        self.extract_button = tk.Button(control_frame,
                                     text="开始提取",
                                     command=self.start_extraction,
                                     bg=self.colors['success'],
                                     fg="white",
                                     font=("Arial", 10, "bold"))
        self.extract_button.pack(side=tk.LEFT, padx=5)

        # 预览按钮
        self.preview_button = tk.Button(control_frame,
                                     text="预览音频",
                                     command=self.toggle_preview,
                                     bg=self.colors['secondary'],
                                     fg="white",
                                     font=("Arial", 10))
        self.preview_button.pack(side=tk.LEFT, padx=5)
        self.preview_button.config(state=tk.DISABLED)

        # 音频进度条
        self.audio_progress_frame = ttk.Frame(control_frame)
        self.audio_progress_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=10)
        
        self.audio_progress_var = tk.DoubleVar(value=0)
        self.audio_progress = ttk.Scale(self.audio_progress_frame,
                                      from_=0,
                                      to=100,
                                      orient=tk.HORIZONTAL,
                                      variable=self.audio_progress_var,
                                      command=self.seek_audio)
        self.audio_progress.pack(side=tk.TOP, fill=tk.X)
        
        self.audio_time_label = ttk.Label(self.audio_progress_frame,
                                        text="00:00 / 00:00",
                                        style="Custom.TLabel")
        self.audio_time_label.pack(side=tk.TOP)

        # 提取进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame,
                                          mode='determinate',
                                          variable=self.progress_var,
                                          style="Custom.Horizontal.TProgressbar")
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=10)

        # 状态标签
        ttk.Label(control_frame,
                 textvariable=self.status,
                 style="Custom.TLabel").pack(side=tk.LEFT, padx=5)

    def process_events(self):
        """处理事件队列中的事件"""
        try:
            while True:  # 处理队列中的所有事件
                event = self.event_queue.get_nowait()
                event_type = event.get('type')
                
                if event_type == 'update_status':
                    self.status.set(event['message'])
                elif event_type == 'update_progress':
                    self.progress_var.set(event['progress'])
                elif event_type == 'update_info':
                    self.info_label.config(text=event['info'])
                elif event_type == 'extraction_complete':
                    self.handle_extraction_complete(event)
                elif event_type == 'error':
                    self.handle_error(event['message'])
                
                self.event_queue.task_done()
        except queue.Empty:
            pass
        finally:
            # 继续监听事件队列
            self.root.after(100, self.process_events)

    def load_video(self):
        video_path = filedialog.askopenfilename(
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mkv *.mov *.flv *.wmv"),
                ("所有文件", "*.*")
            ],
            title="选择视频文件"
        )
        if video_path:
            self.video_path.set(video_path)
            self.status.set("正在读取视频信息...")
            # 在后台线程中获取视频信息
            self.thread_pool.submit(self.get_video_info)
            self.save_config()

    def get_video_info(self):
        """在后台线程中获取视频信息"""
        try:
            # 检查文件是否存在
            if not os.path.exists(self.video_path.get()):
                raise FileNotFoundError("视频文件不存在")

            # 检查ffprobe命令是否可用
            try:
                subprocess.run(["ffprobe", "-version"], 
                             capture_output=True, 
                             check=True,
                             encoding='utf-8',
                             errors='replace')
            except (subprocess.SubprocessError, FileNotFoundError):
                raise RuntimeError("未找到ffprobe，请确保已安装FFmpeg")

            cmd = [
                "ffprobe",
                "-v", "error",
                "-select_streams", "a:0",
                "-show_entries", "stream=codec_name,channels,bit_rate:format=duration,size",
                "-print_format", "json",
                self.video_path.get()
            ]
            
            # 执行命令并检查结果
            result = subprocess.run(cmd, 
                                 capture_output=True, 
                                 text=True,
                                 encoding='utf-8',
                                 errors='replace')
            
            if result.returncode != 0:
                error_msg = result.stderr.strip()
                raise RuntimeError(f"ffprobe执行失败: {error_msg}")
            
            if not result.stdout.strip():
                raise RuntimeError("无法读取视频信息，ffprobe返回空结果")

            try:
                info = json.loads(result.stdout)
            except json.JSONDecodeError as e:
                raise RuntimeError(f"解析视频信息失败: {str(e)}\n输出内容: {result.stdout}")

            # 提取视频信息
            format_info = info.get('format', {})
            stream_info = info.get('streams', [{}])[0] if info.get('streams') else {}
            
            # 获取时长
            duration = float(format_info.get('duration', 0))
            
            # 获取文件大小
            try:
                size = float(format_info.get('size', 0)) / (1024*1024)  # 转换为MB
            except (ValueError, TypeError):
                size = os.path.getsize(self.video_path.get()) / (1024*1024)
            
            # 获取音频信息
            codec = stream_info.get('codec_name', 'unknown')
            channels = stream_info.get('channels', 0)
            
            # 获取比特率
            try:
                bitrate = int(stream_info.get('bit_rate', 0)) / 1000  # 转换为kbps
            except (ValueError, TypeError):
                bitrate = 0
            
            # 构建信息文本
            info_text = f"时长: {int(duration//60)}分{int(duration%60)}秒 | "
            info_text += f"大小: {size:.1f}MB | "
            info_text += f"音频编码: {codec.upper() if codec else '未知'} | "
            info_text += f"声道数: {channels} | "
            info_text += f"比特率: {bitrate:.0f}kbps"
            
            self.event_queue.put({
                'type': 'update_info',
                'info': info_text
            })
            self.event_queue.put({
                'type': 'update_status',
                'message': '就绪'
            })
                
        except FileNotFoundError as e:
            self.event_queue.put({
                'type': 'error',
                'message': str(e)
            })
        except RuntimeError as e:
            self.event_queue.put({
                'type': 'error',
                'message': str(e)
            })
        except Exception as e:
            self.event_queue.put({
                'type': 'error',
                'message': f"读取视频信息时发生错误: {str(e)}"
            })

    def start_extraction(self):
        if not self.video_path.get():
            messagebox.showwarning("警告", "请先选择视频文件")
            return
            
        if self.is_extracting:
            return
            
        self.is_extracting = True
        self.extract_button.config(state=tk.DISABLED)
        self.preview_button.config(state=tk.DISABLED)
        self.progress_var.set(0)
        self.status.set("准备提取...")
        
        # 在线程池中执行提取
        self.thread_pool.submit(self.extract_audio)

    def extract_audio(self):
        try:
            video_dir = os.path.dirname(self.video_path.get())
            video_name = os.path.splitext(os.path.basename(self.video_path.get()))[0]
            output_path = os.path.join(video_dir, f"{video_name}.{self.output_format.get()}")
            
            command = [
                "ffmpeg",
                "-y",  # 覆盖已存在的文件
                "-i", self.video_path.get(),
                "-vn",
                "-acodec"
            ]
            
            if self.output_format.get() == 'mp3':
                command.extend(["libmp3lame", "-b:a", self.audio_quality.get()])
            elif self.output_format.get() == 'aac':
                command.extend(["aac", "-b:a", self.audio_quality.get()])
            elif self.output_format.get() == 'flac':
                command.extend(["flac"])
            else:  # wav
                command.extend(["pcm_s16le"])
            
            command.append(output_path)
            
            # 使用utf-8编码启动进程
            process = subprocess.Popen(
                command,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                encoding='utf-8',
                errors='replace'  # 处理无法解码的字符
            )
            
            duration = None
            for line in process.stderr:
                try:
                    if "Duration" in line and duration is None:
                        time_str = line.split("Duration: ")[1].split(",")[0]
                        h, m, s = map(float, time_str.split(":"))
                        duration = h * 3600 + m * 60 + s
                    
                    if "time=" in line and duration:
                        time_str = line.split("time=")[1].split()[0]
                        h, m, s = map(float, time_str.split(":"))
                        current = h * 3600 + m * 60 + s
                        progress = (current / duration) * 100
                        
                        self.event_queue.put({
                            'type': 'update_progress',
                            'progress': progress
                        })
                        self.event_queue.put({
                            'type': 'update_status',
                            'message': f"提取中... {progress:.1f}%"
                        })
                except (ValueError, IndexError) as e:
                    # 忽略解析错误，继续处理下一行
                    continue
            
            process.wait()
            
            if process.returncode == 0:
                self.event_queue.put({
                    'type': 'extraction_complete',
                    'output_path': output_path,
                    'output_dir': video_dir
                })
            else:
                error_message = process.stderr.read() if process.stderr else "未知错误"
                raise Exception(f"ffmpeg执行失败: {error_message}")
                
        except Exception as e:
            self.event_queue.put({
                'type': 'error',
                'message': f"提取过程出错: {str(e)}"
            })
        finally:
            self.is_extracting = False
            self.root.after(0, lambda: self.extract_button.config(state=tk.NORMAL))

    def handle_extraction_complete(self, event):
        """处理提取完成事件"""
        self.status.set("提取完成")
        self.progress_var.set(100)
        self.preview_button.config(state=tk.NORMAL)
        messagebox.showinfo("完成", "音频提取完成！")
        self.open_output_folder(event['output_dir'])

    def handle_error(self, message):
        """处理错误事件"""
        self.status.set("出错")
        self.progress_var.set(0)
        messagebox.showerror("错误", message)

    def toggle_preview(self):
        if self.is_playing:
            # 暂停播放，记录当前位置
            self.audio_pos = pygame.mixer.music.get_pos() / 1000.0  # 转换为秒
            pygame.mixer.music.pause()
            self.preview_button.config(text="继续播放")
            self.is_playing = False
        else:
            video_dir = os.path.dirname(self.video_path.get())
            video_name = os.path.splitext(os.path.basename(self.video_path.get()))[0]
            audio_path = os.path.join(video_dir, f"{video_name}.{self.output_format.get()}")
            
            if os.path.exists(audio_path):
                try:
                    # 如果是新的音频文件
                    if self.current_audio != audio_path:
                        pygame.mixer.music.load(audio_path)
                        self.current_audio = audio_path
                        self.audio_pos = 0
                        pygame.mixer.music.play()
                    else:
                        # 从暂停位置继续播放
                        pygame.mixer.music.unpause()
                    
                    self.preview_button.config(text="暂停播放")
                    self.is_playing = True
                    
                except Exception as e:
                    messagebox.showerror("错误", f"无法播放音频: {str(e)}")
            else:
                messagebox.showwarning("警告", "找不到提取的音频文件")

    def update_audio_progress(self):
        """更新音频播放进度"""
        if self.is_playing and self.current_audio:
            try:
                # 获取当前播放位置（毫秒）
                pos = pygame.mixer.music.get_pos()
                if pos == -1:  # 播放结束
                    self.is_playing = False
                    self.preview_button.config(text="预览音频")
                    self.audio_pos = 0
                    self.audio_progress_var.set(0)
                else:
                    # 更新进度条
                    total_length = self.get_audio_length()
                    current_pos = pos / 1000.0  # 转换为秒
                    if total_length > 0:
                        progress = (current_pos / total_length) * 100
                        self.audio_progress_var.set(progress)
                        
                        # 更新时间标签
                        current_time = self.format_time(current_pos)
                        total_time = self.format_time(total_length)
                        self.audio_time_label.config(text=f"{current_time} / {total_time}")

            except Exception:
                pass  # 忽略更新进度时的错误

        # 继续更新
        self.root.after(100, self.update_audio_progress)

    def seek_audio(self, value):
        """处理进度条拖动"""
        if self.current_audio and float(value) >= 0:
            total_length = self.get_audio_length()
            if total_length > 0:
                # 计算新的播放位置
                new_pos = (float(value) / 100.0) * total_length
                # 重新开始播放
                pygame.mixer.music.play(start=new_pos)
                self.audio_pos = new_pos
                if not self.is_playing:
                    pygame.mixer.music.pause()

    def get_audio_length(self):
        """获取音频文件长度（秒）"""
        if self.current_audio:
            try:
                # 使用ffprobe获取音频长度
                cmd = [
                    "ffprobe",
                    "-v", "error",
                    "-show_entries", "format=duration",
                    "-of", "default=noprint_wrappers=1:nokey=1",
                    self.current_audio
                ]
                result = subprocess.run(cmd,
                                     capture_output=True,
                                     text=True,
                                     encoding='utf-8',
                                     errors='replace')
                if result.returncode == 0:
                    return float(result.stdout.strip())
            except Exception:
                pass
        return 0

    def format_time(self, seconds):
        """格式化时间为 MM:SS 格式"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

    def open_output_folder(self, folder_path):
        try:
            if os.name == 'nt':  # Windows
                os.startfile(folder_path)
            elif os.name == 'posix':  # macOS, Linux
                subprocess.run(['open', folder_path] if sys.platform == 'darwin' else ['xdg-open', folder_path])
        except Exception as e:
            messagebox.showerror("错误", f"无法打开输出文件夹: {str(e)}")

    def save_config(self):
        try:
            config = {
                'last_video_path': self.video_path.get(),
                'output_format': self.output_format.get(),
                'audio_quality': self.audio_quality.get()
            }
            with open('audio_extractor_config.json', 'w') as f:
                json.dump(config, f)
        except Exception:
            pass

    def load_config(self):
        try:
            if os.path.exists('audio_extractor_config.json'):
                with open('audio_extractor_config.json', 'r') as f:
                    config = json.load(f)
                    self.video_path.set(config.get('last_video_path', ''))
                    self.output_format.set(config.get('output_format', 'wav'))
                    self.audio_quality.set(config.get('audio_quality', '192k'))
                    if self.video_path.get():
                        self.thread_pool.submit(self.get_video_info)
        except Exception:
            pass

    def center_window(self, width=800, height=500):
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.root.geometry(f'{width}x{height}+{x}+{y}')

if __name__ == '__main__':
    try:
        root = tk.Tk()
        app = AudioExtractor(root)
        root.mainloop()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {str(e)}")
