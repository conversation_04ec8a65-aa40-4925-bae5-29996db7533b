import base64
import os
import sys

def decode_base64_file(input_file_path, output_file_path):
    """
    读取一个文件，如果内容是base64编码的，则解码并保存为一个解码后的文件

    Args:
        input_file_path: 输入文件的路径
        output_file_path: 输出文件的路径
    """
    try:
        # 读取输入文件
        with open(input_file_path, 'r', encoding='utf-8') as file:
            encoded_content = file.read()

        # 检查内容是否是base64编码的
        try:
            decoded_content = base64.b64decode(encoded_content).decode('utf-8')
        except (base64.binascii.Error, UnicodeDecodeError):
            print(f"错误: 文件 '{input_file_path}' 的内容不是有效的base64编码")
            return False

        # 将解码后的内容写入输出文件
        with open(output_file_path, 'w', encoding='utf-8') as file:
            file.write(decoded_content)

        print(f"解码后的内容已保存到: {output_file_path}")
        return True

    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    input_file_path = "unlimited_nodes/web.yaml"
    output_file_path = "unlimited_nodes/web_decoded.yaml"

    # 执行解码
    success = decode_base64_file(input_file_path, output_file_path)

    if success:
        print("处理完成")
    else:
        print("处理失败")
        sys.exit(1)
