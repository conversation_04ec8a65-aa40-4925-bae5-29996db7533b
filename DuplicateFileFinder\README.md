# Duplicate File Finder

This is a Python application to find duplicate and similar files/images in a selected directory.

## Features

- Find exact duplicate files using SHA256 hash.
- Find visually similar images using perceptual hashing (imagehash).
- GUI built with Tkinter.
- Multithreaded scanning to keep the UI responsive.

## Usage

1. Install dependencies: `pip install -r requirements.txt`
2. Run the application: `python app.py`
