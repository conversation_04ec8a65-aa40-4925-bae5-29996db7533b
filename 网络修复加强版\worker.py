import os
import winreg
import socket
import time
import logging
from PyQt5.QtCore import QThread, pyqtSignal, QMutex, QProcess

class Worker(QThread):
    status_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int)
    finished_signal = pyqtSignal()
    error_signal = pyqtSignal(str)

    def __init__(self, task, proxy_server=None):
        super().__init__()
        self.task = task
        self.proxy_server = proxy_server
        self.is_cancelled = False
        self.mutex = QMutex()
        self.test_targets = [
            ('8.8.8.8', 53, 'Google DNS'),
            ('223.5.5.5', 53, '阿里DNS'),
            ('114.114.114.114', 53, '114 DNS'),
            ('www.baidu.com', 80, '百度')
        ]

    def run(self):
        try:
            if self.task == 'disable_proxy':
                self.disable_proxy()
            elif self.task == 'enable_proxy':
                self.enable_proxy()
            elif self.task == 'test_network':
                self.test_network()
            elif self.task == 'repair_network':
                self.repair_network()
            elif self.task == 'clear_cache':
                self.clear_cache()

            if not self.is_cancelled:
                self.finished_signal.emit()
        except Exception as e:
            logging.error(f"线程执行错误: {str(e)}")
            self.error_signal.emit(f"执行错误: {str(e)}")

    def cancel(self):
        self.mutex.lock()
        self.is_cancelled = True
        self.mutex.unlock()

    # 添加清理缓存的方法
    def clear_cache(self):
        try:
            self.status_signal.emit("开始清理系统缓存...")
            self.progress_signal.emit(10)
            
            # 清理临时文件
            self.status_signal.emit("清理临时文件...")
            self.progress_signal.emit(20)
            os.system('del /f /s /q %temp%\\*.*')
            
            # 清理Windows预取文件
            self.status_signal.emit("清理Windows预取文件...")
            self.progress_signal.emit(40)
            os.system('del /f /s /q C:\\Windows\\Prefetch\\*.*')
            
            # 清理系统缓存
            self.status_signal.emit("清理系统缓存...")
            self.progress_signal.emit(60)
            os.system('ipconfig /flushdns')
            
            # 清理IE缓存
            self.status_signal.emit("清理浏览器缓存...")
            self.progress_signal.emit(80)
            os.system('RunDll32.exe InetCpl.cpl,ClearMyTracksByProcess 8')
            
            self.status_signal.emit("系统缓存清理完成")
            self.progress_signal.emit(100)
            
        except Exception as e:
            logging.error(f"清理缓存错误: {str(e)}")
            self.status_signal.emit(f"清理缓存错误: {str(e)}")
            self.progress_signal.emit(0)

    def check_admin(self):
        """检查是否具有管理员权限"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False

    def run_command(self, cmd, timeout=10):
        """安全地执行系统命令"""
        try:
            if not self.check_admin():
                raise PermissionError("需要管理员权限才能执行此操作")

            import subprocess
            import sys
            import os
            
            # 使用完整的命令路径
            system32_path = os.path.join(os.environ['SystemRoot'], 'System32')
            if cmd.startswith('ipconfig'):
                exe_path = os.path.join(system32_path, "ipconfig.exe")
                args = cmd.split()[1:]
            elif cmd.startswith('netsh'):
                exe_path = os.path.join(system32_path, "netsh.exe")
                args = cmd.split()[1:]
            elif cmd.startswith('nbtstat'):
                exe_path = os.path.join(system32_path, "nbtstat.exe")
                args = cmd.split()[1:]
            elif cmd.startswith('arp'):
                exe_path = os.path.join(system32_path, "arp.exe")
                args = cmd.split()[1:]
            else:
                raise ValueError(f"不支持的命令: {cmd}")
            
            # 构建完整命令
            full_cmd = [exe_path] + args
            logging.info(f"执行命令: {' '.join(full_cmd)}")
            
            # 使用 subprocess.run 执行命令
            try:
                # 创建 startupinfo 对象来隐藏窗口
                startupinfo = None
                if sys.platform == 'win32':
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = subprocess.SW_HIDE

                # 执行命令
                result = subprocess.run(
                    full_cmd,
                    capture_output=True,
                    text=True,
                    timeout=timeout,
                    encoding='gbk',
                    errors='ignore',
                    startupinfo=startupinfo,
                    check=False  # 不自动抛出异常
                )
                
                # 记录输出
                if result.stdout:
                    logging.info(f"命令输出: {result.stdout.strip()}")
                if result.stderr:
                    logging.warning(f"命令错误输出: {result.stderr.strip()}")
                
                # 检查返回值
                if result.returncode != 0:
                    logging.warning(f"命令返回非零值: {result.returncode}")
                    return False
                    
                return True
                
            except subprocess.TimeoutExpired:
                logging.error(f"命令执行超时: {' '.join(full_cmd)}")
                self.error_signal.emit(f"命令执行超时: {cmd}")
                return False
                
        except Exception as e:
            error_msg = f"执行命令 {cmd} 时出错: {str(e)}"
            logging.error(error_msg)
            self.error_signal.emit(error_msg)
            return False

    def repair_network(self):
        """修复网络连接"""
        try:
            if self.is_cancelled:
                return

            if not self.check_admin():
                self.error_signal.emit("需要管理员权限才能执行此操作")
                return

            self.status_signal.emit("开始网络修复...")
            self.progress_signal.emit(10)

            commands = [
                ('ipconfig /flushdns', "清理 DNS 缓存", 20),
                ('nbtstat -R', "清理 NetBIOS 缓存", 30),
                ('arp -d *', "清理 ARP 缓存", 40),
                ('netsh interface ip delete arpcache', "重置网络接口 ARP 缓存", 50),
                ('netsh interface ip delete destinationcache', "重置目标缓存", 60),
                ('ipconfig /release', "释放 IP 地址", 70),
                ('ipconfig /renew', "更新 IP 地址", 90)
            ]

            success_count = 0
            for cmd, status, progress in commands:
                if self.is_cancelled:
                    return

                try:
                    self.status_signal.emit(status + "...")
                    self.progress_signal.emit(progress)
                    logging.info(f"准备执行命令: {cmd}")

                    if self.run_command(cmd):
                        success_count += 1
                        logging.info(f"命令执行成功: {cmd}")
                    else:
                        logging.warning(f"命令执行失败: {cmd}")

                    # 每个命令之间添加短暂延时
                    time.sleep(1)  # 减少延时到1秒
                    
                except Exception as e:
                    error_msg = f"执行命令 {cmd} 时出错: {str(e)}"
                    logging.error(error_msg)
                    self.error_signal.emit(error_msg)
                    continue

            if not self.is_cancelled:
                if success_count == len(commands):
                    msg = "网络修复完成"
                    self.status_signal.emit(msg)
                    logging.info(msg)
                else:
                    msg = f"网络修复部分完成 ({success_count}/{len(commands)})"
                    self.status_signal.emit(msg)
                    logging.warning(f"部分命令执行失败，成功率: {success_count}/{len(commands)}")
                self.progress_signal.emit(100)

        except Exception as e:
            error_msg = f"网络修复错误: {str(e)}"
            logging.error(error_msg)
            self.error_signal.emit(error_msg)
            self.progress_signal.emit(0)

    def disable_proxy(self):
        try:
            self.status_signal.emit("正在准备关闭代理...")
            self.progress_signal.emit(10)
            time.sleep(0.1)
            if self.is_cancelled: return

            self.status_signal.emit("正在打开注册表...")
            self.progress_signal.emit(30)
            reg_key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"Software\Microsoft\Windows\CurrentVersion\Internet Settings",
                0,
                winreg.KEY_WRITE
            )
            if self.is_cancelled:
                winreg.CloseKey(reg_key)
                return

            self.status_signal.emit("正在修改代理设置...")
            self.progress_signal.emit(60)
            winreg.SetValueEx(reg_key, "ProxyEnable", 0, winreg.REG_DWORD, 0)
            if self.is_cancelled:
                winreg.CloseKey(reg_key)
                return

            self.status_signal.emit("正在保存设置...")
            self.progress_signal.emit(90)
            winreg.CloseKey(reg_key)

            # 刷新系统代理设置
            os.system('ipconfig /flushdns')

            if not self.is_cancelled:
                self.status_signal.emit("代理已关闭")
                self.progress_signal.emit(100)
                logging.info("代理已关闭")
        except winreg.error as e:
            logging.error(f"注册表操作错误: {str(e)}")
            self.status_signal.emit(f"注册表操作错误: {str(e)}")
            self.progress_signal.emit(0)
        except Exception as e:
            logging.error(f"关闭代理错误: {str(e)}")
            self.status_signal.emit(f"关闭代理错误: {str(e)}")
            self.progress_signal.emit(0)

    def enable_proxy(self):
        try:
            if not self.proxy_server:
                self.status_signal.emit("未设置代理服务器地址")
                self.progress_signal.emit(0)
                return

            self.status_signal.emit("正在准备启用代理...")
            self.progress_signal.emit(10)
            time.sleep(0.1)
            if self.is_cancelled: return

            self.status_signal.emit("正在打开注册表...")
            self.progress_signal.emit(30)
            reg_key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"Software\Microsoft\Windows\CurrentVersion\Internet Settings",
                0,
                winreg.KEY_WRITE
            )
            if self.is_cancelled:
                winreg.CloseKey(reg_key)
                return

            self.status_signal.emit("正在修改代理设置...")
            self.progress_signal.emit(50)
            winreg.SetValueEx(reg_key, "ProxyEnable", 0, winreg.REG_DWORD, 1)

            self.progress_signal.emit(70)
            winreg.SetValueEx(reg_key, "ProxyServer", 0, winreg.REG_SZ, self.proxy_server)

            if self.is_cancelled:
                winreg.CloseKey(reg_key)
                return

            self.status_signal.emit("正在保存设置...")
            self.progress_signal.emit(90)
            winreg.CloseKey(reg_key)

            # 刷新系统代理设置
            os.system('ipconfig /flushdns')

            if not self.is_cancelled:
                self.status_signal.emit(f"代理已启用: {self.proxy_server}")
                self.progress_signal.emit(100)
                logging.info(f"代理已启用: {self.proxy_server}")
        except winreg.error as e:
            logging.error(f"注册表操作错误: {str(e)}")
            self.status_signal.emit(f"注册表操作错误: {str(e)}")
            self.progress_signal.emit(0)
        except Exception as e:
            logging.error(f"启用代理错误: {str(e)}")
            self.status_signal.emit(f"启用代理错误: {str(e)}")
            self.progress_signal.emit(0)

    def test_network(self):
        try:
            self.status_signal.emit("正在初始化网络测试...")
            self.progress_signal.emit(10)
            time.sleep(0.1)
            if self.is_cancelled: return

            socket.setdefaulttimeout(5)
            success_count = 0
            total_targets = len(self.test_targets)

            for i, (host, port, name) in enumerate(self.test_targets):
                if self.is_cancelled: return

                progress = 10 + int(80 * (i / total_targets))
                self.progress_signal.emit(progress)
                self.status_signal.emit(f"正在测试 {name} ({host})...")

                try:
                    # 使用 with 语句自动关闭 socket
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                        # 先尝试解析域名
                        try:
                            if not host.replace('.', '').isdigit():
                                host = socket.gethostbyname(host)
                        except socket.gaierror as e:
                            logging.error(f"域名解析失败 {name} ({host}): {str(e)}")
                            continue

                        result = sock.connect_ex((host, port))

                        if result == 0:
                            success_count += 1
                            logging.info(f"连接 {name} ({host}:{port}) 成功")
                        else:
                            logging.warning(f"连接 {name} ({host}:{port}) 失败，错误码: {result}")

                except Exception as e:
                    logging.error(f"测试 {name} ({host}:{port}) 时出错: {str(e)}")
                    continue

            self.progress_signal.emit(100)
            if success_count == total_targets:
                self.status_signal.emit("网络连接正常！所有测试点均可访问")
            elif success_count > 0:
                self.status_signal.emit(f"网络部分可用 ({success_count}/{total_targets})")
            else:
                self.status_signal.emit("网络连接失败！所有测试点均不可访问")

        except socket.timeout:
            logging.error("网络连接超时")
            self.status_signal.emit("网络连接超时！")
            self.progress_signal.emit(0)
        except Exception as e:
            logging.error(f"网络测试错误: {str(e)}")
            self.status_signal.emit(f"网络测试错误: {str(e)}")
            self.progress_signal.emit(0)


