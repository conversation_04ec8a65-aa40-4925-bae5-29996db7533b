#! /usr/bin/env python
#  -*- coding: utf-8 -*-

import sys
import requests
import threading
import json
import pyperclip
from tkinter import messagebox, Menu
from requests.packages.urllib3.exceptions import InsecureRequestWarning
import time
import math

requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

try:
    import Tkinter as tk
except ImportError:
    import tkinter as tk

try:
    import ttk
    py3 = False
except ImportError:
    import tkinter.ttk as ttk
    py3 = True

# 导入自定义模块
from config import UI, LANGUAGES, FILES, API
from history import TranslationHistory

class AnimationManager:
    """动画管理器 - 处理各种UI动画效果"""

    def __init__(self, root):
        self.root = root
        self.animations = {}  # 存储正在进行的动画

    def fade_in_window(self, window, duration=500):
        """窗口淡入动画"""
        window.attributes('-alpha', 0.0)
        steps = 20
        step_time = duration // steps
        alpha_step = 1.0 / steps

        def animate_step(step):
            if step <= steps:
                alpha = step * alpha_step
                window.attributes('-alpha', alpha)
                window.after(step_time, lambda: animate_step(step + 1))

        animate_step(1)

    def button_pulse(self, button, duration=1000):
        """按钮脉冲动画"""
        original_bg = button.cget('background')
        pulse_color = UI['primary_button_hover']
        steps = 30
        step_time = duration // steps

        def animate_pulse(step):
            if step <= steps:
                # 使用正弦波创建脉冲效果
                intensity = (math.sin(step * math.pi / steps * 2) + 1) / 2
                # 在原色和脉冲色之间插值
                if step <= steps:
                    button.after(step_time, lambda: animate_pulse(step + 1))

        animate_pulse(1)

    def button_click_scale(self, button):
        """按钮点击缩放动画"""
        def scale_down():
            # 缩小效果 - 改变按钮状态
            try:
                button.state(['pressed'])
            except:
                pass
            button.after(50, scale_up)

        def scale_up():
            # 恢复效果
            try:
                button.state(['!pressed'])
            except:
                pass

        scale_down()

    def swap_button_rotate(self, button):
        """交换按钮旋转动画"""
        symbols = ['⇄', '↻', '⇄', '↺']
        current_symbol = 0

        def rotate_step():
            nonlocal current_symbol
            if current_symbol < len(symbols):
                button.configure(text=symbols[current_symbol])
                current_symbol += 1
                button.after(100, rotate_step)
            else:
                button.configure(text='⇄')

        rotate_step()

    def status_slide_in(self, status_bar, text, status_type="info"):
        """状态栏文字滑入动画"""
        # 先清空当前文字
        status_bar.label.configure(text="")

        # 逐字显示新文字
        def show_char(index):
            if index <= len(text):
                status_bar.label.configure(text=text[:index])
                if index < len(text):
                    status_bar.label.after(30, lambda: show_char(index + 1))
                else:
                    # 设置状态指示器
                    status_configs = {
                        'info': {'color': UI['info_color'], 'indicator': '就绪'},
                        'success': {'color': UI['success_color'], 'indicator': '完成'},
                        'warning': {'color': UI['warning_color'], 'indicator': '警告'},
                        'error': {'color': UI['error_color'], 'indicator': '错误'}
                    }
                    config = status_configs.get(status_type, status_configs['info'])
                    status_bar.status_indicator.config(text=config['indicator'], fg=config['color'])

        show_char(0)

    def text_focus_glow(self, text_widget, focused=True):
        """文本框聚焦发光动画"""
        if focused:
            # 聚焦时的发光效果
            colors = [UI['text_border'], UI['text_focus_border']]
            steps = 10

            def animate_glow(step):
                if step <= steps:
                    # 计算颜色插值
                    progress = step / steps
                    if progress <= 1:
                        text_widget.configure(highlightbackground=UI['text_focus_border'])
                        if step < steps:
                            text_widget.after(20, lambda: animate_glow(step + 1))

            animate_glow(1)
        else:
            # 失焦时恢复
            text_widget.configure(highlightbackground=UI['text_border'])

    def copy_success_flash(self, widget):
        """复制成功闪烁动画"""
        try:
            original_bg = widget.cget('bg')
            flash_color = UI['success_color']

            def flash():
                widget.configure(bg=flash_color)
                widget.after(100, lambda: widget.configure(bg=original_bg))

            flash()
        except:
            # 如果无法获取背景色，跳过动画
            pass

    def loading_dots(self, button, text="翻译中"):
        """加载点动画"""
        dots = ["", ".", "..", "..."]
        current_dot = 0

        def animate_dots():
            nonlocal current_dot
            if hasattr(button, '_loading') and button._loading:
                button.configure(text=f"{text}{dots[current_dot]}")
                current_dot = (current_dot + 1) % len(dots)
                button.after(300, animate_dots)

        button._loading = True
        animate_dots()

    def stop_loading(self, button, original_text):
        """停止加载动画"""
        button._loading = False
        button.configure(text=original_text)

    def text_type_animation(self, text_widget, text, speed=20):
        """文字逐字出现动画"""
        # 清空文本框
        text_widget.delete(1.0, tk.END)

        def type_char(index):
            if index <= len(text):
                text_widget.delete(1.0, tk.END)
                text_widget.insert(1.0, text[:index])
                if index < len(text):
                    text_widget.after(speed, lambda: type_char(index + 1))

        type_char(0)

class StatusBar(tk.Frame):
    """简约商务风格状态栏组件"""

    def __init__(self, master, **kwargs):
        """初始化状态栏"""
        tk.Frame.__init__(self, master, bg=UI['bg'], **kwargs)
        self.pack_propagate(False)  # 防止自动调整大小

        # 顶部分隔线
        top_separator = tk.Frame(self, bg=UI['separator'], height=1)
        top_separator.pack(side=tk.TOP, fill=tk.X)

        # 状态栏容器
        container = tk.Frame(self, bg=UI['bg'], height=UI['status_bar']['height'])
        container.pack(fill=tk.BOTH, expand=True)
        container.pack_propagate(False)

        # 内容框架
        content = tk.Frame(container, bg=UI['bg'])
        content.pack(fill=tk.BOTH, expand=True, padx=UI['status_bar']['padding'], pady=8)

        # 状态文本
        self.label = tk.Label(content, bg=UI['bg'], fg=UI['fg'],
                            anchor=tk.W, font=UI['font'])
        self.label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 状态指示器
        self.status_indicator = tk.Label(content, text="就绪", bg=UI['bg'],
                                       fg=UI['info_color'], font=UI['font'])
        self.status_indicator.pack(side=tk.RIGHT)

    def set(self, text, status_type="info"):
        """设置状态栏文本

        Args:
            text: 状态文本
            status_type: 状态类型 ('info', 'success', 'warning', 'error')
        """
        # 根据状态类型设置颜色和指示器
        status_configs = {
            'info': {'color': UI['info_color'], 'indicator': '就绪'},
            'success': {'color': UI['success_color'], 'indicator': '完成'},
            'warning': {'color': UI['warning_color'], 'indicator': '警告'},
            'error': {'color': UI['error_color'], 'indicator': '错误'}
        }

        config = status_configs.get(status_type, status_configs['info'])

        self.label.config(text=text)
        self.status_indicator.config(text=config['indicator'], fg=config['color'])
        self.label.update_idletasks()

    def clear(self):
        """清空状态栏"""
        self.set("就绪")

def vp_start_gui():
    '''Starting point when module is the main routine.'''
    global root
    root = tk.Tk()
    root.configure(background=UI['bg'])

    # 设置窗口大小
    window_width = UI['width']
    window_height = UI['height']

    # 获取屏幕尺寸
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()

    # 计算窗口位置
    x = (screen_width - window_width) // 2
    y = (screen_height - window_height) // 2

    # 设置窗口大小和位置
    root.geometry(f"{window_width}x{window_height}+{x}+{y}")

    # 设置简约商务样式
    style = ttk.Style()

    # 基础样式
    style.configure('TFrame', background=UI['bg'])
    style.configure('TLabel', background=UI['bg'], foreground=UI['fg'], font=UI['font'])

    # 区域样式
    style.configure('Section.TFrame',
                   background=UI['section_bg'],
                   relief='solid',
                   borderwidth=1,
                   bordercolor=UI['card_border'])

    # 主要按钮样式 - 商务风格
    style.configure('Primary.TButton',
                   font=UI['button_font'],
                   background=UI['primary_button_bg'],
                   foreground=UI['primary_button_fg'],
                   borderwidth=0,
                   relief='flat',
                   padding=UI['primary_button']['padding'],
                   focuscolor='none',
                   width=UI['primary_button']['width'])

    style.map('Primary.TButton',
             background=[('active', UI['primary_button_hover']),
                        ('pressed', UI['primary_button_hover'])])

    # 次要按钮样式
    style.configure('Secondary.TButton',
                   font=UI['button_font'],
                   background=UI['secondary_button_bg'],
                   foreground=UI['secondary_button_fg'],
                   borderwidth=1,
                   bordercolor=UI['secondary_button_border'],
                   relief='flat',
                   padding=UI['button']['padding'],
                   focuscolor='none',
                   width=UI['button']['width'])

    style.map('Secondary.TButton',
             background=[('active', UI['secondary_button_hover']),
                        ('pressed', UI['secondary_button_hover'])])

    # 强调按钮样式
    style.configure('Accent.TButton',
                   font=UI['button_font'],
                   background=UI['accent_button_bg'],
                   foreground=UI['accent_button_fg'],
                   borderwidth=0,
                   relief='flat',
                   padding=UI['button']['padding'],
                   focuscolor='none',
                   width=UI['button']['width'])

    style.map('Accent.TButton',
             background=[('active', UI['accent_button_hover']),
                        ('pressed', UI['accent_button_hover'])])

    # 危险按钮样式
    style.configure('Danger.TButton',
                   font=UI['button_font'],
                   background=UI['danger_button_bg'],
                   foreground=UI['danger_button_fg'],
                   borderwidth=0,
                   relief='flat',
                   padding=UI['button']['padding'],
                   focuscolor='none',
                   width=UI['button']['width'])

    # 交换按钮样式
    style.configure('Swap.TButton',
                   font=UI['swap_button']['font'],
                   background=UI['secondary_button_bg'],
                   foreground=UI['primary_button_bg'],
                   borderwidth=1,
                   bordercolor=UI['secondary_button_border'],
                   relief='flat',
                   padding=UI['swap_button']['padding'],
                   focuscolor='none',
                   width=UI['swap_button']['width'])

    style.map('Swap.TButton',
             background=[('active', UI['secondary_button_hover']),
                        ('pressed', UI['primary_button_bg'])],
             foreground=[('pressed', UI['primary_button_fg'])])

    # Combobox样式
    style.configure('TCombobox',
                   fieldbackground=UI['text_bg'],
                   background=UI['card_bg'],
                   foreground=UI['text_fg'],
                   arrowcolor=UI['primary_button_bg'],
                   selectbackground=UI['highlight_bg'],
                   selectforeground=UI['highlight_fg'],
                   borderwidth=1,
                   bordercolor=UI['text_border'],
                   relief='solid',
                   padding=UI['combo']['padding'])

    style.map('TCombobox',
             bordercolor=[('focus', UI['text_focus_border'])])

    # Checkbutton样式
    style.configure('TCheckbutton',
                   background=UI['bg'],
                   foreground=UI['fg'],
                   font=UI['font'],
                   focuscolor='none')

    # 滚动条样式
    style.configure('TScrollbar',
                   background=UI['card_bg'],
                   arrowcolor=UI['primary_button_bg'],
                   bordercolor=UI['border'],
                   troughcolor=UI['section_bg'],
                   gripcount=0,
                   borderwidth=1,
                   relief='solid')

    style.map('TScrollbar',
             background=[('active', UI['primary_button_bg'])])

    top = Toplevel1(root)
    root.mainloop()

class Toplevel1:
    def __init__(self, top=None):
        '''This class configures and populates the toplevel window.'''
        self.top = top
        self.top.title(UI['title'])
        self.top.configure(background=UI['bg'])
        self.top.geometry(f"{UI['width']}x{UI['height']}")
        self.top.minsize(900, 650)

        # 初始化动画管理器
        self.animation_manager = AnimationManager(self.top)

        # 初始化翻译历史记录
        self.history_manager = TranslationHistory(FILES['history'])

        # 初始化自动复制设置
        self.auto_copy = tk.BooleanVar(value=True)

        # 初始化语言设置
        self.from_lang = tk.StringVar(value='auto')
        self.to_lang = tk.StringVar(value='zh')

        # 使用定时器定期保存窗口状态
        self._last_save_time = 0
        self._save_interval = 5000  # 5秒
        self._schedule_state_save()

        # 加载窗口状态
        self._load_window_state()

        # 创建菜单栏
        self._create_menu()

        # 主容器框架
        main_container = tk.Frame(top, bg=UI['bg'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=UI['padding'], pady=UI['padding'])

        # 创建简约商务布局
        self._create_language_section(main_container)
        self._create_text_section(main_container)
        self._create_button_section(main_container)

        # 状态栏
        self.status_bar = StatusBar(top)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        self.status_bar.set("🚀 就绪")

        # 绑定快捷键
        self._bind_shortcuts()

    def _create_language_section(self, parent):
        """创建简约商务风格语言选择区域"""
        # 语言选择区域
        lang_section = ttk.Frame(parent, style='Section.TFrame')
        lang_section.pack(fill=tk.X, pady=(0, UI['layout']['section_spacing']))

        # 区域内容框架
        lang_content = tk.Frame(lang_section, bg=UI['section_bg'])
        lang_content.pack(fill=tk.X, padx=UI['section']['padding'], pady=UI['section']['padding'])

        # 标题
        title_label = tk.Label(lang_content, text="语言设置",
                              font=UI['title_font'],
                              bg=UI['section_bg'],
                              fg=UI['fg'])
        title_label.pack(anchor=tk.W, pady=(0, UI['layout']['element_spacing']))

        # 语言选择控件区域 - 优化布局
        controls_frame = tk.Frame(lang_content, bg=UI['section_bg'])
        controls_frame.pack(fill=tk.X)

        # 左侧语言选择区域
        lang_selection_frame = tk.Frame(controls_frame, bg=UI['section_bg'])
        lang_selection_frame.pack(side=tk.LEFT)

        # 源语言
        from_lang_frame = tk.Frame(lang_selection_frame, bg=UI['section_bg'])
        from_lang_frame.pack(side=tk.LEFT, padx=(0, UI['layout']['button_spacing']*2))

        from_lang_label = tk.Label(from_lang_frame, text="源语言:",
                                  font=UI['font'],
                                  bg=UI['section_bg'],
                                  fg=UI['fg'])
        from_lang_label.pack(side=tk.LEFT, padx=(0, UI['layout']['element_spacing']))

        self.from_lang_combo = ttk.Combobox(from_lang_frame,
                                        values=list(LANGUAGES.values()),
                                        width=UI['combo']['width'],
                                        font=UI['font'],
                                        state='readonly')
        self.from_lang_combo.pack(side=tk.LEFT)
        self.from_lang_combo.set('自动检测')

        # 交换按钮
        self.swap_btn = ttk.Button(lang_selection_frame, text=UI['swap_button']['text'],
                                 style='Swap.TButton',
                                 command=self.swap_languages_animated)
        self.swap_btn.pack(side=tk.LEFT, padx=UI['layout']['button_spacing'])

        # 目标语言
        to_lang_frame = tk.Frame(lang_selection_frame, bg=UI['section_bg'])
        to_lang_frame.pack(side=tk.LEFT, padx=(UI['layout']['button_spacing'], 0))

        to_lang_label = tk.Label(to_lang_frame, text="目标语言:",
                                font=UI['font'],
                                bg=UI['section_bg'],
                                fg=UI['fg'])
        to_lang_label.pack(side=tk.LEFT, padx=(0, UI['layout']['element_spacing']))

        self.to_lang_combo = ttk.Combobox(to_lang_frame,
                                        values=list(LANGUAGES.values()),
                                        width=UI['combo']['width'],
                                        font=UI['font'],
                                        state='readonly')
        self.to_lang_combo.pack(side=tk.LEFT)
        self.to_lang_combo.set('中文')
        self.to_lang_combo.bind('<<ComboboxSelected>>', self.on_language_change)

        # 右侧选项区域
        options_frame = tk.Frame(controls_frame, bg=UI['section_bg'])
        options_frame.pack(side=tk.RIGHT, padx=(UI['layout']['button_group_spacing'], 0))

        # 自动复制选项
        auto_copy_check = ttk.Checkbutton(options_frame, text="自动复制结果",
                                        variable=self.auto_copy)
        auto_copy_check.pack()

    def _create_text_section(self, parent):
        """创建简约商务风格文本输入输出区域"""
        # 文本区域容器
        text_container = tk.Frame(parent, bg=UI['bg'])
        text_container.pack(fill=tk.BOTH, expand=True, pady=(0, UI['layout']['section_spacing']))

        # 源文本区域
        source_section = ttk.Frame(text_container, style='Section.TFrame')
        source_section.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, UI['layout']['element_spacing']))

        # 源文本标题和内容
        source_content = tk.Frame(source_section, bg=UI['section_bg'])
        source_content.pack(fill=tk.BOTH, expand=True, padx=UI['section']['padding'], pady=UI['section']['padding'])

        source_title = tk.Label(source_content, text="原文",
                               font=UI['title_font'],
                               bg=UI['section_bg'],
                               fg=UI['fg'])
        source_title.pack(anchor=tk.W, pady=(0, UI['layout']['element_spacing']))

        # 源文本框架
        source_frame = tk.Frame(source_content, bg=UI['section_bg'])
        source_frame.pack(fill=tk.BOTH, expand=True)

        self.original_text = tk.Text(source_frame,
                                  wrap=UI['text']['wrap'],
                                  width=40,
                                  height=UI['text']['height'],
                                  font=UI['text_font'],
                                  relief=UI['text']['relief'],
                                  bd=UI['text']['borderwidth'],
                                  bg=UI['text_bg'],
                                  fg=UI['text_fg'],
                                  padx=UI['text']['padx'],
                                  pady=UI['text']['pady'],
                                  insertwidth=UI['text']['insertwidth'],
                                  selectbackground=UI['text']['selectbackground'],
                                  selectforeground=UI['text']['selectforeground'],
                                  spacing1=UI['text']['spacing1'],
                                  spacing2=UI['text']['spacing2'],
                                  spacing3=UI['text']['spacing3'],
                                  highlightthickness=1,
                                  highlightcolor=UI['text_focus_border'],
                                  highlightbackground=UI['text_border'])
        self.original_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 源文本滚动条
        original_scrollbar = ttk.Scrollbar(source_frame, orient=tk.VERTICAL,
                                       command=self.original_text.yview)
        self.original_text.configure(yscrollcommand=original_scrollbar.set)
        original_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 目标文本区域
        target_section = ttk.Frame(text_container, style='Section.TFrame')
        target_section.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(UI['layout']['element_spacing'], 0))

        # 目标文本标题和内容
        target_content = tk.Frame(target_section, bg=UI['section_bg'])
        target_content.pack(fill=tk.BOTH, expand=True, padx=UI['section']['padding'], pady=UI['section']['padding'])

        target_title = tk.Label(target_content, text="译文",
                               font=UI['title_font'],
                               bg=UI['section_bg'],
                               fg=UI['fg'])
        target_title.pack(anchor=tk.W, pady=(0, UI['layout']['element_spacing']))

        # 目标文本框架
        target_frame = tk.Frame(target_content, bg=UI['section_bg'])
        target_frame.pack(fill=tk.BOTH, expand=True)

        self.now_text = tk.Text(target_frame,
                              wrap=UI['text']['wrap'],
                              width=40,
                              height=UI['text']['height'],
                              font=UI['text_font'],
                              relief=UI['text']['relief'],
                              bd=UI['text']['borderwidth'],
                              bg=UI['text_bg'],
                              fg=UI['text_fg'],
                              padx=UI['text']['padx'],
                              pady=UI['text']['pady'],
                              insertwidth=UI['text']['insertwidth'],
                              selectbackground=UI['text']['selectbackground'],
                              selectforeground=UI['text']['selectforeground'],
                              spacing1=UI['text']['spacing1'],
                              spacing2=UI['text']['spacing2'],
                              spacing3=UI['text']['spacing3'],
                              highlightthickness=1,
                              highlightcolor=UI['text_focus_border'],
                              highlightbackground=UI['text_border'])
        self.now_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 目标文本滚动条
        now_scrollbar = ttk.Scrollbar(target_frame, orient=tk.VERTICAL,
                                  command=self.now_text.yview)
        self.now_text.configure(yscrollcommand=now_scrollbar.set)
        now_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定聚焦动画事件
        self.original_text.bind("<FocusIn>", lambda e: self.animation_manager.text_focus_glow(self.original_text, True))
        self.original_text.bind("<FocusOut>", lambda e: self.animation_manager.text_focus_glow(self.original_text, False))
        self.now_text.bind("<FocusIn>", lambda e: self.animation_manager.text_focus_glow(self.now_text, True))
        self.now_text.bind("<FocusOut>", lambda e: self.animation_manager.text_focus_glow(self.now_text, False))

        # 绑定滚动同步事件
        self.original_text.bind("<MouseWheel>", self._on_mousewheel)
        self.now_text.bind("<MouseWheel>", self._on_mousewheel)
        self.original_text.bind("<Up>", self._on_up_down)
        self.now_text.bind("<Up>", self._on_up_down)
        self.original_text.bind("<Down>", self._on_up_down)
        self.now_text.bind("<Down>", self._on_up_down)
        self.original_text.bind("<Prior>", self._on_page_updown)
        self.now_text.bind("<Prior>", self._on_page_updown)
        self.original_text.bind("<Next>", self._on_page_updown)
        self.now_text.bind("<Next>", self._on_page_updown)

        # 创建右键菜单
        self._create_text_context_menu(self.original_text)
        self._create_text_context_menu(self.now_text)


    def _create_button_section(self, parent):
        """创建简约商务风格按钮操作区域"""
        # 按钮区域
        button_section = ttk.Frame(parent, style='Section.TFrame')
        button_section.pack(fill=tk.X)

        # 按钮内容框架
        button_content = tk.Frame(button_section, bg=UI['section_bg'])
        button_content.pack(fill=tk.X, padx=UI['section']['padding'], pady=UI['section']['padding'])

        # 翻译按钮（均分空间）
        self.translateBtn = ttk.Button(button_content, text='翻译',
                                   style='Secondary.TButton',
                                   command=self.translate_animated)
        self.translateBtn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, UI['layout']['button_spacing']))

        # 清空按钮
        self.clearBtn = ttk.Button(button_content, text='清空',
                               style='Secondary.TButton',
                               command=self.clear_all)
        self.clearBtn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, UI['layout']['button_spacing']))

        # 复制原文按钮
        copy_source_btn = ttk.Button(button_content, text='复制原文',
                                 style='Secondary.TButton',
                                 command=lambda: self.copy_text(self.original_text))
        copy_source_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, UI['layout']['button_spacing']))

        # 复制译文按钮
        copy_target_btn = ttk.Button(button_content, text='复制译文',
                                style='Secondary.TButton',
                                command=lambda: self.copy_text(self.now_text))
        copy_target_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, UI['layout']['button_spacing']))

        # 历史记录按钮
        self.historyBtn = ttk.Button(button_content, text='历史记录',
                                 style='Secondary.TButton',
                                 command=self.show_history)
        self.historyBtn.pack(side=tk.LEFT, fill=tk.X, expand=True)

    def _create_menu(self):
        """创建菜单栏"""
        menubar = Menu(self.top)
        
        # 文件菜单
        file_menu = Menu(menubar, tearoff=0)
        file_menu.add_command(label="新建", command=self.clear_all, accelerator="Ctrl+N")
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.top.quit, accelerator="Alt+F4")
        menubar.add_cascade(label="文件", menu=file_menu)
        
        # 编辑菜单
        edit_menu = Menu(menubar, tearoff=0)
        edit_menu.add_command(label="撤销", command=self._undo, accelerator="Ctrl+Z")
        edit_menu.add_command(label="重做", command=self._redo, accelerator="Ctrl+Y")
        edit_menu.add_separator()
        edit_menu.add_command(label="剪切", command=self._cut, accelerator="Ctrl+X")
        edit_menu.add_command(label="复制", command=self._copy, accelerator="Ctrl+C")
        edit_menu.add_command(label="粘贴", command=self._paste, accelerator="Ctrl+V")
        edit_menu.add_separator()
        edit_menu.add_command(label="全选", command=self._select_all, accelerator="Ctrl+A")
        menubar.add_cascade(label="编辑", menu=edit_menu)
        
        # 翻译菜单
        translate_menu = Menu(menubar, tearoff=0)
        translate_menu.add_command(label="翻译", command=self.translate, accelerator="F5")
        translate_menu.add_command(label="清空", command=self.clear_all, accelerator="F8")
        translate_menu.add_separator()
        translate_menu.add_command(label="历史记录", command=self.show_history, accelerator="F3")
        menubar.add_cascade(label="翻译", menu=translate_menu)
        
        # 帮助菜单
        help_menu = Menu(menubar, tearoff=0)
        help_menu.add_command(label="关于", command=self._show_about)
        menubar.add_cascade(label="帮助", menu=help_menu)
        
        self.top.config(menu=menubar)

    def _create_text_context_menu(self, text_widget):
        """为文本框创建右键菜单"""
        context_menu = Menu(text_widget, tearoff=0)
        context_menu.add_command(label="撤销", command=lambda: text_widget.edit_undo())
        context_menu.add_command(label="重做", command=lambda: text_widget.edit_redo())
        context_menu.add_separator()
        context_menu.add_command(label="剪切", command=lambda: text_widget.event_generate("<<Cut>>"))
        context_menu.add_command(label="复制", command=lambda: text_widget.event_generate("<<Copy>>"))
        context_menu.add_command(label="粘贴", command=lambda: text_widget.event_generate("<<Paste>>"))
        context_menu.add_separator()
        context_menu.add_command(label="全选", command=lambda: text_widget.tag_add(tk.SEL, "1.0", tk.END))
        
        text_widget.bind("<Button-3>", lambda e: self._show_context_menu(e, context_menu))

    def _show_context_menu(self, event, menu):
        """显示右键菜单"""
        menu.post(event.x_root, event.y_root)

    def _bind_shortcuts(self):
        """绑定快捷键"""
        self.top.bind("<F5>", lambda e: self.translate())
        self.top.bind("<F8>", lambda e: self.clear_all())
        self.top.bind("<F3>", lambda e: self.show_history())
        self.top.bind("<Control-n>", lambda e: self.clear_all())
        self.top.bind("<Control-a>", lambda e: self._select_all())

    def _focused_text(self):
        """获取当前焦点所在的文本框"""
        focused = self.top.focus_get()
        if focused in (self.original_text, self.now_text):
            return focused
        return self.original_text

    def _cut(self):
        """剪切文本"""
        self._focused_text().event_generate("<<Cut>>")

    def _copy(self):
        """复制文本"""
        self._focused_text().event_generate("<<Copy>>")

    def _paste(self):
        """粘贴文本"""
        self._focused_text().event_generate("<<Paste>>")

    def _undo(self):
        """撤销"""
        try:
            self._focused_text().edit_undo()
        except:
            pass

    def _redo(self):
        """重做"""
        try:
            self._focused_text().edit_redo()
        except:
            pass

    def _select_all(self, event=None):
        """全选文本"""
        self._focused_text().tag_add(tk.SEL, "1.0", tk.END)
        return "break"

    def _show_about(self):
        """显示关于对话框"""
        messagebox.showinfo("关于", 
            "翻译助手 v1.0\n\n"
            "基于DeepL的在线翻译工具\n"
            "支持多种语言互译\n\n"
            "作者: chengbeyond")

    def swap_languages_animated(self):
        """带动画的交换源语言和目标语言"""
        # 播放旋转动画
        self.animation_manager.swap_button_rotate(self.swap_btn)

        # 延迟执行交换逻辑
        self.top.after(400, self.swap_languages)

    def swap_languages(self):
        """交换源语言和目标语言"""
        from_lang = self.from_lang_combo.get()
        to_lang = self.to_lang_combo.get()
        if from_lang != '自动检测':
            self.from_lang_combo.set(to_lang)
            self.to_lang_combo.set(from_lang)
            # 如果有文本，自动触发翻译
            if self.original_text.get(1.0, tk.END).strip():
                self.translate()

    def clear_all(self):
        """清空所有文本"""
        self.original_text.delete(1.0, tk.END)
        self.now_text.delete(1.0, tk.END)
        self.status_bar.set("已清空", "success")

    def copy_text(self, text_widget):
        """复制指定文本框的内容"""
        text = text_widget.get(1.0, tk.END).strip()
        if text:
            pyperclip.copy(text)
            self.status_bar.set("文本已复制到剪贴板", "success")

    def translate_animated(self):
        """带动画的翻译"""
        # 按钮点击动画
        self.animation_manager.button_click_scale(self.translateBtn)

        # 延迟执行翻译
        self.top.after(100, self.translate)

    def translate(self):
        """执行翻译"""
        try:
            # 获取原文
            original_text = self.original_text.get(1.0, tk.END).strip()
            if not original_text:
                return

            # 开始加载动画
            self.animation_manager.loading_dots(self.translateBtn, "翻译中")

            # 设置状态（使用动画）
            self.animation_manager.status_slide_in(self.status_bar, "正在翻译...", "info")
            self.translateBtn.configure(state='disabled')

            # 在后台线程中执行翻译
            threading.Thread(target=self._do_translate_async,
                          args=(original_text,),
                          daemon=True).start()

        except Exception as e:
            self.animation_manager.stop_loading(self.translateBtn, "翻译")
            self.animation_manager.status_slide_in(self.status_bar, f"翻译出错: {str(e)}", "error")
            self.translateBtn.configure(state='normal')
            
    def _do_translate_async(self, text):
        """在后台线程中执行翻译
        
        Args:
            text: 要翻译的文本
        """
        try:
            # 获取源语言和目标语言
            from_lang = self.from_lang_combo.get()
            if from_lang == '自动检测':
                from_lang = 'auto'
            to_lang = self.to_lang_combo.get()
            
            # 调用翻译API
            translated_text = translate_text(text, from_lang, to_lang)
            
            # 在主线程中更新UI
            self.top.after(0, self._update_translation_result, translated_text)
            
        except Exception as e:
            # 在主线程中显示错误
            self.top.after(0, self._show_error, str(e))
            
    def _update_translation_result(self, translated_text):
        """更新翻译结果

        Args:
            translated_text: 翻译结果文本
        """
        try:
            # 停止加载动画
            self.animation_manager.stop_loading(self.translateBtn, "翻译")

            # 使用文字逐字出现动画显示翻译结果
            self.animation_manager.text_type_animation(self.now_text, translated_text, speed=3)

            # 如果开启了自动复制
            if self.auto_copy.get():
                pyperclip.copy(translated_text)

            # 保存到历史记录
            source_text = self.original_text.get(1.0, tk.END).strip()
            self.history_manager.add_record(
                source_text,
                translated_text,
                self.from_lang_combo.get(),
                self.to_lang_combo.get()
            )

            # 更新状态（使用动画）
            if self.auto_copy.get():
                self.animation_manager.status_slide_in(self.status_bar, "翻译完成，已复制到剪贴板", "success")
            else:
                self.animation_manager.status_slide_in(self.status_bar, "翻译完成", "success")

        except Exception as e:
            self.animation_manager.stop_loading(self.translateBtn, "翻译")
            self.animation_manager.status_slide_in(self.status_bar, f"更新翻译结果出错: {str(e)}", "error")
        finally:
            # 恢复翻译按钮状态
            self.translateBtn.configure(state='normal')

    def _show_error(self, message):
        """显示错误信息"""
        self.animation_manager.stop_loading(self.translateBtn, "翻译")
        self.animation_manager.status_slide_in(self.status_bar, f"错误: {message}", "error")
        self.translateBtn.configure(state='normal')

    def show_history(self):
        def apply_history(record):
            self.original_text.delete(1.0, tk.END)
            self.original_text.insert(1.0, record['source_text'])
            self.now_text.delete(1.0, tk.END)
            self.now_text.insert(1.0, record['translated_text'])
            self.from_lang_combo.set(record['from_lang'])
            self.to_lang_combo.set(record['to_lang'])

        history_dialog = HistoryDialog(self.top, self.history_manager, apply_history)

    def _schedule_state_save(self):
        """定期保存窗口状态"""
        current_time = int(time.time() * 1000)
        if current_time - self._last_save_time >= self._save_interval:
            self._save_window_state()
            self._last_save_time = current_time
        self.top.after(1000, self._schedule_state_save)
        
    def _save_window_state(self):
        """保存窗口状态到文件"""
        try:
            state = {
                'width': self.top.winfo_width(),
                'height': self.top.winfo_height(),
                'auto_copy': self.auto_copy.get(),
                'from_lang': self.from_lang_combo.get(),
                'to_lang': self.to_lang_combo.get()
            }
            
            # 在后台线程中保存文件
            threading.Thread(
                target=self._write_state_file,
                args=(state,),
                daemon=True
            ).start()
        except:
            pass
            
    def _write_state_file(self, state):
        """在后台线程中写入状态文件
        
        参数:
            state: 要保存的状态字典
        """
        try:
            with open('window_state.json', 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
        except:
            pass

    def _load_window_state(self):
        """加载窗口状态"""
        try:
            with open('window_state.json', 'r', encoding='utf-8') as f:
                state = json.load(f)
                
            # 设置窗口大小
            if 'width' in state and 'height' in state:
                self.top.geometry(f"{state['width']}x{state['height']}")
            
            # 设置自动复制
            if 'auto_copy' in state:
                self.auto_copy.set(state['auto_copy'])
            
            # 设置语言
            if 'from_lang' in state:
                self.from_lang_combo.set(state['from_lang'])
            if 'to_lang' in state:
                self.to_lang_combo.set(state['to_lang'])
        except:
            # 设置默认窗口大小
            self.top.geometry("800x600")



    def _on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        # 获取当前滚动位置
        source = event.widget
        target = self.now_text if source == self.original_text else self.original_text
        
        # Windows下滚轮事件的delta值需要除以120
        delta = -1 * (event.delta // 120)
        
        # 同步滚动两个文本框
        source.yview_scroll(delta, "units")
        target.yview_scroll(delta, "units")
        return "break"

    def _on_up_down(self, event):
        """处理上下键事件"""
        source = event.widget
        target = self.now_text if source == self.original_text else self.original_text
        
        if event.keysym == "Up":
            delta = -1
        else:
            delta = 1
            
        source.yview_scroll(delta, "units")
        target.yview_scroll(delta, "units")
        return "break"

    def _on_page_updown(self, event):
        """处理Page Up/Down事件"""
        source = event.widget
        target = self.now_text if source == self.original_text else self.original_text
        
        if event.keysym == "Prior":  # Page Up
            delta = -1
        else:  # Page Down
            delta = 1
            
        source.yview_scroll(delta, "pages")
        target.yview_scroll(delta, "pages")
        return "break"

    def on_language_change(self, event):
        """语言选择改变事件"""
        pass

# Material Design风格历史记录对话框
class HistoryDialog(tk.Toplevel):
    def __init__(self, parent, history_manager, apply_callback):
        super().__init__(parent)
        self.title("翻译历史记录")
        self.geometry("850x550")
        self.minsize(700, 450)
        self.configure(bg=UI['bg'])
        self.history_manager = history_manager
        self.apply_callback = apply_callback

        # 创建界面
        self._create_widgets()
        self._load_history()

        # 设置模态
        self.transient(parent)
        self.grab_set()
        self.protocol("WM_DELETE_WINDOW", self.destroy)

    def _create_widgets(self):
        """创建简约商务风格界面组件"""
        # 主容器
        main_container = tk.Frame(self, bg=UI['bg'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=UI['padding'], pady=UI['padding'])

        # 标题区域
        title_section = ttk.Frame(main_container, style='Section.TFrame')
        title_section.pack(fill=tk.X, pady=(0, UI['layout']['section_spacing']))

        title_content = tk.Frame(title_section, bg=UI['section_bg'])
        title_content.pack(fill=tk.X, padx=UI['section']['padding'], pady=UI['section']['padding'])

        title_label = tk.Label(title_content, text="翻译历史记录",
                              font=UI['title_font'],
                              bg=UI['section_bg'],
                              fg=UI['fg'])
        title_label.pack(side=tk.LEFT)

        # 工具栏按钮 - 优化布局
        toolbar = tk.Frame(title_content, bg=UI['section_bg'])
        toolbar.pack(side=tk.RIGHT)

        # 刷新按钮
        refresh_btn = ttk.Button(toolbar, text="刷新",
                            style='Secondary.TButton',
                            command=self._load_history)
        refresh_btn.pack(side=tk.LEFT, padx=(0, UI['layout']['button_spacing']))

        # 清空按钮
        clear_btn = ttk.Button(toolbar, text="清空历史",
                            style='Secondary.TButton',
                            command=self._clear_history)
        clear_btn.pack(side=tk.LEFT)

        # 表格区域
        table_section = ttk.Frame(main_container, style='Section.TFrame')
        table_section.pack(fill=tk.BOTH, expand=True)

        table_content = tk.Frame(table_section, bg=UI['section_bg'])
        table_content.pack(fill=tk.BOTH, expand=True, padx=UI['section']['padding'], pady=UI['section']['padding'])

        # 创建Treeview
        columns = ("时间", "源语言", "目标语言", "原文", "译文")
        self.tree = ttk.Treeview(table_content, columns=columns, show="headings")

        # 设置字体和样式
        style = ttk.Style()
        style.configure("Treeview",
                       font=UI['font'],
                       background=UI['text_bg'],
                       foreground=UI['text_fg'],
                       fieldbackground=UI['text_bg'],
                       borderwidth=1,
                       relief='solid')
        style.configure("Treeview.Heading",
                       font=UI['button_font'],
                       background=UI['section_bg'],
                       foreground=UI['fg'],
                       relief='solid',
                       borderwidth=1)

        # 设置行高和选择样式
        style.configure("Treeview", rowheight=25)
        style.map("Treeview",
                 background=[('selected', UI['highlight_bg'])],
                 foreground=[('selected', UI['highlight_fg'])])

        # 设置列标题 - 优化列宽
        column_configs = {
            "时间": {"text": "时间", "width": 140},
            "源语言": {"text": "源语言", "width": 70},
            "目标语言": {"text": "目标语言", "width": 70},
            "原文": {"text": "原文", "width": 250},
            "译文": {"text": "译文", "width": 250}
        }

        for col in columns:
            config = column_configs[col]
            self.tree.heading(col, text=config["text"])
            self.tree.column(col, width=config["width"])

        # 添加滚动条
        scrollbar = ttk.Scrollbar(table_content, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=scrollbar.set)

        # 布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定双击事件
        self.tree.bind("<Double-1>", self._on_item_double_click)

        # 绑定右键菜单
        self.context_menu = tk.Menu(self, tearoff=0,
                                  bg=UI['bg'], fg=UI['fg'],
                                  activebackground=UI['highlight_bg'],
                                  activeforeground=UI['highlight_fg'])
        self.context_menu.add_command(label="应用此记录", command=self._apply_selected)
        self.context_menu.add_command(label="删除此记录", command=self._delete_selected)
        self.tree.bind("<Button-3>", self._show_context_menu)

    def _load_history(self):
        # 清空现有记录
        for item in self.tree.get_children():
            self.tree.delete(item)

        # 加载历史记录
        history = self.history_manager.get_history()
        for i, record in enumerate(history):
            values = (
                record["datetime"],
                LANGUAGES.get(record["from_lang"], record["from_lang"]),
                LANGUAGES.get(record["to_lang"], record["to_lang"]),
                record["source_text"][:50] + ("..." if len(record["source_text"]) > 50 else ""),
                record["translated_text"][:50] + ("..." if len(record["translated_text"]) > 50 else "")
            )
            self.tree.insert("", tk.END, iid=str(i), values=values)

    def _clear_history(self):
        if messagebox.askyesno("确认", "确定要清空所有历史记录吗？"):
            self.history_manager.clear_history()
            self._load_history()

    def _on_item_double_click(self, event):
        self._apply_selected()

    def _apply_selected(self):
        selected = self.tree.selection()
        if not selected:
            return

        # 获取选中的记录
        index = int(selected[0])
        record = self.history_manager.get_history()[index]

        # 调用回调函数
        self.apply_callback(record)

        # 关闭对话框
        self.destroy()

    def _delete_selected(self):
        selected = self.tree.selection()
        if not selected:
            return

        # 获取选中的记录索引
        index = int(selected[0])

        # 删除记录
        if self.history_manager.delete_record(index):
            self._load_history()

    def _show_context_menu(self, event):
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

if __name__ == '__main__':
    # 设置全局异常处理
    def handle_exception(exc_type, exc_value, exc_traceback):
        error_msg = f"发生错误: {str(exc_value)}"
        messagebox.showerror("程序错误", error_msg)

    sys.excepthook = handle_exception

    def translate_text(text, from_lang, to_lang):
        """调用DeepL翻译API"""
        # 根据显示名称查找对应的语言代码
        def get_lang_code(lang_name):
            for code, name in LANGUAGES.items():
                if name == lang_name:
                    return code
            return lang_name
        
        # 转换语言代码
        source_lang = from_lang if from_lang == 'auto' else get_lang_code(from_lang)
        target_lang = get_lang_code(to_lang)
        
        # 自动检测语言
        if source_lang == 'auto':
            # 检查文本包含的字符类型
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)
            has_japanese = any('\u3040' <= char <= '\u30ff' for char in text)
            
            # 只有检测到中文或日语时才设置源语言
            if has_chinese:
                source_lang = 'ZH'
            elif has_japanese:
                source_lang = 'JA'
            # 如果无法识别是中文或日语，保持auto让API自动检测
        
        payload = {
            "text": text,
            "source_lang": source_lang.upper(),
            "target_lang": target_lang.upper()
        }
        
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        
        try:
            print(f"发送请求到: {API['url']}")
            print(f"请求参数: {payload}")
            
            response = requests.post(API['url'], headers=headers, json=payload, verify=False, timeout=10)
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code != 200:
                raise Exception(f"API返回错误: {response.status_code}\n响应内容: {response.text}")
            
            data = response.json()
            if 'data' not in data:
                raise Exception(f"API返回格式错误: {data}")
            return data['data']
            
        except requests.RequestException as e:
            raise Exception(f"翻译请求失败: {str(e)}")
        except (KeyError, json.JSONDecodeError) as e:
            raise Exception(f"解析翻译结果失败: {str(e)}\n响应内容: {response.text if 'response' in locals() else '无响应'}")
    
    vp_start_gui()
