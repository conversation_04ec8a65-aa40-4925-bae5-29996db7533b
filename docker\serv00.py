import requests
from bs4 import BeautifulSoup
import time
import os
import json
from flask import Flask, jsonify

app = Flask(__name__)

# 定义请求头
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'}

# 发送请求并解析页面
def fetch_data():
    url = 'https://www.serv00.com/'
    try:
        response = requests.get(url, headers=headers)
        soup = BeautifulSoup(response.text, 'html.parser')
        span_element = soup.find('span', class_='button is-large is-flexible')
        if span_element:
            i_element = span_element.find('i', class_='fa fa-fw fa-users')
            if i_element:
                # 提取span元素中的文本内容
                text_content = span_element.get_text(strip=True)
                # 从文本内容中提取数字部分
                import re
                match = re.search(r'(\d+)\s*/\s*(\d+)', text_content)
                if match:
                    return {"current": match.group(1), "total": match.group(2)}
                else:
                    return {"error": "No numbers found"}
            else:
                return {"error": "i element not found"}
        else:
            return {"error": "span element not found"}
    except Exception as e:
        return {"error": str(e)}


@app.route('/', methods=['GET'])
def index():
    return jsonify({
        "message": "Serv00 API is running",
        "endpoints": ["/data", "/health"]
    })

@app.route('/data', methods=['GET'])
def get_data():
    result = fetch_data()
    return jsonify(result)


@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({"status": "ok"})


if __name__ == "__main__":
    port = int(os.environ.get('PORT', 6288))
    app.run(debug=False, host="0.0.0.0", port=port)

