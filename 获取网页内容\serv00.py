import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import requests
from bs4 import BeautifulSoup
import re
import logging
import json
import os
from datetime import datetime
import threading
import time
import csv

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler("scraper.log", encoding='utf-8'), logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

class WebScraper:
    def __init__(self, root):
        self.root = root
        self.root.title("Serv00 数据监控工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置主题色
        self.colors = {
            'primary': '#2196F3',
            'secondary': '#FFC107',
            'background': '#F5F5F5',
            'text': '#212121',
            'success': '#4CAF50',
            'error': '#F44336'
        }

        # 变量
        self.url = tk.StringVar(value="https://www.serv00.com/")
        self.auto_refresh = tk.BooleanVar(value=False)
        self.refresh_interval = tk.IntVar(value=60)
        self.current_data = tk.StringVar(value="尚未获取数据")
        self.status = tk.StringVar(value="就绪")
        self.current_value = tk.IntVar(value=0)
        self.max_value = tk.IntVar(value=100)
        self.history = []
        self.refresh_thread = None
        self.is_running = False

        # 设置样式
        self.setup_styles()
        
        # 创建UI
        self.create_ui()
        
        # 加载历史记录
        self.load_history()

        # 定义请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'
        }

    def setup_styles(self):
        self.style = ttk.Style()
        self.style.configure("Custom.TFrame", background=self.colors['background'])
        self.style.configure("Custom.TLabel",
                           background=self.colors['background'],
                           foreground=self.colors['text'],
                           font=("Arial", 10))
        self.style.configure("Header.TLabel",
                           font=("Arial", 16, "bold"),
                           foreground=self.colors['primary'])
        self.style.configure("Custom.Horizontal.TProgressbar",
                           background=self.colors['primary'],
                           troughcolor=self.colors['background'])

    def create_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, style="Custom.TFrame", padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题区域
        self.create_header(main_frame)
        
        # URL和控制区域
        self.create_control_panel(main_frame)
        
        # 数据显示区域
        self.create_data_display(main_frame)
        
        # 历史记录区域
        self.create_history_panel(main_frame)
        
        # 状态栏
        self.create_status_bar(main_frame)

    def create_header(self, parent):
        header_frame = ttk.Frame(parent, style="Custom.TFrame")
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(header_frame,
                 text="Serv00 数据监控工具",
                 style="Header.TLabel").pack(side=tk.LEFT)

    def create_control_panel(self, parent):
        control_frame = ttk.Frame(parent, style="Custom.TFrame")
        control_frame.pack(fill=tk.X, pady=(0, 20))

        # URL输入框
        url_frame = ttk.Frame(control_frame, style="Custom.TFrame")
        url_frame.pack(fill=tk.X)
        ttk.Label(url_frame, text="监控地址:", style="Custom.TLabel").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Entry(url_frame, textvariable=self.url, width=50).pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 控制按钮
        button_frame = ttk.Frame(control_frame, style="Custom.TFrame")
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 使用tk.Button替代ttk.Button
        tk.Button(button_frame,
                 text="获取数据",
                 font=("Arial", 10, "bold"),
                 bg=self.colors['primary'],
                 fg="white",
                 relief="raised",
                 padx=10,
                 pady=5,
                 command=self.fetch_data_once).pack(side=tk.LEFT, padx=(0, 10))
                  
        ttk.Checkbutton(button_frame,
                       text="自动刷新",
                       variable=self.auto_refresh,
                       command=self.toggle_auto_refresh).pack(side=tk.LEFT, padx=(0, 10))
                       
        ttk.Label(button_frame,
                 text="刷新间隔(秒):",
                 style="Custom.TLabel").pack(side=tk.LEFT, padx=(0, 5))
                 
        ttk.Spinbox(button_frame,
                   from_=5,
                   to=3600,
                   textvariable=self.refresh_interval,
                   width=5).pack(side=tk.LEFT, padx=(0, 10))
                   
        tk.Button(button_frame,
                 text="导出数据",
                 font=("Arial", 10, "bold"),
                 bg=self.colors['primary'],
                 fg="white",
                 relief="raised",
                 padx=10,
                 pady=5,
                 command=self.export_data).pack(side=tk.LEFT)

    def create_data_display(self, parent):
        data_frame = ttk.Frame(parent, style="Custom.TFrame")
        data_frame.pack(fill=tk.X, pady=(0, 20))

        # 当前数据显示
        ttk.Label(data_frame,
                 text="当前数据:",
                 style="Custom.TLabel").pack(side=tk.LEFT)
        ttk.Label(data_frame,
                 textvariable=self.current_data,
                 font=("Arial", 24, "bold"),
                 foreground=self.colors['primary']).pack(side=tk.LEFT, padx=10)

        # 进度条
        self.progress = ttk.Progressbar(data_frame,
                                      style="Custom.Horizontal.TProgressbar",
                                      length=300,
                                      mode='determinate')
        self.progress.pack(side=tk.LEFT, fill=tk.X, expand=True)

    def create_history_panel(self, parent):
        history_frame = ttk.Frame(parent, style="Custom.TFrame")
        history_frame.pack(fill=tk.BOTH, expand=True)

        # 历史记录标题
        ttk.Label(history_frame,
                 text="历史记录",
                 style="Custom.TLabel",
                 font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(0, 10))

        # 创建表格和滚动条
        table_frame = ttk.Frame(history_frame)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # 滚动条
        scrollbar = ttk.Scrollbar(table_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Treeview
        columns = ("timestamp", "current", "maximum", "percentage")
        self.history_tree = ttk.Treeview(table_frame,
                                       columns=columns,
                                       show="headings",
                                       height=10)
        
        # 设置列
        self.history_tree.heading("timestamp", text="时间", anchor="center")
        self.history_tree.heading("current", text="当前人数", anchor="center")
        self.history_tree.heading("maximum", text="最大人数", anchor="center")
        self.history_tree.heading("percentage", text="占用率", anchor="center")
        
        # 设置列宽和对齐方式
        self.history_tree.column("timestamp", width=150, anchor="center")
        self.history_tree.column("current", width=100, anchor="center")
        self.history_tree.column("maximum", width=100, anchor="center")
        self.history_tree.column("percentage", width=100, anchor="center")

        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.history_tree.yview)
        self.history_tree.config(yscrollcommand=scrollbar.set)

    def create_status_bar(self, parent):
        status_frame = ttk.Frame(parent, style="Custom.TFrame")
        status_frame.pack(fill=tk.X, pady=(20, 0))
        ttk.Label(status_frame,
                 text="状态:",
                 style="Custom.TLabel").pack(side=tk.LEFT)
        ttk.Label(status_frame,
                 textvariable=self.status,
                 style="Custom.TLabel").pack(side=tk.LEFT, padx=5)

    def fetch_data(self):
        self.status.set("获取数据中...")
        try:
            url = self.url.get()
            logger.info(f"正在从 {url} 获取数据")

            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')
            span_element = soup.find('span', class_='button is-large is-flexible')

            if span_element:
                i_element = span_element.find('i', class_='fa fa-fw fa-users')
                if i_element:
                    text_content = span_element.get_text(strip=True)
                    match = re.search(r'(\d+)\s*/\s*(\d+)', text_content)
                    if match:
                        current = int(match.group(1))
                        maximum = int(match.group(2))
                        percentage = round((current / maximum) * 100, 1)
                        
                        # 更新数据显示
                        self.current_data.set(f"{current} / {maximum} ({percentage}%)")
                        self.current_value.set(current)
                        self.max_value.set(maximum)
                        self.progress['value'] = percentage

                        # 更新历史记录
                        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        record = {
                            "timestamp": timestamp,
                            "current": current,
                            "maximum": maximum,
                            "percentage": f"{percentage}%"
                        }
                        self.history.append(record)
                        self.history_tree.insert("", 0, values=(
                            timestamp,
                            current,
                            maximum,
                            f"{percentage}%"
                        ))

                        # 保留最近的100条记录
                        if len(self.history) > 100:
                            self.history.pop(0)
                            # 删除最旧的树项
                            last_item = self.history_tree.get_children()[-1]
                            self.history_tree.delete(last_item)

                        self.status.set(f"数据获取成功: {current}/{maximum}")
                        logger.info(f"数据获取成功: {current}/{maximum}")
                        return record
                    
            self.status.set("未找到数据")
            logger.warning("未找到数据")
            return None
            
        except requests.RequestException as e:
            error_msg = f"请求错误: {str(e)}"
            self.status.set(error_msg)
            logger.error(error_msg)
            messagebox.showerror("错误", error_msg)
            return None
        except Exception as e:
            error_msg = f"发生错误: {str(e)}"
            self.status.set(error_msg)
            logger.error(error_msg, exc_info=True)
            messagebox.showerror("错误", error_msg)
            return None

    def fetch_data_once(self):
        self.fetch_data()

    def auto_refresh_task(self):
        while self.is_running and self.auto_refresh.get():
            self.fetch_data()
            for i in range(self.refresh_interval.get()):
                if not self.is_running or not self.auto_refresh.get():
                    break
                time.sleep(1)

    def toggle_auto_refresh(self):
        if self.auto_refresh.get():
            self.is_running = True
            self.refresh_thread = threading.Thread(target=self.auto_refresh_task, daemon=True)
            self.refresh_thread.start()
            self.status.set(f"自动刷新已启动 (间隔: {self.refresh_interval.get()}秒)")
            logger.info(f"自动刷新已启动 (间隔: {self.refresh_interval.get()}秒)")
        else:
            self.is_running = False
            if self.refresh_thread:
                self.refresh_thread.join(timeout=1)
            self.status.set("自动刷新已停止")
            logger.info("自动刷新已停止")

    def save_history(self):
        if not self.history:
            messagebox.showwarning("警告", "没有历史数据可保存")
            return

        try:
            with open('history.json', 'w', encoding='utf-8') as f:
                json.dump(self.history, f, ensure_ascii=False, indent=2)
            self.status.set("历史记录已保存")
            logger.info("历史记录已保存到history.json")
        except Exception as e:
            error_msg = f"保存历史记录失败: {str(e)}"
            self.status.set(error_msg)
            logger.error(error_msg)
            messagebox.showerror("错误", error_msg)

    def load_history(self):
        try:
            if os.path.exists('history.json'):
                with open('history.json', 'r', encoding='utf-8') as f:
                    self.history = json.load(f)
                
                # 清除现有树项
                for item in self.history_tree.get_children():
                    self.history_tree.delete(item)
                
                # 添加历史记录到树
                for record in reversed(self.history[-100:]):  # 只显示最近100条
                    self.history_tree.insert("", "end", values=(
                        record['timestamp'],
                        record['current'],
                        record['maximum'],
                        record['percentage']
                    ))
                logger.info("历史记录已加载")
        except Exception as e:
            logger.error(f"加载历史记录失败: {str(e)}")

    def export_data(self):
        if not self.history:
            messagebox.showwarning("警告", "没有数据可导出")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="导出数据"
            )
            
            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=['timestamp', 'current', 'maximum', 'percentage'])
                    writer.writeheader()
                    writer.writerows(self.history)
                
                self.status.set(f"数据已导出到: {file_path}")
                logger.info(f"数据已导出到: {file_path}")
                messagebox.showinfo("成功", "数据导出完成！")
        except Exception as e:
            error_msg = f"导出数据失败: {str(e)}"
            self.status.set(error_msg)
            logger.error(error_msg)
            messagebox.showerror("错误", error_msg)

if __name__ == "__main__":
    try:
        root = tk.Tk()
        app = WebScraper(root)
        root.mainloop()
    except Exception as e:
        logger.error(f"程序启动失败: {str(e)}", exc_info=True)
        messagebox.showerror("错误", f"程序启动失败: {str(e)}")
