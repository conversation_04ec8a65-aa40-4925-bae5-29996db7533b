// ==UserScript==
// @name         获取页面dom前端代码(带可视化面板)
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  右键获取DOM元素选择器，支持控制台输出和页面内可视化显示面板
// <AUTHOR> by AI
// @match        *://*/*
// @grant        none
// ==/UserScript==

// 显示管理器类 - 负责页面内的可视化显示
class DisplayManager {
    constructor() {
        this.panel = null;
        this.isMinimized = true;
        this.history = [];
        this.maxHistory = 5;
        this.init();
        // 初始化时设置为最小化状态
        this.panel.style.display = 'none'; // 初始直接隐藏
        this.panel.classList.add('minimized');
    }

    init() {
        this.createPanel();
        this.addStyles();
        this.bindEvents();
    }

    createPanel() {
        // 创建主容器
        this.panel = document.createElement('div');
        this.panel.id = 'element-inspector-panel';
        this.panel.innerHTML = `
            <div class="panel-header">
                <span class="panel-title">🎯 元素选择器</span>
                <div class="panel-controls">
                    <button class="btn-minimize" title="最小化/展开">−</button>
                    <button class="btn-clear" title="清空历史">🗑</button>
                    <button class="btn-close" title="关闭面板">×</button>
                </div>
            </div>
            <div class="panel-content">
                <div class="welcome-message">右键点击页面元素查看选择器信息</div>
            </div>
        `;
        this.panel.style.display = 'none'; // 初始直接隐藏
        document.body.appendChild(this.panel);
    }

    addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            #element-inspector-panel {
                position: fixed;
                top: 20px;
                left: 20px;
                width: 400px;
                max-height: 500px;
                background: rgba(255, 255, 255, 0.3);
                border: 1px solid rgba(221, 221, 221, 0.5);
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 12px;
                z-index: 999999;
                backdrop-filter: blur(10px);
                transition: all 0.3s ease;
                opacity: 0.6;
            }

            #element-inspector-panel.has-content {
                background: rgba(255, 255, 255, 0.95);
                border: 1px solid #ddd;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                opacity: 1;
            }

            #element-inspector-panel.minimized {
                width: 200px;
                height: 40px;
                overflow: hidden;
                opacity: 0;
                background: rgba(255, 255, 255, 0.2);
                border: 1px solid rgba(221, 221, 221, 0.3);
            }

            #element-inspector-panel.minimized.has-content {
                opacity: 0.8;
                background: rgba(255, 255, 255, 0.6);
                border: 1px solid rgba(221, 221, 221, 0.6);
            }

            .panel-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 8px 12px;
                border-radius: 8px 8px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: move;
                user-select: none;
            }

            .panel-title {
                font-weight: bold;
                font-size: 13px;
            }

            .panel-controls {
                display: flex;
                gap: 5px;
            }

            .panel-controls button {
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: white;
                width: 20px;
                height: 20px;
                border-radius: 3px;
                cursor: pointer;
                font-size: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background 0.2s;
            }

            .panel-controls button:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            .panel-content {
                padding: 12px;
                max-height: 440px;
                overflow-y: auto;
            }

            .welcome-message {
                text-align: center;
                color: #666;
                font-style: italic;
                padding: 20px;
            }

            .element-info {
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 10px;
                margin-bottom: 10px;
                animation: fadeIn 0.3s ease;
            }

            .element-info:last-child {
                margin-bottom: 0;
            }

            .selector-item {
                margin: 8px 0;
                padding: 6px;
                background: white;
                border-radius: 4px;
                border-left: 3px solid #ddd;
            }

            .selector-item.valid {
                border-left-color: #28a745;
            }

            .selector-item.warning {
                border-left-color: #ffc107;
            }

            .selector-item.error {
                border-left-color: #dc3545;
            }

            .selector-label {
                font-weight: bold;
                color: #495057;
                margin-bottom: 4px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .selector-code {
                background: #f1f3f4;
                padding: 4px 6px;
                border-radius: 3px;
                font-family: inherit;
                word-break: break-all;
                font-size: 11px;
                color: #2d3748;
            }

            .copy-btn {
                background: #007bff;
                color: white;
                border: none;
                padding: 2px 6px;
                border-radius: 3px;
                cursor: pointer;
                font-size: 10px;
                transition: background 0.2s;
            }

            .copy-btn:hover {
                background: #0056b3;
            }

            .copy-btn.copied {
                background: #28a745;
            }

            .summary {
                margin-top: 10px;
                padding: 8px;
                background: #e3f2fd;
                border-radius: 4px;
                font-size: 11px;
                color: #1565c0;
            }

            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            .panel-content::-webkit-scrollbar {
                width: 6px;
            }

            .panel-content::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
            }

            .panel-content::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 3px;
            }

            .panel-content::-webkit-scrollbar-thumb:hover {
                background: #a8a8a8;
            }
        `;
        document.head.appendChild(style);
    }

    bindEvents() {
        const header = this.panel.querySelector('.panel-header');
        const minimizeBtn = this.panel.querySelector('.btn-minimize');
        const clearBtn = this.panel.querySelector('.btn-clear');
        const closeBtn = this.panel.querySelector('.btn-close');

        // 拖拽功能
        let isDragging = false;
        let startX, startY, startLeft, startTop;

        header.addEventListener('mousedown', (e) => {
            if (e.target.tagName === 'BUTTON') return;
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            const rect = this.panel.getBoundingClientRect();
            startLeft = rect.left;
            startTop = rect.top;
            document.addEventListener('mousemove', handleDrag);
            document.addEventListener('mouseup', stopDrag);
        });

        const handleDrag = (e) => {
            if (!isDragging) return;
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            this.panel.style.left = (startLeft + deltaX) + 'px';
            this.panel.style.top = (startTop + deltaY) + 'px';
            this.panel.style.right = 'auto';
        };

        const stopDrag = () => {
            isDragging = false;
            document.removeEventListener('mousemove', handleDrag);
            document.removeEventListener('mouseup', stopDrag);
        };

        // 最小化/展开
        minimizeBtn.addEventListener('click', () => {
            this.toggleMinimize();
        });

        // 双击标题栏切换最小化
        header.addEventListener('dblclick', () => {
            this.toggleMinimize();
        });

        // 清空历史
        clearBtn.addEventListener('click', () => {
            this.clearHistoryWithConfirm();
        });

        // 关闭面板
        closeBtn.addEventListener('click', () => {
            this.panel.style.display = 'none';
        });

        // 事件委托处理各种按钮
        this.panel.addEventListener('click', (e) => {
            if (e.target.classList.contains('copy-btn')) {
                this.copyToClipboard(e.target.dataset.text, e.target);
            } else if (e.target.classList.contains('clear-all-btn')) {
                this.clearHistoryWithConfirm();
            } else if (e.target.classList.contains('confirm-clear-btn')) {
                this.clearHistory();
            } else if (e.target.classList.contains('cancel-clear-btn')) {
                this.renderHistory(); // 返回历史记录显示
            }
        });
    }

    toggleMinimize() {
        this.isMinimized = !this.isMinimized;
        this.panel.classList.toggle('minimized', this.isMinimized);
        const btn = this.panel.querySelector('.btn-minimize');
        btn.textContent = this.isMinimized ? '+' : '−';
        btn.title = this.isMinimized ? '展开' : '最小化';
        if (!this.isMinimized) {
            this.panel.style.display = 'block';
        }
    }

    clearHistoryWithConfirm() {
        if (this.history.length === 0) {
            this.showTip('没有历史记录需要清空', 'info');
            return;
        }

        // 显示确认按钮而不是弹框
        this.showConfirmClear();
    }

    showConfirmClear() {
        // 在面板内显示确认界面
        const content = this.panel.querySelector('.panel-content');
        const currentCount = this.history.length;

        content.innerHTML = `
            <div style="text-align: center; padding: 20px;">
                <div style="font-size: 16px; margin-bottom: 15px;">🗑️</div>
                <div style="margin-bottom: 15px; color: #495057;">
                    确定要清空 <strong>${currentCount}</strong> 条历史记录吗？
                </div>
                <div style="display: flex; gap: 10px; justify-content: center;">
                    <button class="confirm-clear-btn" style="
                        background: #dc3545;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 12px;
                    ">确定清空</button>
                    <button class="cancel-clear-btn" style="
                        background: #6c757d;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 12px;
                    ">取消</button>
                </div>
            </div>
        `;
    }

    clearHistory() {
        this.history = [];
        const content = this.panel.querySelector('.panel-content');
        content.innerHTML = '<div class="welcome-message">右键点击页面元素查看选择器信息</div>';

        // 清空后恢复到初始状态：半透明且最小化
        this.panel.classList.remove('has-content');
        if (!this.isMinimized) {
            this.toggleMinimize();
        }

        this.showTip('✅ 历史记录已清空', 'success');
    }

    showTip(message, type = 'info') {
        // 在面板右上角显示小提示
        const tip = document.createElement('div');
        const bgColor = type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8';

        tip.style.cssText = `
            position: fixed;
            top: 20px;
            left: 440px;
            background: ${bgColor};
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000000;
            pointer-events: none;
            animation: tipSlideIn 3s ease-in-out forwards;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        `;
        tip.textContent = message;

        // 添加动画样式
        if (!document.querySelector('#tip-animation-style')) {
            const animationStyle = document.createElement('style');
            animationStyle.id = 'tip-animation-style';
            animationStyle.textContent = `
                @keyframes tipSlideIn {
                    0% { opacity: 0; transform: translateX(-20px); }
                    15% { opacity: 1; transform: translateX(0); }
                    85% { opacity: 1; transform: translateX(0); }
                    100% { opacity: 0; transform: translateX(-20px); }
                }
            `;
            document.head.appendChild(animationStyle);
        }

        document.body.appendChild(tip);

        // 3秒后自动移除
        setTimeout(() => {
            if (tip.parentNode) {
                tip.parentNode.removeChild(tip);
            }
        }, 3000);
    }

    displayElementInfo(elementData) {
        // 确保面板可见
        this.panel.style.display = 'block';

        // 添加到历史记录
        this.history.unshift(elementData);
        if (this.history.length > this.maxHistory) {
            this.history = this.history.slice(0, this.maxHistory);
        }

        // 有内容时自动展开并变为不透明
        if (this.isMinimized) {
            this.toggleMinimize();
        }
        this.panel.classList.add('has-content');

        this.renderHistory();
    }

    renderHistory() {
        const content = this.panel.querySelector('.panel-content');
        if (this.history.length === 0) {
            content.innerHTML = '<div class="welcome-message">右键点击页面元素查看选择器信息</div>';
            return;
        }

        const historyHtml = this.history.map((data, index) => {
            const timestamp = new Date(data.timestamp).toLocaleTimeString();
            return `
                <div class="element-info">
                    <div style="font-weight: bold; margin-bottom: 8px; color: #495057;">
                        ${index === 0 ? '🎯 ' : ''}元素 #${this.history.length - index}
                        <span style="font-size: 10px; color: #6c757d;">${timestamp}</span>
                    </div>

                    ${this.renderSelector('ID', data.id.code, data.id.status, data.id.available)}
                    ${this.renderSelector('CSS', data.css.code, data.css.status, true)}
                    ${this.renderSelector('XPath', data.xpath.code, data.xpath.status, true)}

                    <div class="summary">${data.summary}</div>
                </div>
            `;
        }).join('');

        // 添加清空按钮到内容区域
        const clearButtonHtml = `
            <div style="text-align: center; margin-top: 15px; padding-top: 10px; border-top: 1px solid #e9ecef;">
                <button class="clear-all-btn" style="
                    background: #dc3545;
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 11px;
                    transition: background 0.2s;
                " onmouseover="this.style.background='#c82333'" onmouseout="this.style.background='#dc3545'">
                    🗑 清空所有历史记录 (${this.history.length}条)
                </button>
            </div>
        `;

        content.innerHTML = historyHtml + clearButtonHtml;
    }

    renderSelector(type, code, status, available) {
        if (!available) {
            return `
                <div class="selector-item error">
                    <div class="selector-label">
                        ${type}: 无${type} ${status}
                    </div>
                </div>
            `;
        }

        const statusClass = status === '✅' ? 'valid' : status === '⚠️' ? 'warning' : 'error';

        return `
            <div class="selector-item ${statusClass}">
                <div class="selector-label">
                    ${type}: ${status}
                    <button class="copy-btn" data-text="${code}" title="复制${type}选择器">复制</button>
                </div>
                <div class="selector-code">${this.escapeHtml(code)}</div>
            </div>
        `;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    copyToClipboard(text, button) {
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(() => {
                this.showCopySuccess(button);
            }).catch(() => {
                this.showCopyFallback(text);
            });
        } else {
            this.showCopyFallback(text);
        }
    }

    showCopySuccess(button) {
        const originalText = button.textContent;
        button.textContent = '已复制';
        button.classList.add('copied');
        setTimeout(() => {
            button.textContent = originalText;
            button.classList.remove('copied');
        }, 1000);
    }

    showCopyFallback(text) {
        // 创建临时文本框进行复制
        const textarea = document.createElement('textarea');
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        try {
            document.execCommand('copy');
            console.log('✅ 已复制到剪贴板');
        } catch (err) {
            console.log('⚠️ 复制失败，请手动复制:', text);
        }
        document.body.removeChild(textarea);
    }
}

class ElementInspector {
    constructor() {
        this.cache = new Map();
        this.displayManager = new DisplayManager();
        this.isDisplayEnabled = false; // 初始化时禁用显示层
        this.bindKeyEvents();
    }

    bindKeyEvents() {
        document.addEventListener('keydown', (event) => {
            if (event.key === 's' || event.key === 'S') {
                this.isDisplayEnabled = !this.isDisplayEnabled;
                console.log(this.isDisplayEnabled ? '✅ 已启用右键显示层功能' : '❌ 已禁用右键显示层功能');
                if (!this.isDisplayEnabled) {
                    this.displayManager.panel.style.display = 'none';
                }
            }
        });
    }

    logElementInfo(element) {
        if (!this.isDisplayEnabled) {
            console.log('⚠️ 右键显示层功能已禁用，按S键启用');
            return;
        }
        const css = this.getBestSelector(element);
        const xpath = this.getXPath(element);

        // 获取验证状态
        const idStatus = this.validateSelector(element.id, element, 'id');
        const cssStatus = this.validateSelector(css, element, 'css');
        const xpathStatus = this.validateSelector(xpath, element, 'xpath');

        // 控制台输出（保持原有功能）
        const output = `🎯 元素选择器代码：
ID: ${element.id ? `document.getElementById('${element.id}')` : '无ID'} ${idStatus}
CSS: document.querySelector('${css}') ${cssStatus}
XPath: document.evaluate('${xpath}', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue ${xpathStatus}`;

        console.log(output);

        // 验证结果总结
        const validCount = [idStatus, cssStatus, xpathStatus].filter(status => status === '✅').length;
        const warningCount = [idStatus, cssStatus, xpathStatus].filter(status => status === '⚠️').length;
        const errorCount = [idStatus, cssStatus, xpathStatus].filter(status => status === '❌').length;

        let summary;
        if (validCount === 3) {
            summary = '🎉 所有选择器验证通过！';
        } else if (validCount > 0) {
            summary = `✅ ${validCount}个选择器验证通过${warningCount > 0 ? `，${warningCount}个选择器可用但不唯一` : ''}${errorCount > 0 ? `，${errorCount}个选择器验证失败` : ''}`;
        } else {
            summary = '⚠️ 所有选择器验证失败，请检查元素状态';
        }

        console.log(summary);

        // 准备可视化面板数据
        const elementData = {
            timestamp: Date.now(),
            id: {
                code: element.id ? `document.getElementById('${element.id}')` : '',
                status: idStatus,
                available: !!element.id
            },
            css: {
                code: `document.querySelector('${css}')`,
                status: cssStatus,
                available: true
            },
            xpath: {
                code: `document.evaluate('${xpath}', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue`,
                status: xpathStatus,
                available: true
            },
            summary: summary
        };

        // 显示到可视化面板
        this.displayManager.displayElementInfo(elementData);

        // 自动复制CSS选择器到剪贴板（最常用）
        this.copyToClipboard(`document.querySelector('${css}')`);
    }

    copyToClipboard(text) {
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(() => {
                console.log('✅ CSS选择器已复制到剪贴板');
            }).catch(() => {
                console.log('⚠️ 复制失败，请手动复制');
            });
        } else {
            console.log('⚠️ 当前环境不支持自动复制，请手动复制');
        }
    }

    validateSelector(selector, element, type) {
        try {
            if (type === 'css') {
                // 优化：使用一次querySelectorAll同时检查匹配和唯一性
                const results = document.querySelectorAll(selector);
                if (results.length === 1 && results[0] === element) {
                    return '✅';
                } else if (results.length > 1 && Array.from(results).includes(element)) {
                    return '⚠️';
                }
                return '❌';
            } else if (type === 'xpath') {
                const found = document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                return found === element ? '✅' : '❌';
            } else if (type === 'id') {
                if (!element.id) return '❌';
                const found = document.getElementById(element.id);
                return found === element ? '✅' : '❌';
            }
            return '❌';
        } catch (e) {
            return '❌';
        }
    }

    getBestSelector(element) {
        if (element.id) return `#${CSS.escape(element.id)}`;

        const tag = element.tagName.toLowerCase();
        const attrs = Array.from(element.attributes);

        // 尝试单个属性选择器
        const tryAttrs = [...attrs.filter(a => a.name.startsWith('data-')),
                         ...['name', 'role', 'type', 'aria-label', 'title', 'placeholder']
                           .map(name => ({name, value: element.getAttribute(name)}))
                           .filter(a => a.value)];

        for (const attr of tryAttrs) {
            const selector = `${tag}[${attr.name}="${CSS.escape(attr.value)}"]`;
            if (this.isUnique(selector)) return selector;
        }

        // 尝试组合data属性
        const dataAttrs = attrs.filter(a => a.name.startsWith('data-'));
        for (let i = 0; i < dataAttrs.length - 1; i++) {
            for (let j = i + 1; j < dataAttrs.length; j++) {
                const selector = `${tag}[${dataAttrs[i].name}="${CSS.escape(dataAttrs[i].value)}"][${dataAttrs[j].name}="${CSS.escape(dataAttrs[j].value)}"]`;
                if (this.isUnique(selector)) return selector;
            }
        }

        // 类名选择器
        if (element.className) {
            const selector = `${tag}.${element.className.trim().split(/\s+/).map(c => CSS.escape(c)).join('.')}`;
            if (this.isUnique(selector)) return selector;
        }

        return this.getPathSelector(element);
    }

    isUnique(selector) {
        if (this.cache.has(selector)) return this.cache.get(selector);
        try {
            const unique = document.querySelectorAll(selector).length === 1;
            this.cache.set(selector, unique);
            return unique;
        } catch { return false; }
    }

    getPathSelector(element) {
        const path = [];
        let current = element;
        while (current && current !== document.body) {
            const tag = current.tagName.toLowerCase();
            if (current.id) {
                path.unshift(`#${CSS.escape(current.id)}`);
                break;
            }
            const parent = current.parentElement;
            if (parent) {
                const index = Array.from(parent.children).filter(e => e.tagName === current.tagName).indexOf(current) + 1;
                path.unshift(`${tag}:nth-of-type(${index})`);
            } else {
                path.unshift(tag);
            }
            current = parent;
        }
        return path.join(' > ');
    }

    getXPath(element) {
        if (element.id) return `//*[@id="${element.id}"]`;
        const parts = [];
        let current = element;
        while (current?.nodeType === Node.ELEMENT_NODE) {
            const tag = current.nodeName.toLowerCase();
            const index = Array.from(current.parentNode?.children || []).filter(el => el.nodeName === current.nodeName).indexOf(current) + 1;
            parts.unshift(index > 1 ? `${tag}[${index}]` : tag);
            current = current.parentNode;
        }
        return '/' + parts.join('/');
    }
}

(() => {
    'use strict';

    const inspector = new ElementInspector();

    document.addEventListener('contextmenu', event => {
        // event.preventDefault(); // 取消注释可以禁用默认右键菜单
        if (inspector.isDisplayEnabled) {
            inspector.logElementInfo(event.target);
        }
    }, true);
})();
