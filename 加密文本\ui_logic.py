import threading
import traceback
import pyperclip
from ui_texts import TEXTS
from jm import encrypt, decrypt
import customtkinter as ctk

def _clear_output_text(app):
    """辅助函数：清空输出文本框（处理disabled状态）"""
    app.output_text.configure(state="normal")
    app.output_text.delete("1.0", "end")
    app.output_text.configure(state="disabled")

def process_text(app, operation):
    input_text = app.input_text.get("1.0", "end-1c").strip()
    key = app.key_entry.get().strip()
    if not key:
        app.update_status(TEXTS['error_key_empty'], "#f39c12", error=True)
        return
    if not input_text:
        app.update_status(TEXTS['error_text_empty'], "#f39c12", error=True)
        return
    if len(input_text) > 100000:
        app.update_status(TEXTS['error_text_too_long'], "#f39c12", error=True)
        return
    action = TEXTS['encrypt'] if operation == 'encrypt' else TEXTS['decrypt']
    app.update_status(f"🔄 正在{action}", "#3498db", loading=True)
    def worker():
        try:
            result = (encrypt if operation == 'encrypt' else decrypt)(input_text, key)
            app.root.after(0, lambda: handle_result(app, result, operation))
        except Exception as e:
            traceback.print_exc()
            msg = TEXTS['error_encrypt'] if operation == 'encrypt' else TEXTS['error_decrypt']
            app.root.after(0, lambda: app.update_status(f"{msg}{e}", "#e74c3c", error=True))
    threading.Thread(target=worker, daemon=True).start()

def handle_result(app, result, operation):
    app.output_text.delete("1.0", "end")
    app.output_text.configure(state="normal")
    app.output_text.insert("1.0", "")
    def fade_in(text, idx=0):
        if idx > len(text):
            return
        app.output_text.delete("1.0", "end")
        app.output_text.insert("1.0", text[:idx])
        app.output_text.after(8, lambda: fade_in(text, idx+max(1, len(text)//60)))
    fade_in(result)
    app._loading = False
    if operation == 'decrypt' and ("失败" in result or "错误" in result):
        app.update_status(TEXTS['error_decrypt']+result, "#e74c3c", error=True)
    else:
        msg = TEXTS['success_encrypt'] if operation == 'encrypt' else TEXTS['success_decrypt']
        app.update_status(msg, "#2ecc71")

def copy_result(app):
    try:
        text = app.output_text.get("1.0", "end-1c")
        if text.strip():
            pyperclip.copy(text)
            app.update_status(TEXTS['copied'], "#2ecc71")
        else:
            app.update_status(TEXTS['error_clipboard'], "#f39c12", error=True)
    except Exception:
        import platform
        if platform.system() == "Linux":
            app.update_status(TEXTS['error_clipboard']+"\n请安装 xclip 或 xsel。", "#e74c3c", error=True)
        else:
            app.update_status(TEXTS['error_clipboard'], "#e74c3c", error=True)

def paste_text(app):
    try:
        text = pyperclip.paste()
        if text:
            app.input_text.delete("1.0", "end")
            app.input_text.insert("1.0", text)
            app.update_status(TEXTS['pasted'], "#2ecc71")
        else:
            app.update_status(TEXTS['error_clipboard'], "#f39c12", error=True)
    except Exception:
        import platform
        if platform.system() == "Linux":
            app.update_status(TEXTS['error_clipboard']+"\n请安装 xclip 或 xsel。", "#e74c3c", error=True)
        else:
            app.update_status(TEXTS['error_clipboard'], "#e74c3c", error=True)

def clear_text(app, widget):
    """清空指定的文本组件"""
    if widget == app.output_text:
        _clear_output_text(app)
    else:
        widget.delete("1.0", "end")
    app.update_status(TEXTS['cleared'], "#2ecc71")

def clear_input_and_output(app):
    """清空输入和输出文本框"""
    app.input_text.delete("1.0", "end")
    _clear_output_text(app)
    app.update_status(TEXTS['cleared'], "#2ecc71")

def clear_all(app):
    """清空所有内容"""
    app.input_text.delete("1.0", "end")
    _clear_output_text(app)
    app.key_entry.delete(0, "end")
    app.update_status(TEXTS['cleared_all'], "#2ecc71")

def toggle_theme(app):
    new_mode = "light" if ctk.get_appearance_mode() == "Dark" else "dark"
    ctk.set_appearance_mode(new_mode)
    for btn in getattr(app, 'transparent_buttons', []):
        btn.configure(text_color="#222222" if new_mode == "light" else "#f0f0f0")
    msg = TEXTS['theme_light'] if new_mode == "light" else TEXTS['theme_dark']
    app.update_status(msg, "#2ecc71") 