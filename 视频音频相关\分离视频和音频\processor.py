import os
import subprocess
import platform
import re

# --- Optimized Hardware Check with Caching ---
_gpu_info_cache = None

def check_gpu_support():
    """
    Checks for GPU hardware acceleration support (NVIDIA, AMD, Intel) and caches the result.
    This function is platform-aware and more efficient.
    """
    global _gpu_info_cache
    if _gpu_info_cache is not None:
        return _gpu_info_cache

    gpu_info = {
        'nvidia': False,
        'amd': False,
        'intel': False,
        'recommended_codec': 'libx264'  # Default to CPU encoder
    }

    try:
        # A more reliable check is to see if ffmpeg itself reports the encoder
        result = subprocess.run(['ffmpeg', '-encoders'], capture_output=True, text=True, encoding='utf-8', timeout=10)
        encoders = result.stdout
        
        if 'h264_nvenc' in encoders:
            gpu_info['nvidia'] = True
            gpu_info['recommended_codec'] = 'h264_nvenc'
        elif 'h264_amf' in encoders:
            gpu_info['amd'] = True
            gpu_info['recommended_codec'] = 'h264_amf'
        elif 'h264_qsv' in encoders:
            gpu_info['intel'] = True
            gpu_info['recommended_codec'] = 'h264_qsv'

    except (subprocess.TimeoutExpired, FileNotFoundError):
        # Fallback for when ffmpeg isn't installed or the check fails
        pass

    _gpu_info_cache = gpu_info
    return gpu_info

def get_optimal_codec():
    """
    Gets the best available video codec based on hardware detection.
    Returns the codec name and a user-friendly description.
    """
    gpu_info = check_gpu_support()
    if gpu_info['nvidia']:
        return 'h264_nvenc', 'NVIDIA GPU (NVENC)'
    elif gpu_info['amd']:
        return 'h264_amf', 'AMD GPU (AMF)'
    elif gpu_info['intel']:
        return 'h264_qsv', 'Intel GPU (QuickSync)'
    else:
        return 'libx264', 'CPU (Software)'

def get_video_duration(video_path):
    """Gets video duration in seconds using ffprobe."""
    command = [
        'ffprobe', '-v', 'error', '-show_entries', 'format=duration',
        '-of', 'default=noprint_wrappers=1:nokey=1', video_path
    ]
    try:
        result = subprocess.run(command, capture_output=True, text=True, check=True, timeout=10)
        return float(result.stdout)
    except (FileNotFoundError, subprocess.CalledProcessError, ValueError):
        return 0.0

def _run_ffmpeg_command(command, duration, progress_callback, initial_progress, progress_range, status_message):
    """
    Runs an FFmpeg command, captures its output, and reports real-time progress.
    """
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, universal_newlines=True, encoding='utf-8', errors='ignore')
    
    time_pattern = re.compile(r"time=(\d{2}):(\d{2}):(\d{2})\.(\d{2})")

    for line in process.stdout:
        match = time_pattern.search(line)
        if match and duration > 0:
            hours, minutes, seconds, ms = map(int, match.groups())
            elapsed_time = hours * 3600 + minutes * 60 + seconds + ms / 100
            progress = min(elapsed_time / duration, 1.0)
            current_progress = initial_progress + progress * progress_range
            if progress_callback:
                progress_callback(current_progress, f"{status_message} ({int(current_progress * 100)}%)")
    
    process.wait()
    if process.returncode != 0:
        raise subprocess.CalledProcessError(process.returncode, command, output=process.stdout.read() if process.stdout else "")

def separate_audio(video_path, output_dir, progress_callback=None):
    """
    Separates audio from a video file using direct FFmpeg calls for maximum performance and low memory usage.
    Provides real-time progress updates.
    """
    try:
        if not os.path.exists(video_path):
            return False, f"错误：找不到视频文件 '{video_path}'"

        try:
            subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True, timeout=5)
        except (FileNotFoundError, subprocess.TimeoutExpired, subprocess.CalledProcessError):
            return False, "错误：找不到 FFmpeg。请确保已安装 FFmpeg 并将其添加至系统路径。"

        base_name = os.path.splitext(os.path.basename(video_path))[0]
        audio_output_path = os.path.join(output_dir, f"{base_name}_audio.mp3")
        video_output_path = os.path.join(output_dir, f"{base_name}_silent.mp4")

        duration = get_video_duration(video_path)
        if duration == 0 and progress_callback:
            progress_callback(0, "警告：无法获取视频时长，无法显示精确进度。")
        
        if progress_callback:
            progress_callback(0.05, "开始提取音频...")
        
        audio_command = ['ffmpeg', '-y', '-i', video_path, '-vn', '-c:a', 'libmp3lame', '-q:a', '2', audio_output_path]
        result = subprocess.run(audio_command, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        if result.returncode != 0:
            if "audio stream not found" in result.stderr.lower() or "does not contain any stream" in result.stderr.lower():
                return False, "错误：此视频不包含音轨。"
            return False, f"提取音频失败: {result.stderr[:500]}..."

        if progress_callback:
            progress_callback(0.4, "音频提取完成。开始生成静音视频...")

        video_codec, codec_info = get_optimal_codec()
        
        def create_video_command(codec):
            cmd = ['ffmpeg', '-y', '-i', video_path, '-an', '-c:v', codec, '-preset', 'fast', video_output_path]
            if codec == 'libx264':
                cmd.extend(['-crf', '23'])
            else:
                cmd.extend(['-qp', '23'])
            return cmd

        video_command = create_video_command(video_codec)
        
        try:
            if progress_callback:
                progress_callback(0.45, f"使用 {codec_info} 生成静音视频...")
            _run_ffmpeg_command(video_command, duration, progress_callback, 0.4, 0.6, "正在生成静音视频")
        
        except (subprocess.CalledProcessError, FileNotFoundError):
            if video_codec != 'libx264': # Only try fallback if we weren't already on CPU
                if progress_callback:
                    progress_callback(0.8, "GPU编码失败，回退到CPU处理...")
                codec_info = "CPU (GPU回退)"
                video_command = create_video_command('libx264')
                _run_ffmpeg_command(video_command, duration, progress_callback, 0.8, 0.2, "正在使用CPU重新生成")
            else:
                raise # Re-raise if CPU encoding itself failed

        if progress_callback:
            progress_callback(1.0, f"处理完成！({codec_info})")

        return True, f"成功！使用 {codec_info} 处理完成。音频已保存到 '{os.path.basename(audio_output_path)}'，静音视频已保存到 '{os.path.basename(video_output_path)}'"

    except Exception as e:
        error_message = f"处理过程中发生未知错误：{e}"
        return False, error_message
