import requests
import json
import time
import urllib3
import random

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def get_and_delete_smtp_accounts():
    base_url = "https://api.smtp.dev/accounts"
    
    # 查询参数
    params = {
        "page": 1,
        "address": "luxbey.myfw.us",
        "itemsPerPage": 30  # 每页显示数量
    }
    
    # 请求头
    headers = {
        "accept": "application/ld+json",
        "accept-encoding": "gzip, deflate, br",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "dnt": "1",
        "referer": "https://api.smtp.dev/",
        "sec-ch-ua": '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "x-api-key": "smtplabs_xCNxc4v7cjXKBkkjLcEhLmer14J3PviN2mFM8Zwpcv9LLDJX"
    }
    
    # 添加cookie
    cookies = {
        "__client_uat": "1744596094",
        "__client_uat_7CrubjyF": "1744596094"
    }
    
    success_count = 0
    fail_count = 0
    
    print("开始删除账户...")
    
    # 持续获取第一页并删除，直到没有账户为止
    while True:
        try:
            # 始终获取第一页
            params['page'] = 1
            response = requests.get(base_url, params=params, headers=headers, cookies=cookies, timeout=15, verify=False)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'member' in data and len(data['member']) > 0:
                    members = data['member']
                    total_items = data.get('totalItems', 0)
                    
                    print(f"\n当前剩余账户总数: {total_items}")
                    print(f"当前页找到 {len(members)} 个账户")
                    
                    # 删除当前页的所有账户
                    page_success, page_fail = delete_all_accounts(members, headers, cookies)
                    success_count += page_success
                    fail_count += page_fail
                    
                    # 添加延迟，避免请求过快
                    delay = 2 + random.random()  # 2-3秒随机延迟
                    print(f"等待 {delay:.1f} 秒后继续...")
                    time.sleep(delay)
                else:
                    print("没有找到更多账户，操作完成")
                    break
            else:
                print(f"请求失败，状态码: {response.status_code}")
                print("响应内容:", response.text)
                break
        
        except Exception as e:
            print(f"获取账户列表时出错: {str(e)}")
            break
    
    print(f"\n所有操作完成: 成功删除 {success_count} 个账户, 失败 {fail_count} 个")

def delete_account(account_id, headers, cookies):
    """删除指定ID的账户"""
    delete_url = f"https://api.smtp.dev/accounts/{account_id}"
    
    print(f"正在删除账户 {account_id}...")
    
    # 添加随机延迟，避免请求过快
    delay = 1 + random.random()  # 1-2秒随机延迟
    time.sleep(delay)
    
    try:
        response = requests.delete(delete_url, headers=headers, cookies=cookies, timeout=15, verify=False)
        
        if response.status_code in [200, 204]:
            print(f"成功删除账户 {account_id}")
            return True
        else:
            print(f"删除账户 {account_id} 失败，状态码: {response.status_code}")
            return False
    
    except Exception as e:
        print(f"删除账户 {account_id} 时出错: {str(e)}")
        return False

def delete_all_accounts(members, headers, cookies):
    """删除所有账户"""
    print(f"开始删除 {len(members)} 个账户...")
    
    success_count = 0
    fail_count = 0
    
    for i, member in enumerate(members):
        if 'id' not in member:
            print(f"跳过账户 #{i+1}，未找到ID")
            continue
        
        account_id = member['id']
        print(f"处理账户 {i+1}/{len(members)}: {account_id}")
        
        if delete_account(account_id, headers, cookies):
            success_count += 1
        else:
            fail_count += 1
    
    print(f"当前批次删除操作完成: 成功 {success_count} 个, 失败 {fail_count} 个")
    return success_count, fail_count

if __name__ == "__main__":
    get_and_delete_smtp_accounts()



