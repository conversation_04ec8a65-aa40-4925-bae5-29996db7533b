# -*- coding: utf-8 -*-
"""
翻译历史记录模块
"""

import json
import os
import time
from config import MAX_HISTORY

class TranslationHistory:
    """翻译历史记录管理类"""
    
    def __init__(self, history_file='translation_history.json'):
        """初始化历史记录管理器
        
        参数:
            history_file (str): 历史记录文件路径
        """
        self.history_file = history_file
        self.history = self._load_history()
        
    def _load_history(self):
        """从文件加载历史记录
        
        如果文件不存在或加载失败，则返回空列表
        """
        if not os.path.exists(self.history_file):
            return []
            
        try:
            with open(self.history_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            # 如果文件损坏或无法读取，返回空列表
            return []
            
    def _save_history(self):
        """保存历史记录到文件"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.history, f, ensure_ascii=False, indent=2)
            return True
        except IOError:
            return False
            
    def add_record(self, source_text, translated_text, from_lang, to_lang):
        """添加一条翻译记录
        
        参数:
            source_text (str): 原文
            translated_text (str): 译文
            from_lang (str): 源语言代码
            to_lang (str): 目标语言代码
            
        返回:
            bool: 是否成功添加
        """
        # 创建记录
        record = {
            'source_text': source_text,
            'translated_text': translated_text,
            'from_lang': from_lang,
            'to_lang': to_lang,
            'timestamp': time.time(),
            'datetime': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 添加到历史记录列表开头
        self.history.insert(0, record)
        
        # 如果超过最大数量，则删除最旧的记录
        if len(self.history) > MAX_HISTORY:
            self.history = self.history[:MAX_HISTORY]
            
        # 保存到文件
        return self._save_history()
        
    def get_history(self, count=None):
        """获取历史记录
        
        参数:
            count (int, optional): 获取的记录数量，默认为全部
            
        返回:
            list: 历史记录列表
        """
        if count is None:
            return self.history
        return self.history[:count]
        
    def clear_history(self):
        """清空历史记录
        
        返回:
            bool: 是否成功清空
        """
        self.history = []
        return self._save_history()
        
    def delete_record(self, index):
        """删除指定索引的记录
        
        参数:
            index (int): 记录索引
            
        返回:
            bool: 是否成功删除
        """
        if 0 <= index < len(self.history):
            del self.history[index]
            return self._save_history()
        return False
