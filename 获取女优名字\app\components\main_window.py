"""
主窗口组件
重构后的主窗口类，集成所有模块
"""

from typing import Optional
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QTextEdit, QProgressBar, QCompleter, QMessageBox, QApplication, QDesktopWidget
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QPainter, QBrush, QColor, QPen, QIcon

from app.config import config, config_manager
from app.ui_styles import style_manager
from app.history_manager import history_manager
from app.network_service import AsyncNetworkWorker, SearchResult, SearchStatus
from app.components.modern_button import ModernButton


class MainWindow(QWidget):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        
        # 初始化组件
        self.network_worker: Optional[AsyncNetworkWorker] = None
        self.status_timer: Optional[QTimer] = None
        
        # 设置窗口
        self._setup_window()
        
        # 构建UI
        self._setup_ui()
        
        # 居中显示
        self._center_window()
        
        # 加载历史记录
        self._load_search_history()
    
    def _setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle(config.WINDOW_TITLE)
        self.setWindowIcon(self._create_icon())
        self.setFixedSize(*config_manager.get_window_size())
    
    def _create_icon(self) -> QIcon:
        """创建应用图标"""
        icon_size = config_manager.get_icon_size()
        pixmap = QPixmap(*icon_size)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setBrush(QBrush(QColor(config.PRIMARY_COLOR)))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(0, 0, *icon_size)
        painter.setPen(QPen(Qt.white, 2))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "S")
        painter.end()
        
        return QIcon(pixmap)
    
    def _setup_ui(self):
        """构建用户界面"""
        # 设置主窗口样式
        self.setStyleSheet(style_manager.get_main_window_style())
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(config.MAIN_LAYOUT_SPACING)
        main_layout.setContentsMargins(*config_manager.get_main_layout_margins())
        
        # 添加各个区域
        self._create_title_area(main_layout)
        self._create_input_area(main_layout)
        self._create_status_area(main_layout)
        self._create_result_area(main_layout)
    
    def _create_title_area(self, parent_layout: QVBoxLayout):
        """创建标题区域"""
        title_label = QLabel(config.WINDOW_TITLE)
        title_label.setStyleSheet(style_manager.get_title_label_style())
        parent_layout.addWidget(title_label, alignment=Qt.AlignCenter)
    
    def _create_input_area(self, parent_layout: QVBoxLayout):
        """创建输入区域"""
        # 输入容器
        input_container = QWidget()
        input_container.setStyleSheet(style_manager.get_input_container_style())
        
        input_layout = QVBoxLayout(input_container)
        input_layout.setContentsMargins(*config_manager.get_input_layout_margins())
        input_layout.setSpacing(config.INPUT_LAYOUT_SPACING)
        input_layout.setAlignment(Qt.AlignLeft)
        
        # 输入行
        input_header = QHBoxLayout()
        input_header.setSpacing(config.INPUT_HEADER_SPACING)
        input_header.setAlignment(Qt.AlignLeft)
        
        # 标签
        input_label = QLabel("番号:")
        input_label.setFixedHeight(config.INPUT_HEIGHT)
        input_label.setFixedWidth(config.INPUT_LABEL_WIDTH)
        input_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        input_label.setStyleSheet(style_manager.get_input_label_style())
        
        # 输入框
        self.input_field = QLineEdit()
        self.input_field.setFixedHeight(config.INPUT_HEIGHT)
        self.input_field.setPlaceholderText("请输入要查询的番号...")
        self.input_field.setStyleSheet(style_manager.get_input_field_style())
        self.input_field.returnPressed.connect(self._search_actress)
        
        # 搜索按钮
        self.search_button = ModernButton("查询")
        self.search_button.setFixedSize(*config_manager.get_button_size_normal())
        self.search_button.clicked.connect(self._search_actress)
        
        # 添加到布局
        input_header.addWidget(input_label)
        input_header.addWidget(self.input_field)
        input_header.addWidget(self.search_button)
        input_layout.addLayout(input_header)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(config.PROGRESS_BAR_HEIGHT)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setValue(config.PROGRESS_RESET)
        input_layout.addWidget(self.progress_bar)
        
        parent_layout.addWidget(input_container)
    
    def _create_status_area(self, parent_layout: QVBoxLayout):
        """创建状态区域"""
        self.status_label = QLabel()
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet(style_manager.get_status_label_style())
        parent_layout.addWidget(self.status_label)
    
    def _create_result_area(self, parent_layout: QVBoxLayout):
        """创建结果区域"""
        # 结果容器
        result_container = QWidget()
        result_container.setStyleSheet(style_manager.get_result_container_style())
        
        result_layout = QVBoxLayout(result_container)
        result_layout.setContentsMargins(*config_manager.get_result_layout_margins())
        result_layout.setSpacing(config.RESULT_LAYOUT_SPACING)
        
        # 结果标题
        result_header = QLabel("查询结果")
        result_header.setStyleSheet(style_manager.get_result_header_style())
        result_layout.addWidget(result_header)
        
        # 结果文本框
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setFixedHeight(config.RESULT_TEXT_HEIGHT)
        self.result_text.setStyleSheet(style_manager.get_result_text_style())
        result_layout.addWidget(self.result_text)
        
        # 底部按钮区域
        self._create_button_area(result_layout)
        
        parent_layout.addWidget(result_container)
    
    def _create_button_area(self, parent_layout: QVBoxLayout):
        """创建按钮区域"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(config.BUTTONS_LAYOUT_SPACING)
        buttons_layout.addStretch()
        
        # 清除按钮
        self.clear_button = ModernButton("清除", button_type="secondary")
        self.clear_button.setFixedSize(*config_manager.get_button_size_small())
        self.clear_button.clicked.connect(self._clear_result)
        
        # 复制按钮
        self.copy_button = ModernButton("复制结果", button_type="success")
        self.copy_button.setFixedSize(*config_manager.get_button_size_small())
        self.copy_button.clicked.connect(self._copy_result)
        
        buttons_layout.addWidget(self.clear_button)
        buttons_layout.addWidget(self.copy_button)
        parent_layout.addLayout(buttons_layout)
    
    def _load_search_history(self):
        """加载搜索历史作为自动完成"""
        try:
            queries = history_manager.get_recent_queries(20)  # 获取最近20个查询
            if queries:
                completer = QCompleter(queries)
                completer.setCaseSensitivity(Qt.CaseInsensitive)
                completer.setCompletionMode(QCompleter.PopupCompletion)
                self.input_field.setCompleter(completer)
        except Exception as e:
            print(f"加载搜索历史失败: {e}")
    
    def _center_window(self):
        """窗口居中"""
        qr = self.frameGeometry()
        cp = QApplication.desktop().availableGeometry().center()
        qr.moveCenter(cp)
        self.move(qr.topLeft())
    
    def _search_actress(self):
        """搜索演员"""
        query = self.input_field.text().strip()
        if not query:
            QMessageBox.warning(self, "提示", config.MSG_NO_INPUT)
            return
        
        # 如果正在搜索，先取消
        if self.network_worker and self.network_worker.isRunning():
            self.network_worker.cancel()
            self.network_worker.wait()
        
        # 清空结果
        self.result_text.clear()
        
        # 设置搜索状态
        self._set_search_state(True)
        
        # 创建并启动网络工作线程
        self.network_worker = AsyncNetworkWorker(query)
        self.network_worker.finished.connect(self._on_search_finished)
        self.network_worker.status_changed.connect(self._on_status_changed)
        self.network_worker.progress_changed.connect(self._on_progress_changed)
        self.network_worker.start()
    
    def _set_search_state(self, searching: bool):
        """设置搜索状态"""
        if searching:
            self.search_button.set_loading(True)
            self.input_field.setEnabled(False)
            self.status_label.setText(config.MSG_SEARCHING)
            self.progress_bar.setValue(config.PROGRESS_INITIAL)
        else:
            self.search_button.set_loading(False)
            self.input_field.setEnabled(True)
            self.progress_bar.setValue(config.PROGRESS_RESET)
    
    def _on_search_finished(self, result: SearchResult):
        """搜索完成处理"""
        self._set_search_state(False)
        
        if result.status == SearchStatus.COMPLETED:
            if result.actresses:
                # 显示结果
                self.result_text.setPlainText('\n'.join(result.actresses))
                
                # 显示状态信息
                status_msg = result.message
                if result.from_cache:
                    status_msg += " (缓存)"
                if result.elapsed_time > 0:
                    status_msg += f" ({result.elapsed_time:.2f}s)"
                
                self._show_status_message(status_msg, 3000)
            else:
                self.result_text.setPlainText(config.MSG_NOT_FOUND)
                self._show_status_message(config.MSG_NOT_FOUND, 3000)
        else:
            # 显示错误信息
            self.result_text.setPlainText(result.message)
            self._show_status_message(f"搜索失败: {result.message}", 5000)
        
        # 更新自动完成
        self._load_search_history()
    
    def _on_status_changed(self, status: str):
        """状态变化处理"""
        self.status_label.setText(status)
    
    def _on_progress_changed(self, progress: int):
        """进度变化处理"""
        self.progress_bar.setValue(progress)
    
    def _show_status_message(self, message: str, duration: int = 2000):
        """显示状态消息"""
        self.status_label.setText(message)
        
        # 清除之前的定时器
        if self.status_timer:
            self.status_timer.stop()
        
        # 设置新的定时器
        self.status_timer = QTimer()
        self.status_timer.setSingleShot(True)
        self.status_timer.timeout.connect(lambda: self.status_label.clear())
        self.status_timer.start(duration)
    
    def _copy_result(self):
        """复制结果"""
        text = self.result_text.toPlainText()
        if text:
            clipboard = QApplication.clipboard()
            clipboard.setText(text)
            self._show_status_message(config.MSG_COPIED, config.TOOLTIP_CLEAR_DELAY)
        else:
            QMessageBox.warning(self, "提示", config.MSG_NO_COPY_CONTENT)
    
    def _clear_result(self):
        """清除结果"""
        self.result_text.clear()
        self.input_field.clear()
        self.status_label.clear()
        self.progress_bar.setValue(config.PROGRESS_RESET)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 取消正在进行的网络请求
        if self.network_worker and self.network_worker.isRunning():
            self.network_worker.cancel()
            self.network_worker.wait()
        
        # 清理定时器
        if self.status_timer:
            self.status_timer.stop()
        
        event.accept()
