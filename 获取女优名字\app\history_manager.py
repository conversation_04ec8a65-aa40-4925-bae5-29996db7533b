"""
历史记录管理模块
封装历史记录的加载、保存、查询功能，提供清晰的API接口
"""

import json
import os
import time
import threading
from typing import Dict, List, Optional, Tuple, Any
from collections import OrderedDict
from app.config import config


class HistoryEntry:
    """历史记录条目"""
    
    def __init__(self, query: str, result: str, timestamp: Optional[str] = None):
        self.query = query
        self.result = result
        self.timestamp = timestamp or time.strftime('%Y-%m-%d %H:%M:%S')
        self.access_count = 1
        self.last_access = self.timestamp
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'result': self.result,
            'timestamp': self.timestamp,
            'access_count': self.access_count,
            'last_access': self.last_access
        }
    
    @classmethod
    def from_dict(cls, query: str, data: Dict[str, Any]) -> 'HistoryEntry':
        """从字典创建实例"""
        entry = cls(query, data['result'], data.get('timestamp'))
        entry.access_count = data.get('access_count', 1)
        entry.last_access = data.get('last_access', entry.timestamp)
        return entry
    
    def update_access(self):
        """更新访问信息"""
        self.access_count += 1
        self.last_access = time.strftime('%Y-%m-%d %H:%M:%S')


class HistoryManager:
    """历史记录管理器"""
    
    def __init__(self, history_file: Optional[str] = None, max_size: Optional[int] = None):
        """
        初始化历史记录管理器
        
        Args:
            history_file: 历史记录文件路径，默认使用配置中的文件名
            max_size: 最大历史记录数量，默认使用配置中的值
        """
        self.history_file = history_file or os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            'data',
            config.HISTORY_FILENAME
        )
        self.max_size = max_size or config.HISTORY_MAX_SIZE
        self.history: OrderedDict[str, HistoryEntry] = OrderedDict()
        self._lock = threading.RLock()  # 使用可重入锁
        
        # 加载历史记录
        self.load_history()
    
    def load_history(self) -> bool:
        """
        加载历史记录
        
        Returns:
            bool: 加载是否成功
        """
        with self._lock:
            try:
                if os.path.exists(self.history_file):
                    with open(self.history_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 转换为HistoryEntry对象
                    self.history.clear()
                    for query, entry_data in data.items():
                        if isinstance(entry_data, dict):
                            # 新格式：包含详细信息
                            entry = HistoryEntry.from_dict(query, entry_data)
                        else:
                            # 旧格式：只有结果字符串，兼容处理
                            entry = HistoryEntry(query, str(entry_data))
                        
                        self.history[query] = entry
                    
                    # 按最后访问时间排序
                    self._sort_by_last_access()
                    
                    return True
                else:
                    self.history.clear()
                    return True
                    
            except (json.JSONDecodeError, IOError, OSError) as e:
                print(f"加载历史记录失败: {e}")
                self.history.clear()
                return False
            except Exception as e:
                print(f"加载历史记录时发生未知错误: {e}")
                self.history.clear()
                return False
    
    def save_history(self, query: str, result: str) -> bool:
        """
        保存历史记录
        
        Args:
            query: 查询内容
            result: 查询结果
            
        Returns:
            bool: 保存是否成功
        """
        with self._lock:
            try:
                # 更新或添加记录
                if query in self.history:
                    # 更新现有记录
                    entry = self.history[query]
                    entry.result = result
                    entry.update_access()
                    # 移动到最后（最新）
                    self.history.move_to_end(query)
                else:
                    # 添加新记录
                    entry = HistoryEntry(query, result)
                    self.history[query] = entry
                
                # 检查大小限制
                self._enforce_size_limit()
                
                # 保存到文件
                return self._save_to_file()
                
            except Exception as e:
                print(f"保存历史记录失败: {e}")
                return False
    
    def get_history(self, query: str) -> Optional[str]:
        """
        获取历史记录
        
        Args:
            query: 查询内容
            
        Returns:
            Optional[str]: 查询结果，如果不存在返回None
        """
        with self._lock:
            if query in self.history:
                entry = self.history[query]
                entry.update_access()
                # 移动到最后（最新访问）
                self.history.move_to_end(query)
                return entry.result
            return None
    
    def has_history(self, query: str) -> bool:
        """
        检查是否存在历史记录
        
        Args:
            query: 查询内容
            
        Returns:
            bool: 是否存在
        """
        with self._lock:
            return query in self.history
    
    def get_all_queries(self) -> List[str]:
        """
        获取所有查询关键字
        
        Returns:
            List[str]: 查询关键字列表，按最后访问时间倒序排列
        """
        with self._lock:
            return list(reversed(self.history.keys()))
    
    def get_recent_queries(self, limit: int = 10) -> List[str]:
        """
        获取最近的查询关键字
        
        Args:
            limit: 返回数量限制
            
        Returns:
            List[str]: 最近的查询关键字列表
        """
        with self._lock:
            queries = self.get_all_queries()
            return queries[:limit]
    
    def get_popular_queries(self, limit: int = 10) -> List[Tuple[str, int]]:
        """
        获取热门查询关键字
        
        Args:
            limit: 返回数量限制
            
        Returns:
            List[Tuple[str, int]]: (查询关键字, 访问次数) 列表，按访问次数倒序排列
        """
        with self._lock:
            entries = [(query, entry.access_count) for query, entry in self.history.items()]
            entries.sort(key=lambda x: x[1], reverse=True)
            return entries[:limit]
    
    def delete_history(self, query: str) -> bool:
        """
        删除指定的历史记录
        
        Args:
            query: 查询内容
            
        Returns:
            bool: 删除是否成功
        """
        with self._lock:
            if query in self.history:
                del self.history[query]
                return self._save_to_file()
            return True
    
    def clear_history(self) -> bool:
        """
        清空所有历史记录
        
        Returns:
            bool: 清空是否成功
        """
        with self._lock:
            self.history.clear()
            return self._save_to_file()
    
    def get_history_stats(self) -> Dict[str, Any]:
        """
        获取历史记录统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            if not self.history:
                return {
                    'total_count': 0,
                    'total_access': 0,
                    'avg_access': 0,
                    'oldest_entry': None,
                    'newest_entry': None
                }
            
            total_access = sum(entry.access_count for entry in self.history.values())
            timestamps = [entry.timestamp for entry in self.history.values()]
            
            return {
                'total_count': len(self.history),
                'total_access': total_access,
                'avg_access': round(total_access / len(self.history), 2),
                'oldest_entry': min(timestamps),
                'newest_entry': max(timestamps)
            }
    
    def _enforce_size_limit(self):
        """强制执行大小限制"""
        while len(self.history) > self.max_size:
            # 删除最旧的记录（OrderedDict的第一个）
            self.history.popitem(last=False)
    
    def _sort_by_last_access(self):
        """按最后访问时间排序"""
        # 按最后访问时间排序，最旧的在前面
        sorted_items = sorted(
            self.history.items(),
            key=lambda x: x[1].last_access
        )
        self.history.clear()
        self.history.update(sorted_items)
    
    def _save_to_file(self) -> bool:
        """保存到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.history_file), exist_ok=True)
            
            # 转换为可序列化的格式
            data = {query: entry.to_dict() for query, entry in self.history.items()}
            
            # 写入临时文件，然后重命名（原子操作）
            temp_file = self.history_file + '.tmp'
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=config.JSON_INDENT)
            
            # 原子性重命名
            if os.path.exists(self.history_file):
                os.replace(temp_file, self.history_file)
            else:
                os.rename(temp_file, self.history_file)
            
            return True
            
        except (IOError, OSError) as e:
            print(f"保存历史记录到文件失败: {e}")
            # 清理临时文件
            temp_file = self.history_file + '.tmp'
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
            return False
        except Exception as e:
            print(f"保存历史记录时发生未知错误: {e}")
            return False


# 创建全局实例
history_manager = HistoryManager()
