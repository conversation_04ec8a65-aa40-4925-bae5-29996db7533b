<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动画天气卡片</title>
    <style>
        body {
            margin: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #1a1a1a;
            font-family: Arial, sans-serif;
        }

        .container {
            display: flex;
            gap: 20px;
            padding: 20px;
        }

        .weather-card {
            width: 200px;
            height: 300px;
            background: #2a2a2a;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.3);
        }

        .weather-title {
            color: white;
            text-align: center;
            padding: 10px;
            font-size: 1.2em;
            background: rgba(0,0,0,0.2);
        }

        /* 太阳动画 */
        .sun {
            position: absolute;
            width: 80px;
            height: 80px;
            background: #ffd700;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation: glow 2s infinite alternate;
        }

        .sun::after {
            content: '';
            position: absolute;
            top: -20px;
            left: -20px;
            right: -20px;
            bottom: -20px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255,215,0,0.4) 0%, rgba(255,215,0,0) 70%);
            animation: sunRays 4s infinite linear;
        }

        /* 雨动画 */
        .rain {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .raindrop {
            position: absolute;
            width: 2px;
            height: 20px;
            background: #4fc3f7;
            animation: rain 1s infinite linear;
        }

        /* 雪动画 */
        .snow {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .snowflake {
            position: absolute;
            width: 6px;
            height: 6px;
            background: white;
            border-radius: 50%;
            animation: snow 3s infinite linear;
        }

        /* 风动画 */
        .wind {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .wind-line {
            position: absolute;
            height: 2px;
            background: rgba(255,255,255,0.3);
            animation: wind 2s infinite linear;
        }

        @keyframes glow {
            from { box-shadow: 0 0 20px #ffd700; }
            to { box-shadow: 0 0 40px #ffd700; }
        }

        @keyframes sunRays {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes rain {
            0% { transform: translateY(-100%); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateY(1000%); opacity: 0; }
        }

        @keyframes snow {
            0% { transform: translateY(-100%) rotate(0deg); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateY(1000%) rotate(360deg); opacity: 0; }
        }

        @keyframes wind {
            0% { transform: translateX(-100%); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateX(200%); opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="weather-card" id="sun-card">
            <div class="weather-title">晴天</div>
            <div class="sun"></div>
        </div>
        
        <div class="weather-card" id="rain-card">
            <div class="weather-title">雨天</div>
            <div class="rain"></div>
        </div>
        
        <div class="weather-card" id="snow-card">
            <div class="weather-title">雪天</div>
            <div class="snow"></div>
        </div>
        
        <div class="weather-card" id="wind-card">
            <div class="weather-title">大风</div>
            <div class="wind"></div>
        </div>
    </div>

    <script>
        // 创建雨滴
        function createRaindrops() {
            const rain = document.querySelector('.rain');
            for (let i = 0; i < 20; i++) {
                const raindrop = document.createElement('div');
                raindrop.className = 'raindrop';
                raindrop.style.left = Math.random() * 100 + '%';
                raindrop.style.animationDelay = Math.random() * 1 + 's';
                rain.appendChild(raindrop);
            }
        }

        // 创建雪花
        function createSnowflakes() {
            const snow = document.querySelector('.snow');
            for (let i = 0; i < 20; i++) {
                const snowflake = document.createElement('div');
                snowflake.className = 'snowflake';
                snowflake.style.left = Math.random() * 100 + '%';
                snowflake.style.animationDelay = Math.random() * 3 + 's';
                snow.appendChild(snowflake);
            }
        }

        // 创建风线
        function createWindLines() {
            const wind = document.querySelector('.wind');
            for (let i = 0; i < 10; i++) {
                const windLine = document.createElement('div');
                windLine.className = 'wind-line';
                windLine.style.top = (i * 10) + '%';
                windLine.style.width = (Math.random() * 50 + 50) + 'px';
                windLine.style.animationDelay = Math.random() * 2 + 's';
                wind.appendChild(windLine);
            }
        }

        // 初始化所有动画
        window.onload = function() {
            createRaindrops();
            createSnowflakes();
            createWindLines();
        };
    </script>
</body>
</html>
