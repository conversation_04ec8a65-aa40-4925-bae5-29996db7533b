"""
UI样式管理模块
统一管理应用程序的所有UI样式表
"""

from typing import Dict, Optional
from app.config import config


class StyleManager:
    """UI样式管理器"""
    
    @staticmethod
    def get_main_window_style() -> str:
        """获取主窗口样式"""
        return f"""
            QWidget {{
                font-family: '{config.FONT_FAMILY}';
                font-size: {config.FONT_SIZE_NORMAL}px;
                background-color: {config.BACKGROUND_COLOR};
            }}
            QLineEdit {{
                padding: {config.PADDING_NORMAL}px;
                border: {config.BORDER_WIDTH_NORMAL}px solid {config.BORDER_COLOR};
                border-radius: {config.BORDER_RADIUS_LARGE}px;
                background-color: {config.CONTAINER_BACKGROUND};
            }}
            QLineEdit:focus {{
                border-color: {config.PRIMARY_COLOR};
            }}
            QTextEdit {{
                padding: {config.PADDING_NORMAL}px;
                border: {config.BORDER_WIDTH_NORMAL}px solid {config.BORDER_COLOR};
                border-radius: {config.BORDER_RADIUS_LARGE}px;
                background-color: {config.CONTAINER_BACKGROUND};
            }}
            QLabel {{
                color: {config.TEXT_COLOR_SECONDARY};
            }}
            QProgressBar {{
                border: none;
                border-radius: {config.BORDER_RADIUS_SMALL}px;
                background-color: {config.BORDER_COLOR};
                text-align: center;
            }}
            QProgressBar::chunk {{
                background-color: {config.PRIMARY_COLOR};
                border-radius: {config.BORDER_RADIUS_SMALL}px;
            }}
        """
    
    @staticmethod
    def get_modern_button_style() -> str:
        """获取现代化按钮样式"""
        return f"""
            QPushButton {{
                background-color: {config.PRIMARY_COLOR};
                color: white;
                border: none;
                padding: {config.PADDING_SMALL}px;
                border-radius: {config.BORDER_RADIUS_SMALL}px;
                min-width: {config.BUTTON_WIDTH}px;
                font-family: '{config.FONT_FAMILY}';
            }}
            QPushButton:hover {{
                background-color: {config.PRIMARY_COLOR_HOVER};
            }}
            QPushButton:pressed {{
                background-color: {config.PRIMARY_COLOR_PRESSED};
            }}
            QPushButton:disabled {{
                background-color: {config.BORDER_COLOR_DISABLED};
                color: {config.TEXT_COLOR_DISABLED};
            }}
        """
    
    @staticmethod
    def get_title_label_style() -> str:
        """获取标题标签样式"""
        return f"""
            font-size: {config.FONT_SIZE_TITLE}px;
            font-weight: bold;
            color: {config.TEXT_COLOR_PRIMARY};
            padding-bottom: 10px;
        """
    
    @staticmethod
    def get_input_container_style() -> str:
        """获取输入容器样式"""
        return f"""
            QWidget {{
                background-color: {config.CONTAINER_BACKGROUND};
                border-radius: {config.BORDER_RADIUS_CONTAINER}px;
            }}
            QLabel {{
                padding-left: {config.PADDING_LEFT_RIGHT}px;
            }}
        """
    
    @staticmethod
    def get_input_label_style() -> str:
        """获取输入标签样式"""
        return "font-weight: bold;"
    
    @staticmethod
    def get_input_field_style() -> str:
        """获取输入框样式"""
        return f"""
            QLineEdit {{
                padding-left: {config.PADDING_NORMAL}px;
                padding-right: {config.PADDING_NORMAL}px;
                border: {config.BORDER_WIDTH_THIN}px solid {config.BORDER_COLOR};
                border-radius: {config.BORDER_RADIUS_NORMAL}px;
            }}
            QLineEdit:focus {{
                border: {config.BORDER_WIDTH_THIN}px solid {config.PRIMARY_COLOR};
            }}
        """
    
    @staticmethod
    def get_status_label_style() -> str:
        """获取状态标签样式"""
        return f"color: {config.TEXT_COLOR_MUTED};"
    
    @staticmethod
    def get_result_container_style() -> str:
        """获取结果容器样式"""
        return f"""
            QWidget {{
                background-color: {config.CONTAINER_BACKGROUND};
                border-radius: {config.BORDER_RADIUS_CONTAINER}px;
            }}
            QLabel {{
                color: {config.TEXT_COLOR_PRIMARY};
            }}
        """
    
    @staticmethod
    def get_result_header_style() -> str:
        """获取结果标题样式"""
        return f"font-weight: bold; color: {config.TEXT_COLOR_PRIMARY};"
    
    @staticmethod
    def get_result_text_style() -> str:
        """获取结果文本框样式"""
        return f"""
            QTextEdit {{
                border: {config.BORDER_WIDTH_THIN}px solid {config.BORDER_COLOR};
                padding: {config.PADDING_NORMAL}px;
                background-color: {config.RESULT_BACKGROUND};
                border-radius: {config.BORDER_RADIUS_NORMAL}px;
            }}
        """


class ThemeManager:
    """主题管理器"""
    
    # 默认主题（浅色主题）
    LIGHT_THEME = {
        'primary_color': '#4CAF50',
        'primary_hover': '#45a049',
        'primary_pressed': '#3d8b40',
        'background': '#f5f5f5',
        'container_background': 'white',
        'result_background': '#fafafa',
        'text_primary': '#2c3e50',
        'text_secondary': '#333',
        'text_muted': '#666',
        'text_disabled': '#666666',
        'border': '#ddd',
        'border_disabled': '#cccccc'
    }
    
    # 深色主题
    DARK_THEME = {
        'primary_color': '#4CAF50',
        'primary_hover': '#45a049',
        'primary_pressed': '#3d8b40',
        'background': '#2b2b2b',
        'container_background': '#3c3c3c',
        'result_background': '#404040',
        'text_primary': '#ffffff',
        'text_secondary': '#e0e0e0',
        'text_muted': '#b0b0b0',
        'text_disabled': '#808080',
        'border': '#555555',
        'border_disabled': '#444444'
    }
    
    def __init__(self):
        self.current_theme = 'light'
        self.themes = {
            'light': self.LIGHT_THEME,
            'dark': self.DARK_THEME
        }
    
    def set_theme(self, theme_name: str) -> bool:
        """设置主题"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            self._apply_theme()
            return True
        return False
    
    def get_current_theme(self) -> str:
        """获取当前主题名称"""
        return self.current_theme
    
    def get_theme_colors(self, theme_name: Optional[str] = None) -> Dict[str, str]:
        """获取主题颜色配置"""
        theme = theme_name or self.current_theme
        return self.themes.get(theme, self.LIGHT_THEME).copy()
    
    def _apply_theme(self):
        """应用主题到配置"""
        theme_colors = self.get_theme_colors()
        
        # 更新配置中的颜色值
        config.PRIMARY_COLOR = theme_colors['primary_color']
        config.PRIMARY_COLOR_HOVER = theme_colors['primary_hover']
        config.PRIMARY_COLOR_PRESSED = theme_colors['primary_pressed']
        config.BACKGROUND_COLOR = theme_colors['background']
        config.CONTAINER_BACKGROUND = theme_colors['container_background']
        config.RESULT_BACKGROUND = theme_colors['result_background']
        config.TEXT_COLOR_PRIMARY = theme_colors['text_primary']
        config.TEXT_COLOR_SECONDARY = theme_colors['text_secondary']
        config.TEXT_COLOR_MUTED = theme_colors['text_muted']
        config.TEXT_COLOR_DISABLED = theme_colors['text_disabled']
        config.BORDER_COLOR = theme_colors['border']
        config.BORDER_COLOR_DISABLED = theme_colors['border_disabled']
    
    def add_custom_theme(self, theme_name: str, theme_colors: Dict[str, str]) -> bool:
        """添加自定义主题"""
        required_keys = set(self.LIGHT_THEME.keys())
        provided_keys = set(theme_colors.keys())
        
        if required_keys.issubset(provided_keys):
            self.themes[theme_name] = theme_colors.copy()
            return True
        return False


class StyleBuilder:
    """样式构建器 - 用于动态构建样式"""
    
    def __init__(self):
        self.styles = []
    
    def add_widget_style(self, widget_type: str, properties: Dict[str, str]) -> 'StyleBuilder':
        """添加组件样式"""
        style_parts = [f"{widget_type} {{"]
        for prop, value in properties.items():
            style_parts.append(f"    {prop}: {value};")
        style_parts.append("}")
        
        self.styles.append("\n".join(style_parts))
        return self
    
    def add_state_style(self, widget_type: str, state: str, properties: Dict[str, str]) -> 'StyleBuilder':
        """添加组件状态样式"""
        style_parts = [f"{widget_type}:{state} {{"]
        for prop, value in properties.items():
            style_parts.append(f"    {prop}: {value};")
        style_parts.append("}")
        
        self.styles.append("\n".join(style_parts))
        return self
    
    def build(self) -> str:
        """构建最终样式字符串"""
        return "\n".join(self.styles)
    
    def clear(self) -> 'StyleBuilder':
        """清空样式"""
        self.styles.clear()
        return self


# 创建全局实例
style_manager = StyleManager()
theme_manager = ThemeManager()
style_builder = StyleBuilder()
