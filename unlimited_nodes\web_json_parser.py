import json
import os
import sys
import re
import logging
from bs4 import BeautifulSoup
import html
import yaml  # 添加这行导入

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def parse_web_json(json_file_path):
    """
    读取web.json文件，解析body内容，去除HTML标签，并保存为纯文本文件

    Args:
        json_file_path: web.json文件的路径
    """
    try:
        # 获取脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # 如果提供的是相对路径，将其转换为相对于脚本目录的绝对路径
        if not os.path.isabs(json_file_path):
            json_file_path = os.path.join(script_dir, json_file_path)

        # 检查文件是否存在
        if not os.path.exists(json_file_path):
            logging.error(f"错误: 文件 '{json_file_path}' 不存在")
            logging.info(f"脚本目录: {script_dir}")
            logging.info(f"当前工作目录: {os.getcwd()}")
            logging.info(f"尝试查找的完整路径: {os.path.abspath(json_file_path)}")

            # 列出脚本目录下的文件
            logging.info("\n脚本目录下的文件:")
            for file in os.listdir(script_dir):
                logging.info(f"  - {file}")
            return False

        # 读取JSON文件
        logging.info(f"正在读取文件: {json_file_path}")
        with open(json_file_path, 'r', encoding='utf-8') as file:
            try:
                data = json.load(file)
                logging.info("JSON文件读取成功")
            except json.JSONDecodeError as e:
                logging.error(f"JSON解析错误: {e}")
                return False

        # 提取body内容
        if 'body' not in data:
            logging.error("错误: JSON中没有找到'body'字段")
            logging.info("JSON文件内容的键:")
            for key in data.keys():
                logging.info(f"  - {key}")
            return False

        body_content = data['body']
        logging.info("成功提取body内容")
        logging.info(f"原始body内容长度: {len(body_content)} 字符")

        # 先解码HTML实体
        body_content = html.unescape(body_content)

        # 使用BeautifulSoup去除HTML标签
        soup = BeautifulSoup(body_content, 'html.parser')
        clean_content = soup.get_text()

        clean_content = re.sub(r'\n\s*\n', '\n', clean_content)  # 合并多余空行
        clean_content = clean_content.strip()  # 去除首尾空白

        # 检查清理后的内容
        if not clean_content.strip():
            logging.warning("警告：清理后的内容为空")
        logging.info(f"清理后内容长度: {len(clean_content)} 字符")
        logging.info("内容的前100个字符:")
        logging.info(clean_content[:100])
        logging.info("内容的最后100个字符:")
        logging.info(clean_content[-100:] if len(clean_content) >= 100 else clean_content)

        # 检查清理后的内容是否有截断迹象
        logging.info(f"清理后内容长度: {len(clean_content)} 字符")

        # 检查是否有特殊字符可能导致截断
        for i, char in enumerate(clean_content):
            if ord(char) < 32 and char not in ['\n', '\t', '\r']:
                logging.warning(f"警告: 位置 {i} 处发现控制字符: {repr(char)} (十六进制: {hex(ord(char))})")
                # 如果在36行附近发现特殊字符，特别提醒
                if 1000 < i < 1500:  # 大致范围
                    logging.warning(f"注意: 这个字符在可能导致截断的区域附近")

        # 保存为YAML文件
        output_file = os.path.splitext(json_file_path)[0] + '.yaml'

        # 打印内容长度和前后部分，用于调试
        logging.info(f"清理后的内容长度: {len(clean_content)} 字符")
        logging.info(f"内容前50个字符: {clean_content[:50]}")
        logging.info(f"内容最后50个字符: {clean_content[-50:] if len(clean_content) > 50 else clean_content}")

        # 直接写入内容，不包含content字段
        with open(output_file, 'w', encoding='utf-8') as file:
            file.write(clean_content)

        # 验证写入的文件大小
        file_size = os.path.getsize(output_file)
        logging.info(f"清理后的内容已保存到: {output_file} (文件大小: {file_size} 字节)")
        return True

    except Exception as e:
        logging.error(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 如果命令行提供了文件路径，使用它；否则使用默认的"web.json"
    if len(sys.argv) > 1:
        json_file_path = sys.argv[1]
    else:
        json_file_path = "web.json"

    # 执行解析
    success = parse_web_json(json_file_path)

    if success:
        logging.info("处理完成")
    else:
        logging.error("处理失败")
        sys.exit(1)
