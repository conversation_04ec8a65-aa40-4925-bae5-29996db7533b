<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animated Weather Cards</title>
    <style>
        body {
            margin: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #1a1a1a;
            font-family: 'Arial', sans-serif;
            color: white;
        }

        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            padding: 20px;
            justify-content: center;
        }

        .weather-card {
            width: 200px;
            height: 300px;
            background: linear-gradient(to bottom, #2a2a2a, #1a1a1a);
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0,0,0,0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .weather-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.4);
        }

        .weather-title {
            color: white;
            text-align: center;
            padding: 15px;
            font-size: 1.2em;
            background: rgba(0,0,0,0.3);
            border-bottom: 1px solid rgba(255,255,255,0.1);
            z-index: 10;
            position: relative;
        }

        .weather-icon {
            position: absolute;
            top: 70px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 40px;
            z-index: 5;
        }

        .temperature {
            position: absolute;
            bottom: 20px;
            width: 100%;
            text-align: center;
            font-size: 2em;
            font-weight: bold;
            z-index: 5;
        }

        /* Sun Animation */
        .sun {
            position: absolute;
            width: 80px;
            height: 80px;
            background: #ffd700;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 50px #ffd700;
            animation: glow 3s infinite alternate;
        }

        .sun::after {
            content: '';
            position: absolute;
            top: -30px;
            left: -30px;
            right: -30px;
            bottom: -30px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255,215,0,0.4) 0%, rgba(255,215,0,0) 70%);
            animation: sunRays 10s infinite linear;
        }

        /* Rain Animation */
        .rain {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .raindrop {
            position: absolute;
            width: 2px;
            height: 20px;
            background: linear-gradient(to bottom, transparent, #4fc3f7);
            border-radius: 0 0 5px 5px;
            opacity: 0.8;
            animation: rain 1.5s infinite linear;
        }

        /* Snow Animation */
        .snow {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .snowflake {
            position: absolute;
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            opacity: 0.8;
            animation: snow 5s infinite linear;
        }

        /* Wind Animation */
        .wind {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .wind-line {
            position: absolute;
            height: 2px;
            background: linear-gradient(to right, transparent, rgba(255,255,255,0.5), transparent);
            animation: wind 3s infinite linear;
        }

        .cloud {
            position: absolute;
            background: #b3b3b3;
            border-radius: 50%;
            animation: moveCloud 8s infinite linear;
        }

        /* Controls */
        .controls {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            justify-content: center;
        }

        .control-btn {
            padding: 10px 15px;
            background: #333;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .control-btn:hover {
            background: #555;
        }

        .active {
            background: #4CAF50;
        }

        .active:hover {
            background: #3e8e41;
        }

        /* Keyframes */
        @keyframes glow {
            from { box-shadow: 0 0 30px #ffd700; }
            to { box-shadow: 0 0 60px #ffd700; }
        }

        @keyframes sunRays {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes rain {
            0% { transform: translateY(-100%); opacity: 0; }
            10% { opacity: 0.8; }
            90% { opacity: 0.8; }
            100% { transform: translateY(1000%); opacity: 0; }
        }

        @keyframes snow {
            0% { transform: translateY(-100%) rotate(0deg); opacity: 0; }
            10% { opacity: 0.8; }
            90% { opacity: 0.8; }
            100% { transform: translateY(1000%) rotate(360deg); opacity: 0; }
        }

        @keyframes wind {
            0% { transform: translateX(-100%); opacity: 0; }
            10% { opacity: 0.8; }
            90% { opacity: 0.8; }
            100% { transform: translateX(200%); opacity: 0; }
        }

        @keyframes moveCloud {
            0% { transform: translateX(-150%); }
            100% { transform: translateX(250%); }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="container">
            <div class="weather-card" id="sun-card">
                <div class="weather-title">Sunny</div>
                <div class="sun"></div>
                <div class="temperature">28°C</div>
            </div>
            
            <div class="weather-card" id="rain-card">
                <div class="weather-title">Rainy</div>
                <div class="rain"></div>
                <div class="temperature">18°C</div>
            </div>
            
            <div class="weather-card" id="snow-card">
                <div class="weather-title">Snowy</div>
                <div class="snow"></div>
                <div class="temperature">-5°C</div>
            </div>
            
            <div class="weather-card" id="wind-card">
                <div class="weather-title">Windy</div>
                <div class="wind"></div>
                <div class="temperature">22°C</div>
            </div>
        </div>
        
        <div class="controls">
            <button class="control-btn active" data-weather="all">All</button>
            <button class="control-btn" data-weather="sun">Sunny</button>
            <button class="control-btn" data-weather="rain">Rainy</button>
            <button class="control-btn" data-weather="snow">Snowy</button>
            <button class="control-btn" data-weather="wind">Windy</button>
        </div>
    </div>

    <script>
        // Create weather elements
        function createRaindrops() {
            const rain = document.querySelector('.rain');
            rain.innerHTML = ''; // Clear existing raindrops
            for (let i = 0; i < 30; i++) {
                const raindrop = document.createElement('div');
                raindrop.className = 'raindrop';
                raindrop.style.left = Math.random() * 100 + '%';
                raindrop.style.animationDelay = Math.random() * 1.5 + 's';
                raindrop.style.opacity = Math.random() * 0.4 + 0.4;
                rain.appendChild(raindrop);
            }
        }

        function createSnowflakes() {
            const snow = document.querySelector('.snow');
            snow.innerHTML = ''; // Clear existing snowflakes
            for (let i = 0; i < 30; i++) {
                const snowflake = document.createElement('div');
                snowflake.className = 'snowflake';
                snowflake.style.left = Math.random() * 100 + '%';
                snowflake.style.animationDelay = Math.random() * 5 + 's';
                snowflake.style.opacity = Math.random() * 0.4 + 0.4;
                snowflake.style.width = snowflake.style.height = Math.random() * 6 + 4 + 'px';
                snow.appendChild(snowflake);
            }
        }

        function createWindElements() {
            const wind = document.querySelector('.wind');
            wind.innerHTML = ''; // Clear existing wind elements
            
            // Create wind lines
            for (let i = 0; i < 15; i++) {
                const windLine = document.createElement('div');
                windLine.className = 'wind-line';
                windLine.style.top = (i * 7 + 10) + '%';
                windLine.style.width = (Math.random() * 50 + 50) + 'px';
                windLine.style.animationDelay = Math.random() * 3 + 's';
                windLine.style.animationDuration = (Math.random() * 2 + 2) + 's';
                wind.appendChild(windLine);
            }
            
            // Create clouds
            for (let i = 0; i < 3; i++) {
                const cloud = document.createElement('div');
                cloud.className = 'cloud';
                cloud.style.top = (i * 30 + 10) + '%';
                cloud.style.width = (Math.random() * 40 + 60) + 'px';
                cloud.style.height = (Math.random() * 20 + 30) + 'px';
                cloud.style.opacity = Math.random() * 0.4 + 0.2;
                cloud.style.animationDelay = i * 2 + 's';
                cloud.style.animationDuration = (Math.random() * 4 + 6) + 's';
                wind.appendChild(cloud);
            }
        }

        // Initialize all weather animations
        function initWeather() {
            createRaindrops();
            createSnowflakes();
            createWindElements();
        }

        // Show/hide weather cards based on selection
        function showWeather(weatherType) {
            const cards = document.querySelectorAll('.weather-card');
            
            if (weatherType === 'all') {
                cards.forEach(card => card.style.display = 'block');
            } else {
                cards.forEach(card => {
                    if (card.id === weatherType + '-card') {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            }
            
            // Update active button
            document.querySelectorAll('.control-btn').forEach(btn => {
                if (btn.dataset.weather === weatherType) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });
        }

        // Add event listeners to buttons
        document.querySelectorAll('.control-btn').forEach(button => {
            button.addEventListener('click', () => {
                showWeather(button.dataset.weather);
            });
        });

        // Initialize on page load
        window.onload = function() {
            initWeather();
            
            // Add click event to cards for a nice effect
            document.querySelectorAll('.weather-card').forEach(card => {
                card.addEventListener('click', () => {
                    card.style.transform = 'scale(1.05) translateY(-10px)';
                    setTimeout(() => {
                        card.style.transform = '';
                    }, 300);
                });
            });
        };
    </script>
</body>
</html>