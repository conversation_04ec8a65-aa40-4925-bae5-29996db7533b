import tkinter as tk
from tkinter import filedialog, Scale, Label, Button, Frame, ttk, messagebox
from PIL import Image, ImageTk, ImageEnhance, ImageFilter
import os
import subprocess
import platform
import tkinter.font as tkFont
import numpy as np

############################################################
# Pillow插值常量动态选择函数（兼容Pillow 10+和旧版）
# 设计说明：
# - Pillow 10.0+ 移除了Image.LANCZOS等常量，统一放入Image.Resampling命名空间。
# - 低版本Pillow仍然使用Image.LANCZOS等常量。
# - 本函数根据Pillow实际版本动态返回正确的插值常量，避免因API变动导致的兼容性问题。
# - mode参数支持'LANCZOS'、'NEAREST'等，若找不到则兜底返回1（LANCZOS）或0（NEAREST）。
############################################################
def get_resample(mode):
    """
    动态获取Pillow插值常量，兼容Pillow 10+和旧版。
    参数：
        mode: 'LANCZOS' 或 'NEAREST' 等插值模式名称（字符串）
    返回：
        对应的插值常量（int）
    """
    try:
        from PIL import Image
        # Pillow 10.0+ 推荐用Image.Resampling.LANCZOS等
        if hasattr(Image, 'Resampling'):
            return getattr(Image.Resampling, mode, 1 if mode=="LANCZOS" else 0)
        else:
            # 兼容旧版Pillow
            return getattr(Image, mode, 1 if mode=="LANCZOS" else 0)
    except Exception:
        # 极端情况下兜底，保证不会因API变动报错
        return 1 if mode=="LANCZOS" else 0

class PixelArtConverter:
    def __init__(self, root):
        self.root = root
        self.root.title("游戏像素风格转换器")

        # 设置窗口大小和居中
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        window_width = 1400
        window_height = 800
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 设置最小窗口大小
        self.root.minsize(1200, 700)
        
        # 设置复古游戏风格主题颜色
        self.bg_color = "#0a0a0a"          # 深黑背景
        self.accent_color = "#00ff41"      # 经典终端绿
        self.control_bg = "#1a1a1a"       # 控制面板背景
        self.button_bg = "#2a2a2a"        # 按钮背景
        self.border_color = "#004400"     # 边框颜色
        self.text_color = "#00ff41"       # 文字颜色
        self.root.configure(bg=self.bg_color)

        # 设置等宽字体配置（带回退机制）
        self.main_font = "Consolas 10 normal"
        self.title_font = "Consolas 11 bold"
        self.label_font = "Consolas 9 normal"
        self.button_font = "Consolas 10 normal"

        # 字体回退列表确保跨平台兼容性
        self.font_fallback = ["Consolas", "Courier New", "Monaco", "Menlo", "monospace"]
        
        self.image_path = None
        self.original_image = None
        self.pixelated_image = None
        self.pixel_size = 10
        self.detail_level = 50
        self.saturation = 1.0
        self.current_style = "custom"  # 添加当前风格标记
        self.styles = {
            "custom": {"pixel": 10, "detail": 50, "saturation": 1.0},
            "细腻像素": {"pixel": 3, "detail": 85, "saturation": 1.25},
            "手绘像素": {"pixel": 5, "detail": 75, "saturation": 1.3},
            "16-bit": {"pixel": 4, "detail": 70, "saturation": 1.2},
            "8-bit": {"pixel": 8, "detail": 30, "saturation": 1.1},
            "现代像素": {"pixel": 6, "detail": 60, "saturation": 1.15},
            "复古像素": {"pixel": 12, "detail": 20, "saturation": 0.9},
            "极简像素": {"pixel": 15, "detail": 10, "saturation": 0.8}
        }
        self.last_save_path = None  # 添加保存路径记录
        
        self.update_timer = None
        self.focused_slider = None  # 记录当前焦点滑块
        self.original_image_size = None  # 保存原始图片尺寸用于统一显示大小
        self._setup_fonts()  # 设置字体
        self.ttk_label_args = {'font': self.label_font}  # 集中定义ttk控件字体参数，便于维护
        self.setup_ui()

    def _setup_fonts(self):
        """设置字体，带回退机制确保跨平台兼容性"""
        # 检测可用字体
        available_fonts = tkFont.families()

        # 选择第一个可用的等宽字体
        selected_font = "Courier New"  # 默认回退字体
        for font in self.font_fallback:
            if font in available_fonts:
                selected_font = font
                break

        # 更新字体配置
        self.main_font = selected_font + " 10 normal"
        self.title_font = selected_font + " 11 bold"
        self.label_font = selected_font + " 9 normal"
        self.button_font = selected_font + " 10 normal"

    def setup_ui(self):
        # --- 主布局框架 ---
        # 左侧控制面板 - 使用8的倍数间距符合像素网格
        control_panel = Frame(self.root, bg=self.control_bg, width=320, padx=16, pady=16)
        control_panel.pack(side=tk.LEFT, fill=tk.Y)
        # 允许面板根据内容自动调节高度
        # control_panel.pack_propagate(False)

        # 垂直分割线 - 像素化效果
        separator = ttk.Separator(self.root, orient='vertical')
        separator.pack(side=tk.LEFT, fill='y', padx=8)

        # 右侧图片显示面板 - 使用8的倍数间距
        image_panel = Frame(self.root, bg=self.bg_color, padx=8, pady=8)
        image_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # --- 左侧控制面板 (Control Panel) ---
        # 1. 风格选择区域 - 添加像素化装饰
        style_frame = ttk.LabelFrame(control_panel, text="▓▓ 预设风格 ▓▓", padding=(8, 8))
        style_frame.grid(row=0, column=0, sticky='ew', pady=(0, 16))

        self.style_buttons = {}
        style_names = list(self.styles.keys())
        for i, style_name in enumerate(style_names):
            btn = ttk.Button(style_frame, text=style_name,
                           command=lambda s=style_name: self.apply_style(s),
                           style="StyleButton.TButton")
            # 每行2个按钮，使用8的倍数间距
            btn.grid(row=i // 2, column=i % 2, padx=8, pady=4, sticky="ew")
            self.style_buttons[style_name] = btn
        style_frame.grid_columnconfigure((0, 1), weight=1)

        # 2. 参数调整区域 - 添加像素化装饰
        params_frame = ttk.LabelFrame(control_panel, text="▓▓ 参数调整 ▓▓", padding=(8, 8))
        params_frame.grid(row=1, column=0, sticky='ew', pady=(0, 16))

        label_style = {"font": self.label_font}
        slider_style = {
            "orient": tk.HORIZONTAL,
            "command": self.schedule_update
        }

        def slider_update_command(value):
            self._update_slider_labels()
            self.schedule_update()

        slider_style["command"] = slider_update_command

        # 像素大小 - 使用8的倍数间距
        ttk.Label(params_frame, text="像素大小:", font=self.label_font).grid(row=0, column=0, sticky='w', pady=4)
        self.pixel_slider = ttk.Scale(params_frame, from_=2, to=50, **slider_style)
        self.pixel_slider.set(self.pixel_size)
        self.pixel_slider.grid(row=0, column=1, sticky='ew', padx=8)
        self.pixel_value_label = ttk.Label(params_frame, text=str(self.pixel_size), width=4, font=self.label_font)
        self.pixel_value_label.grid(row=0, column=2, sticky='w', padx=(8, 0))
        self.setup_slider_bindings(self.pixel_slider, 1)

        # 细节保持 - 使用8的倍数间距
        ttk.Label(params_frame, text="细节保持:", font=self.label_font).grid(row=1, column=0, sticky='w', pady=4)
        self.detail_slider = ttk.Scale(params_frame, from_=0, to=100, **slider_style)
        self.detail_slider.set(self.detail_level)
        self.detail_slider.grid(row=1, column=1, sticky='ew', padx=8)
        self.detail_value_label = ttk.Label(params_frame, text=str(self.detail_level), width=4, font=self.label_font)
        self.detail_value_label.grid(row=1, column=2, sticky='w', padx=(8, 0))
        self.setup_slider_bindings(self.detail_slider, 5)

        # 饱和度 - 使用8的倍数间距
        ttk.Label(params_frame, text="饱和度:", font=self.label_font).grid(row=2, column=0, sticky='w', pady=4)
        self.saturation_slider = ttk.Scale(params_frame, from_=0.0, to=2.0, **slider_style)
        self.saturation_slider.set(self.saturation)
        self.saturation_slider.grid(row=2, column=1, sticky='ew', padx=8)
        self.saturation_value_label = ttk.Label(params_frame, text=f"{self.saturation:.2f}", width=4, font=self.label_font)
        self.saturation_value_label.grid(row=2, column=2, sticky='w', padx=(8, 0))
        self.setup_slider_bindings(self.saturation_slider, 0.1)

        params_frame.grid_columnconfigure(1, weight=1)

        # 3. 状态与进度区域 - 添加像素化装饰
        status_frame = ttk.LabelFrame(control_panel, text="▓▓ 系统状态 ▓▓", padding=(8, 8))
        status_frame.grid(row=3, column=0, sticky='ew', pady=(0, 16))

        self.status_label = ttk.Label(status_frame, text="> 欢迎使用!", font=self.label_font, foreground=self.text_color, width=30, anchor='w')
        self.status_label.pack(side=tk.LEFT, padx=(0, 8))
        
        self.progress = ttk.Progressbar(status_frame, length=100, mode='determinate')
        self.progress.pack(side=tk.RIGHT)

        # 4. 操作按钮区域 (放置在最底部) - 添加像素化装饰
        action_frame = ttk.LabelFrame(control_panel, text="▓▓ 操作面板 ▓▓", padding=(8, 8))
        action_frame.grid(row=4, column=0, sticky='ew')

        # 调整 grid 行列以将按钮区推到底部
        control_panel.grid_columnconfigure(0, weight=1)

        # 按钮使用8的倍数间距和增强交互样式
        btn_select = ttk.Button(action_frame, text="◆ 选择图片", command=self.select_image, style="ActionButton.TButton")
        btn_select.pack(fill=tk.X, pady=4)

        btn_apply = ttk.Button(action_frame, text="◆ 应用效果", command=self.apply_effect, style="ActionButton.TButton")
        btn_apply.pack(fill=tk.X, pady=4)

        btn_save = ttk.Button(action_frame, text="◆ 保存像素图", command=self.save_image, style="ActionButton.TButton")
        btn_save.pack(fill=tk.X, pady=4)

        self.open_folder_btn = ttk.Button(action_frame, text="◆ 打开保存位置", command=self.open_save_location, state=tk.DISABLED, style="ActionButton.TButton")
        self.open_folder_btn.pack(fill=tk.X, pady=4)

        # --- 右侧图片显示面板 (Image Panel) ---
        title_style = {"font": self.title_font}

        ttk.Label(image_panel, text="▓▓ 原始图片 ▓▓", font=self.title_font).grid(row=0, column=0, pady=(0, 8))
        self.original_display = Label(image_panel, bg=self.button_bg, relief="solid", borderwidth=2)
        self.original_display.grid(row=1, column=0, sticky="nsew", padx=(0, 8))

        ttk.Label(image_panel, text="▓▓ 像素风格图片 ▓▓", font=self.title_font).grid(row=0, column=1, pady=(0, 8))
        self.pixel_display = Label(image_panel, bg=self.button_bg, relief="solid", borderwidth=2)
        self.pixel_display.grid(row=1, column=1, sticky="nsew", padx=(8, 0))

        # 绑定尺寸变化事件以自适应缩放
        self.original_display.bind("<Configure>", lambda e: self.on_label_resize(self.original_display))
        self.pixel_display.bind("<Configure>", lambda e: self.on_label_resize(self.pixel_display))

        image_panel.grid_rowconfigure(1, weight=1)
        image_panel.grid_columnconfigure((0, 1), weight=1)

        # --- 复古像素游戏风格样式配置 ---
        style = ttk.Style()
        style.theme_use('clam')  # 使用clam主题作为基础

        # 按钮样式 - 方形像素风格
        style.configure("TButton",
                       font=self.button_font,
                       background=self.button_bg,
                       foreground=self.text_color,
                       borderwidth=2,
                       relief="solid",
                       padding=(8, 4))

        # 按钮悬停状态 - 增强交互反馈
        style.map("TButton",
                 background=[('active', self.accent_color),
                           ('pressed', self.border_color)],
                 foreground=[('active', self.bg_color),
                           ('pressed', self.text_color)],
                 relief=[('pressed', 'sunken')],
                 borderwidth=[('pressed', 3)])

        # 风格按钮专用样式 - 选中状态
        style.configure("StyleButton.TButton",
                       font=self.button_font,
                       background=self.button_bg,
                       foreground=self.text_color,
                       borderwidth=2,
                       relief="solid",
                       padding=(8, 4))

        style.map("StyleButton.TButton",
                 background=[('active', self.accent_color),
                           ('pressed', self.border_color)],
                 foreground=[('active', self.bg_color),
                           ('pressed', self.text_color)],
                 relief=[('pressed', 'sunken')],
                 borderwidth=[('pressed', 3)])

        # 选中的风格按钮样式
        style.configure("Selected.StyleButton.TButton",
                       font=self.button_font,
                       background=self.accent_color,
                       foreground=self.bg_color,
                       borderwidth=3,
                       relief="solid",
                       padding=(8, 4))

        style.map("Selected.StyleButton.TButton",
                 background=[('active', self.text_color),
                           ('pressed', self.border_color)],
                 foreground=[('active', self.bg_color),
                           ('pressed', self.text_color)],
                 relief=[('pressed', 'sunken')])

        # 操作按钮专用样式 - 增强交互效果
        style.configure("ActionButton.TButton",
                       font=self.button_font,
                       background=self.border_color,
                       foreground=self.text_color,
                       borderwidth=2,
                       relief="solid",
                       padding=(8, 6))

        style.map("ActionButton.TButton",
                 background=[('active', self.accent_color),
                           ('pressed', self.text_color),
                           ('disabled', self.button_bg)],
                 foreground=[('active', self.bg_color),
                           ('pressed', self.bg_color),
                           ('disabled', '#666666')],
                 relief=[('pressed', 'sunken')],
                 borderwidth=[('pressed', 3),
                            ('active', 3)])

        # 标签样式
        style.configure("TLabel",
                       background=self.bg_color,
                       font=self.main_font,
                       foreground=self.text_color)

        # 框架样式
        style.configure("TFrame",
                       background=self.bg_color,
                       borderwidth=1,
                       relief="solid")

        # 标签框架样式 - 像素化边框
        style.configure("TLabelframe",
                       background=self.bg_color,
                       bordercolor=self.accent_color,
                       borderwidth=2,
                       relief="solid")

        style.configure("TLabelframe.Label",
                       background=self.bg_color,
                       foreground=self.accent_color,
                       font=self.title_font)

        # 滑块样式 - 方形像素风格
        style.configure("Horizontal.TScale",
                       background=self.bg_color,
                       troughcolor=self.button_bg,
                       borderwidth=2,
                       relief="solid")

        # 进度条样式 - 增强像素块效果
        style.configure("Pixel.Horizontal.TProgressbar",
                       background=self.accent_color,
                       troughcolor=self.button_bg,
                       borderwidth=2,
                       relief="solid",
                       lightcolor=self.accent_color,
                       darkcolor=self.border_color,
                       thickness=16)  # 增加厚度使像素块更明显

        self.progress.configure(style="Pixel.Horizontal.TProgressbar", length=120)

        # 分割线样式 - 像素化效果
        style.configure("TSeparator",
                       background=self.accent_color,
                       borderwidth=1,
                       relief="solid")

        # 初始高亮风格按钮
        self.highlight_style_button()

    def save_image(self):
        """保存图片 - 增强状态反馈"""
        if self.pixelated_image:
            self.update_status("> 选择保存位置...", 0)

            save_path = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[("PNG", "*.png"), ("JPEG", "*.jpg"), ("BMP", "*.bmp")])

            if save_path:
                self.update_status("> 正在保存文件...", 30)

                self.pixelated_image.save(save_path)
                self.last_save_path = save_path  # 记录保存路径

                self.update_status("> 保存成功!", 100)
                self.open_folder_btn.config(state=tk.NORMAL)  # 启用打开文件夹按钮

                # 3秒后恢复默认状态
                self.root.after(3000, lambda: self.update_status("> 就绪", 0))
            else:
                self.update_status("> 取消保存", 0)
        else:
            self.update_status("> 没有可保存的图片", 0)

    def setup_slider_bindings(self, slider, step):
        """设置滑块的键盘绑定"""
        # 焦点事件
        slider.bind('<FocusIn>', lambda e: self.on_slider_focus(slider))
        slider.bind('<FocusOut>', lambda e: self.on_slider_focus(None))
        
        # 键盘事件
        slider.bind('<Left>', lambda e: self.adjust_slider(slider, -step))
        slider.bind('<Right>', lambda e: self.adjust_slider(slider, step))
        slider.bind('<Up>', lambda e: self.adjust_slider(slider, step))
        slider.bind('<Down>', lambda e: self.adjust_slider(slider, -step))
        
        # 鼠标点击时获取焦点
        slider.bind('<Button-1>', lambda e: slider.focus_set())

    def on_slider_focus(self, slider):
        """处理滑块焦点变化"""
        self.focused_slider = slider
        
        # 动态样式更新已移除以修复 TclError 报错

    def adjust_slider(self, slider, step):
        """调整滑块值"""
        current = float(slider.get())
        new_value = current + step
        
        # 确保值在有效范围内
        min_val = float(slider.cget('from'))
        max_val = float(slider.cget('to'))
        new_value = max(min_val, min(max_val, new_value))
        
        # 如果是饱和度滑块，保留一位小数
        if slider == self.saturation_slider:
            new_value = round(new_value, 1)
        else:
            new_value = int(new_value)  # 其他滑块使用整数值
        
        # 设置新值并触发更新
        slider.set(new_value)
        self.schedule_update()

    def detect_edges(self, image):
        """检测图像边缘"""
        edges = image.filter(ImageFilter.FIND_EDGES)
        return edges

    def enhance_details(self, image, edge_image, detail_level):
        """增强图像细节"""
        # 将边缘图像转换为numpy数组
        edge_array = np.array(edge_image.convert('L'))
        
        # 使用更敏感的边缘检测阈值
        edge_threshold = 20  # 降低阈值以检测更多细节
        mask = edge_array > edge_threshold
        
        # 创建渐变mask以实现平滑过渡
        mask = mask.astype(np.float32)
        
        # 应用高斯模糊使边缘过渡更自然
        mask = Image.fromarray((mask * 255).astype(np.uint8))
        mask = mask.filter(ImageFilter.GaussianBlur(radius=0.5))
        mask = np.array(mask) / 255.0
        
        # 根据细节级别调整强度
        detail_strength = detail_level / 100.0
        mask = mask * detail_strength
        
        # 扩展mask维度以匹配图像数组
        mask = np.stack([mask] * 3, axis=2)
        
        # 将原图转换为numpy数组
        img_array = np.array(image)
        
        # 创建增强后的图像版本
        enhanced = np.array(image.filter(ImageFilter.EDGE_ENHANCE_MORE))
        
        # 混合原始图像和增强图像
        result = img_array * (1 - mask) + enhanced * mask
        
        return Image.fromarray(result.astype(np.uint8))

    def pixelate_image(self, image, pixel_size):
        if image is None:
            return None
            
        # 获取原始尺寸
        width, height = image.size
        
        # 调整饱和度（保持在颜色处理之前）
        saturation = self.saturation_slider.get()
        if saturation != 1.0:
            enhancer = ImageEnhance.Color(image)
            image = enhancer.enhance(saturation)
        
        # 检测边缘
        edge_image = self.detect_edges(image)
        
        # 缩小图片
        small_width = max(1, int(width // (pixel_size * 0.8)))
        small_height = max(1, int(height // (pixel_size * 0.8)))
        
        # 使用LANCZOS重采样进行缩小，保持颜色更准确
        small_image = image.resize((small_width, small_height), resample=get_resample("LANCZOS"))
        
        # 放大时使用NEAREST确保像素风格
        result = small_image.resize((width, height), resample=get_resample("NEAREST"))
        
        # 增强细节（移到缩放之后）
        detail_level = self.detail_slider.get()
        if detail_level > 0:
            # 先进行像素化
            small_image = image.resize((small_width, small_height), resample=get_resample("LANCZOS"))
            result = small_image.resize((width, height), resample=get_resample("NEAREST"))
            
            # 然后增强细节
            result = self.enhance_details(result, edge_image, detail_level)
        else:
            # 如果不需要增强细节，直接像素化
            small_image = image.resize((small_width, small_height), resample=get_resample("LANCZOS"))
            result = small_image.resize((width, height), resample=get_resample("NEAREST"))
        
        # 最终的颜色调整：使用更温和的量化方法
        result = result.quantize(colors=256, method=2).convert('RGB')

        # 确保像素化图片与原始图片尺寸完全一致
        if result.size != (width, height):
            result = result.resize((width, height), resample=get_resample("LANCZOS"))

        return result
    
    def schedule_update(self, event=None):
        """使用防抖动机制延迟更新"""
        if self.update_timer:
            self.root.after_cancel(self.update_timer)
        self.update_timer = self.root.after(300, self.apply_effect)
    
    def apply_effect(self):
        """应用像素化效果 - 增强状态显示"""
        if not self.original_image:
            return

        # 开始处理状态
        self.update_status("> 正在处理图像...", 0)

        self.pixel_size = self.pixel_slider.get()

        # 中间处理状态
        self.update_status("> 应用像素化效果...", 30)

        self.pixelated_image = self.pixelate_image(self.original_image, self.pixel_size)

        # 显示图像状态
        self.update_status("> 渲染图像显示...", 70)

        # 确保像素化图像生成成功
        if self.pixelated_image:
            self.display_image(self.pixelated_image, self.pixel_display, use_original_size=True)
            # 完成状态
            self.update_status("> 处理完成!", 100)
        else:
            self.update_status("> 处理失败!", 0)

        # 2秒后恢复默认状态
        self.root.after(2000, lambda: self.update_status("> 就绪", 0))

    def update_status(self, message, progress_value=None):
        """更新状态显示和进度条 - 终端风格"""
        # 更新状态文字
        self.status_label.config(text=message)

        # 更新进度条（如果提供了值）
        if progress_value is not None:
            self.progress['value'] = progress_value

        # 强制更新界面
        self.root.update_idletasks()

    def select_image(self):
        """选择图片 - 增强状态反馈"""
        self.update_status("> 选择图片文件...", 0)

        self.image_path = filedialog.askopenfilename(
            filetypes=[("图片文件", "*.png;*.jpg;*.jpeg;*.bmp;*.gif")])

        if self.image_path:
            self.update_status("> 加载图片中...", 20)

            # 重置原始显示尺寸，确保新图片重新计算
            if hasattr(self, 'original_display_size'):
                delattr(self, 'original_display_size')

            # 为避免通道数不一致，统一转为 RGB
            self.original_image = Image.open(self.image_path).convert('RGB')

            self.update_status("> 显示原始图片...", 60)
            self.display_image(self.original_image, self.original_display)

            self.update_status("> 开始处理...", 80)

            # --- 临时移除slider的command，防止重复触发 ---
            pixel_slider_cmd = self.pixel_slider.cget('command')
            detail_slider_cmd = self.detail_slider.cget('command')
            saturation_slider_cmd = self.saturation_slider.cget('command')
            self.pixel_slider.config(command='')
            self.detail_slider.config(command='')
            self.saturation_slider.config(command='')

            # 如有需要可在此设置slider的值
            # self.pixel_slider.set(self.pixel_size)
            # self.detail_slider.set(self.detail_level)
            # self.saturation_slider.set(self.saturation)

            # 恢复command
            self.pixel_slider.config(command=pixel_slider_cmd)
            self.detail_slider.config(command=detail_slider_cmd)
            self.saturation_slider.config(command=saturation_slider_cmd)

            # 只手动schedule_update一次
            self.schedule_update()
        else:
            self.update_status("> 未选择文件", 0)
    
    def on_label_resize(self, label):
        """当 Label 尺寸变化时重新渲染图片"""
        if hasattr(label, 'original_img'):
            # 判断是否是像素显示区域
            use_original_size = (label == self.pixel_display)
            self.display_image(label.original_img, label, use_original_size)

    def display_image(self, image, label, use_original_size=False):
        if image is None:
            return

        # 强制更新布局确保标签尺寸正确
        self.root.update_idletasks()
        label.update_idletasks()

        # 获取标签的实际可用空间
        display_width = label.winfo_width()
        display_height = label.winfo_height()

        # 如果尺寸还没有初始化，等待一下再获取
        if display_width < 10 or display_height < 10:
            self.root.after(100, lambda: self.display_image(image, label, use_original_size))
            return

        # 减去边框宽度，获取实际可用空间
        available_width = display_width - 4
        available_height = display_height - 4

        # 确保有最小可用空间
        if available_width < 50 or available_height < 50:
            available_width = max(available_width, 200)
            available_height = max(available_height, 150)

        # 确保两张图片显示尺寸一致：原始图片计算尺寸，像素图片使用相同尺寸
        if label == self.original_display:
            # 原始图片：根据可用空间计算最佳显示尺寸
            img_w, img_h = image.size
            scale_x = available_width / img_w
            scale_y = available_height / img_h
            scale = min(scale_x, scale_y)  # 保持图片比例，确保不变形

            target_width = max(1, int(img_w * scale))
            target_height = max(1, int(img_h * scale))

            # 保存显示尺寸供像素图片使用，确保一致性
            self.original_display_size = (target_width, target_height)
        else:
            # 像素图片：直接使用原始图片的显示尺寸，确保完全一致
            if hasattr(self, 'original_display_size'):
                target_width, target_height = self.original_display_size
            else:
                # 如果还没有原始显示尺寸，临时计算（通常不会发生）
                img_w, img_h = image.size
                scale_x = available_width / img_w
                scale_y = available_height / img_h
                scale = min(scale_x, scale_y)
                target_width = max(1, int(img_w * scale))
                target_height = max(1, int(img_h * scale))

        # 缩放图片到计算出的尺寸
        resized_image = image.resize((target_width, target_height), resample=get_resample("LANCZOS"))

        photo = ImageTk.PhotoImage(resized_image)

        # 配置图片显示
        label.config(image=photo)
        label.image = photo
        # 保存原图以便窗口尺寸变化时可重新适配
        label.original_img = image

    def open_save_location(self):
        """打开保存文件的位置"""
        if not self.last_save_path or not os.path.exists(self.last_save_path):
            messagebox.showinfo("提示", "还没有保存过图片")
            return
            
        # 获取文件所在的文件夹路径
        folder_path = os.path.dirname(self.last_save_path)
        
        # 根据不同操作系统打开文件夹
        try:
            if platform.system() == "Windows":
                os.startfile(folder_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件夹：{str(e)}")

    def apply_style(self, style_name):
        """应用预设风格"""
        if style_name not in self.styles:
            return

        self.current_style = style_name
        self.update_sliders()
        self.highlight_style_button()

    def highlight_style_button(self):
        """高亮当前选中的风格按钮 - 使用样式变化而非状态变化"""
        for name, btn in self.style_buttons.items():
            if name == self.current_style:
                # 使用选中样式，明显的视觉标识
                btn.configure(style="Selected.StyleButton.TButton")
            else:
                # 使用普通样式
                btn.configure(style="StyleButton.TButton")

    def update_sliders(self):
        """根据当前风格更新滑块的值"""
        if self.current_style in self.styles:
            style = self.styles[self.current_style]
            self.pixel_slider.set(style["pixel"])
            self.detail_slider.set(style["detail"])
            self.saturation_slider.set(style["saturation"])
        self._update_slider_labels() # 更新标签显示
        self.schedule_update()

    def _update_slider_labels(self):
        """Updates the text of the slider value labels."""
        if hasattr(self, 'pixel_value_label'):
            self.pixel_value_label.config(text=str(int(self.pixel_slider.get())))
        if hasattr(self, 'detail_value_label'):
            self.detail_value_label.config(text=str(int(self.detail_slider.get())))
        if hasattr(self, 'saturation_value_label'):
            self.saturation_value_label.config(text=f"{self.saturation_slider.get():.2f}")

if __name__ == "__main__":
    root = tk.Tk()
    app = PixelArtConverter(root)
    root.mainloop()
