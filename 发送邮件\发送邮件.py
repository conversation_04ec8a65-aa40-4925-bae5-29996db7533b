import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional, List
import logging


class EmailConfig:
    """邮件配置类"""

    def __init__(self,
                 sender_email: str,
                 email_password: str,
                 smtp_server: str = 'smtp.qq.com',
                 smtp_port: int = 587):
        self.sender_email = sender_email
        self.email_password = email_password
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port


class EmailSender:
    """邮件发送类"""

    def __init__(self, config: EmailConfig):
        self.config = config
        self.server = None
        self._setup_logging()

    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

    def _connect_smtp(self):
        """连接SMTP服务器"""
        try:
            self.server = smtplib.SMTP(self.config.smtp_server, self.config.smtp_port)
            self.server.starttls()
            self.server.login(self.config.sender_email, self.config.email_password)
            self.logger.info("成功连接到SMTP服务器")
        except Exception as e:
            self.logger.error(f"SMTP服务器连接失败: {e}")
            raise

    def _create_message(self,
                        receiver_email: str,
                        subject: str,
                        body: str) -> MIMEMultipart:
        """创建邮件消息"""
        msg = MIMEMultipart()
        msg['From'] = self.config.sender_email
        msg['To'] = receiver_email
        msg['Subject'] = subject
        msg.attach(MIMEText(body, 'plain'))
        return msg

    def send_email(self,
                   receiver_email: str,
                   subject: str,
                   body: str) -> bool:
        """发送邮件"""
        try:
            if not self.server:
                self._connect_smtp()

            msg = self._create_message(receiver_email, subject, body)
            self.server.send_message(msg)
            self.logger.info(f"邮件已成功发送至 {receiver_email}")
            return True

        except Exception as e:
            self.logger.error(f"邮件发送失败: {e}")
            return False

        finally:
            if self.server:
                self.server.quit()
                self.server = None


def main():
    """主函数示例"""
    # 配置信息
    config = EmailConfig(
        sender_email='<EMAIL>',
        email_password='rjhuvppoqbfebdeh'
    )

    # 创建邮件发送器
    email_sender = EmailSender(config)

    # 发送测试邮件
    email_sender.send_email(
        receiver_email='<EMAIL>',
        subject='测试邮件',
        body='你好，这是通过Python发送的测试邮件。'
    )


if __name__ == '__main__':
    main()
