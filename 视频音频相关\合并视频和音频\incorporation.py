
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import subprocess
import threading
import os
from queue import Queue
import time
import re

class FlatButton(tk.Button):
    def __init__(self, master=None, **kwargs):
        super().__init__(master, **kwargs)
        self.config(
            relief='flat',
            bg='#2196F3',
            fg='white',
            activebackground='#1976D2',
            activeforeground='white',
            font=('Microsoft YaHei UI', 9),  # 调小字体
            cursor='hand2',
            padx=12,  # 减小水平内边距
            pady=6,   # 减小垂直内边距
            border=0,
            width=8   # 减小按钮宽度
        )
        self.bind('<Enter>', self._on_enter)
        self.bind('<Leave>', self._on_leave)

    def _on_enter(self, e):
        self.config(bg='#1976D2')

    def _on_leave(self, e):
        self.config(bg='#2196F3')

class FlatEntry(tk.Entry):
    def __init__(self, master=None, **kwargs):
        super().__init__(master, **kwargs)
        self.config(
            relief='flat',
            font=('Microsoft YaHei UI', 10),
            bg='white',
            selectbackground='#2196F3',
            selectforeground='white',
            insertbackground='#2196F3',
            highlightthickness=1,
            highlightbackground='#E0E0E0',
            highlightcolor='#2196F3'
        )

class VideoAudioMerger:
    def __init__(self):
        self.root = tk.Tk()
        self.event_queue = Queue()
        self.is_merging = False
        self.setup_ui()
        self.check_queue()

    def setup_ui(self):
        self.root.title("视频音频合并工具")
        self.root.configure(bg='#F5F5F5')

        # 设置窗口大小和位置
        window_width = 800
        window_height = 600
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f'{window_width}x{window_height}+{x}+{y}')

        # 主容器
        main_container = tk.Frame(self.root, bg='#F5F5F5', padx=40, pady=30)
        main_container.pack(fill=tk.BOTH, expand=True)

        # 标题
        tk.Label(
            main_container,
            text="视频音频合并工具",
            font=('Microsoft YaHei UI', 24, 'bold'),
            fg='#1976D2',
            bg='#F5F5F5'
        ).pack(pady=(0, 5))

        tk.Label(
            main_container,
            text="将视频和音频文件合并为一个新的视频文件",
            font=('Microsoft YaHei UI', 10),
            fg='#757575',
            bg='#F5F5F5'
        ).pack(pady=(0, 30))

        # 文件选择区域
        input_frame = tk.Frame(main_container, bg='#F5F5F5')
        input_frame.pack(fill=tk.X, pady=10)

        # 视频选择
        tk.Label(
            input_frame,
            text="视频文件",
            font=('Microsoft YaHei UI', 10, 'bold'),
            fg='#424242',
            bg='#F5F5F5'
        ).pack(anchor='w')

        video_frame = tk.Frame(input_frame, bg='#F5F5F5')
        video_frame.pack(fill=tk.X, pady=(5, 15))

        self.video_path = FlatEntry(video_frame)
        self.video_path.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        self.select_video_btn = FlatButton(
            video_frame,
            text="选择视频",
            command=self.select_video_file,
            pady=4  # 增加垂直内边距
        )
        self.select_video_btn.pack(side=tk.RIGHT)

        # 音频选择
        tk.Label(
            input_frame,
            text="音频文件",
            font=('Microsoft YaHei UI', 10, 'bold'),
            fg='#424242',
            bg='#F5F5F5'
        ).pack(anchor='w')

        audio_frame = tk.Frame(input_frame, bg='#F5F5F5')
        audio_frame.pack(fill=tk.X, pady=(5, 15))

        self.audio_path = FlatEntry(audio_frame)
        self.audio_path.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        self.select_audio_btn = FlatButton(
            audio_frame,
            text="选择音频",
            command=self.select_audio_file,
            pady=4  # 增加垂直内边距
        )
        self.select_audio_btn.pack(side=tk.RIGHT)

        # 输出选择
        tk.Label(
            input_frame,
            text="输出文件",
            font=('Microsoft YaHei UI', 10, 'bold'),
            fg='#424242',
            bg='#F5F5F5'
        ).pack(anchor='w')

        output_frame = tk.Frame(input_frame, bg='#F5F5F5')
        output_frame.pack(fill=tk.X, pady=(5, 15))

        self.output_path = FlatEntry(output_frame)
        self.output_path.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        self.select_output_btn = FlatButton(
            output_frame,
            text="选择保存位置",
            command=self.select_output_file,
            pady=4  # 增加垂直内边距
        )
        self.select_output_btn.pack(side=tk.RIGHT)

        # 进度条
        progress_frame = tk.Frame(main_container, bg='#F5F5F5')
        progress_frame.pack(fill=tk.X, pady=20)

        self.status_label = tk.Label(
            progress_frame,
            text="准备就绪",
            font=('Microsoft YaHei UI', 9),
            fg='#757575',
            bg='#F5F5F5'
        )
        self.status_label.pack(fill=tk.X)

        style = ttk.Style()
        style.theme_use('default')
        style.configure(
            "Custom.Horizontal.TProgressbar",
            troughcolor='#E0E0E0',
            background='#2196F3',
            thickness=6
        )

        self.progress_bar = ttk.Progressbar(
            progress_frame,
            style="Custom.Horizontal.TProgressbar",
            mode='determinate'
        )
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))

        # 操作按钮
        button_frame = tk.Frame(main_container, bg='#F5F5F5')
        button_frame.pack(side=tk.BOTTOM, pady=30)  # 改用 side=tk.BOTTOM

        self.merge_btn = FlatButton(
            button_frame,
            text="开始合并",
            command=self.start_merge
        )
        self.merge_btn.pack(side=tk.LEFT, padx=5)

        self.open_output_btn = FlatButton(
            button_frame,
            text="打开输出文件夹",
            command=self.open_output_folder
        )
        self.open_output_btn.pack(side=tk.LEFT, padx=5)
        self.open_output_btn.configure(state='normal')

        # 版权信息
        tk.Label(
            main_container,
            text="© 2025 视频音频工具集",
            font=('Microsoft YaHei UI', 8),
            fg='#9E9E9E',
            bg='#F5F5F5'
        ).pack(side=tk.BOTTOM, pady=(0, 20))

    def select_video_file(self):
        file_path = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mkv *.mov"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.video_path.delete(0, tk.END)
            self.video_path.insert(0, file_path)
            # 自动设置输出路径
            output_dir = os.path.dirname(file_path)
            output_name = "output_" + os.path.basename(file_path)
            output_path = os.path.join(output_dir, output_name)
            self.output_path.delete(0, tk.END)
            self.output_path.insert(0, output_path)

    def select_audio_file(self):
        file_path = filedialog.askopenfilename(
            title="选择音频文件",
            filetypes=[
                ("音频文件", "*.wav *.mp3 *.aac *.m4a *.mp4"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.audio_path.delete(0, tk.END)
            self.audio_path.insert(0, file_path)

    def select_output_file(self):
        file_path = filedialog.asksaveasfilename(
            title="选择保存位置",
            defaultextension=".mp4",
            filetypes=[("MP4文件", "*.mp4")],
            initialfile="output.mp4"
        )
        if file_path:
            self.output_path.delete(0, tk.END)
            self.output_path.insert(0, file_path)

    def update_progress(self, progress, status):
        self.progress_bar['value'] = progress * 100
        self.status_label.config(text=status)

        # 更新状态标签颜色
        if "失败" in status:
            self.status_label.configure(fg='#D32F2F')  # 红色
        elif "完成" in status:
            self.status_label.configure(fg='#388E3C')  # 绿色
        else:
            self.status_label.configure(fg='#757575')  # 灰色

    def check_queue(self):
        try:
            while True:
                event = self.event_queue.get_nowait()
                if event["type"] == "progress":
                    self.update_progress(event["progress"], event["status"])
                elif event["type"] == "complete":
                    self.merge_complete(event.get("success", True), event.get("error", None))
        except:
            pass
        finally:
            self.root.after(100, self.check_queue)

    def merge_complete(self, success, error=None):
        self.is_merging = False
        self.merge_btn.configure(text="开始合并", state='normal')
        if success:
            self.update_progress(1.0, "合并完成！")
            self.open_output_btn.configure(state='normal')
            messagebox.showinfo("成功", "视频和音频合并成功！")
        else:
            self.update_progress(0, f"合并失败: {error}")
            messagebox.showerror("错误", f"合并失败: {error}")

    def start_merge(self):
        video_path = self.video_path.get().strip()
        audio_path = self.audio_path.get().strip()
        output_path = self.output_path.get().strip()

        if not video_path or not audio_path or not output_path:
            messagebox.showerror("错误", "请选择视频文件、音频文件和输出路径")
            return

        if self.is_merging:
            return

        self.is_merging = True
        self.merge_btn.configure(text="合并中...", state='disabled')
        self.open_output_btn.configure(state='disabled')
        self.update_progress(0, "正在准备合并...")

        def run_merge():
            try:
                command = [
                    'ffmpeg', '-i', video_path, '-i', audio_path,
                    '-c:v', 'copy', '-c:a', 'aac', '-strict', 'experimental',
                    output_path
                ]

                process = subprocess.Popen(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True,
                    bufsize=1,
                    encoding='utf-8',
                    errors='replace'  # 处理无法解码的字符
                )

                duration = None
                time_pattern = r'Duration: (\d{2}):(\d{2}):(\d{2})'
                progress_pattern = r'time=(\d{2}):(\d{2}):(\d{2})'

                while True:
                    line = process.stderr.readline()
                    if not line and process.poll() is not None:
                        break

                    if duration is None:
                        duration_match = re.search(time_pattern, line)
                        if duration_match:
                            h, m, s = map(int, duration_match.groups())
                            duration = h * 3600 + m * 60 + s

                    time_match = re.search(progress_pattern, line)
                    if time_match and duration:
                        h, m, s = map(int, time_match.groups())
                        current_time = h * 3600 + m * 60 + s
                        progress = min(current_time / duration, 1.0)
                        self.event_queue.put({
                            "type": "progress",
                            "progress": progress,
                            "status": f"正在合并中... {progress*100:.1f}%"
                        })

                if process.returncode == 0:
                    self.event_queue.put({"type": "complete", "success": True})
                else:
                    error = process.stderr.read()
                    self.event_queue.put({
                        "type": "complete",
                        "success": False,
                        "error": "FFmpeg处理失败"
                    })
            except Exception as e:
                self.event_queue.put({
                    "type": "complete",
                    "success": False,
                    "error": str(e)
                })

        thread = threading.Thread(target=run_merge)
        thread.daemon = True
        thread.start()

    def open_output_folder(self):
        output_path = self.output_path.get().strip()
        if output_path:
            output_dir = os.path.dirname(output_path)
            os.startfile(output_dir)

    def run(self):
        self.root.mainloop()

if __name__ == '__main__':
    app = VideoAudioMerger()
    app.run()
