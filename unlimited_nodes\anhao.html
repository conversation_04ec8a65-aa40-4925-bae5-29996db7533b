
<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>🔐 暗号转换器 - 网址与磁力链接互转</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.1"/><circle cx="80" cy="20" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        /* Light theme styles */
        body.light-theme {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        /* Theme toggle button */
        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.8);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            z-index: 10;
        }

        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 1);
            transform: scale(1.05);
        }

        body.light-theme .theme-toggle {
            background: rgba(255, 255, 255, 0.3);
            color: rgba(45, 52, 54, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.4);
        }

        body.light-theme .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.5);
            color: #2d3436;
            border-color: rgba(255, 255, 255, 0.6);
            transform: scale(1.05);
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 900px;
            width: 100%;
            animation: fadeInUp 0.8s ease-out;
            position: relative;
        }

        body.light-theme .container {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            color: #ffffff;
            margin-bottom: 32px;
            font-size: 32px;
            font-weight: 700;
            text-align: center;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        body.light-theme h1 {
            color: #2d3436;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .converter-section {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 24px;
            align-items: stretch;
            margin-bottom: 24px;
        }

        .input-section, .result-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 24px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .input-section:hover, .result-section:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
        }

        body.light-theme .input-section,
        body.light-theme .result-section {
            background: rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.4);
        }

        body.light-theme .input-section:hover,
        body.light-theme .result-section:hover {
            background: rgba(255, 255, 255, 0.8);
            border-color: rgba(255, 255, 255, 0.6);
        }

        .section-title {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        body.light-theme .section-title {
            color: #2d3436;
        }

        .divider {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 2px;
            height: 60px;
            background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.3), transparent);
        }

        .divider-icon {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
            backdrop-filter: blur(10px);
            z-index: 1;
            position: relative;
        }

        body.light-theme .divider::before {
            background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.2), transparent);
        }

        body.light-theme .divider-icon {
            background: rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.4);
            color: #2d3436;
        }

        textarea {
            width: 100%;
            min-height: 120px;
            padding: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            resize: vertical;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        textarea:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.4);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
        }

        textarea::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        body.light-theme textarea {
            background: rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.4);
            color: #2d3436;
        }

        body.light-theme textarea:focus {
            background: rgba(255, 255, 255, 0.8);
            border-color: rgba(255, 255, 255, 0.6);
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        body.light-theme textarea::placeholder {
            color: rgba(45, 52, 54, 0.5);
        }

        .result-content {
            min-height: 120px;
            padding: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            word-break: break-all;
            overflow-wrap: break-word;
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }

        body.light-theme .result-content {
            background: rgba(255, 255, 255, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #2d3436;
        }

        .copy-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            color: white;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .copy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .copy-btn:active {
            transform: translateY(0);
        }

        .copy-btn.copied {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
        }

        body.light-theme .copy-btn {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
        }

        body.light-theme .copy-btn:hover {
            box-shadow: 0 6px 20px rgba(116, 185, 255, 0.4);
        }

        body.light-theme .copy-btn.copied {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
        }

        .tip {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 16px;
            margin-top: 24px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        body.light-theme .tip {
            background: rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.4);
            color: #2d3436;
        }

        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                padding: 24px;
                margin: 10px;
            }

            h1 {
                font-size: 24px;
                margin-bottom: 24px;
            }

            .converter-section {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .divider {
                transform: rotate(90deg);
                margin: 16px 0;
            }

            .divider::before {
                width: 60px;
                height: 2px;
            }

            .theme-toggle {
                top: 12px;
                right: 12px;
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Theme Toggle Button -->
        <button class="theme-toggle" id="theme-toggle" onclick="toggleTheme()">
            <i class="fas fa-sun" id="theme-icon"></i>
        </button>

        <h1>
            <i class="fas fa-lock"></i>
            暗号转换器
        </h1>

        <div class="converter-section">
            <!-- Input Section -->
            <div class="input-section">
                <div class="section-title">
                    <i class="fas fa-edit"></i>
                    输入内容
                </div>
                <textarea
                    id="myTextArea"
                    maxlength="300"
                    placeholder="请输入暗号或磁力链接/网址..."
                ></textarea>
            </div>

            <!-- Divider -->
            <div class="divider">
                <div class="divider-icon">
                    <i class="fas fa-exchange-alt"></i>
                </div>
            </div>

            <!-- Result Section -->
            <div class="result-section">
                <div class="section-title">
                    <i class="fas fa-magic"></i>
                    转换结果
                </div>
                <div class="result-content" id="transResult">
                    等待输入内容进行转换...
                </div>
                <button class="copy-btn" id="copyResult">
                    <i class="fas fa-copy"></i>
                    一键复制
                </button>
            </div>
        </div>

        <div class="tip">
            <i class="fas fa-lightbulb"></i>
            小贴士：磁力链接转换前删去'&dn=...'及后面的代码可以更省字符哦~
        </div>
    </div>
</body>
<script>
	// Theme toggle functionality
	function toggleTheme() {
		const body = document.body;
		const themeIcon = document.getElementById('theme-icon');

		if (body.classList.contains('light-theme')) {
			body.classList.remove('light-theme');
			themeIcon.className = 'fas fa-sun';
			localStorage.setItem('theme', 'dark');
		} else {
			body.classList.add('light-theme');
			themeIcon.className = 'fas fa-moon';
			localStorage.setItem('theme', 'light');
		}
	}

	// Load saved theme
	function loadTheme() {
		const savedTheme = localStorage.getItem('theme');
		const body = document.body;
		const themeIcon = document.getElementById('theme-icon');

		if (savedTheme === 'light') {
			body.classList.add('light-theme');
			themeIcon.className = 'fas fa-moon';
		}
	}

	var magnetHeader = "magnet:?xt=urn:btih:"
	var nameMap = {
		"赵": "0", "钱": "1", "孙": "2", "李": "3", "周": "4", "吴": "5", "郑": "6", "王": "7", "冯": "8", "陈": "9",
		"褚": "a", "卫": "b", "蒋": "c", "沈": "d", "韩": "e", "杨": "f", "朱": "g", "秦": "h", "尤": "i", "许": "j",
		"何": "k", "吕": "l", "施": "m", "张": "n", "孔": "o", "曹": "p", "严": "q", "华": "r", "金": "s", "魏": "t",
		"陶": "u", "姜": "v", "戚": "w", "谢": "x", "邹": "y", "喻": "z", "福": "A", "水": "B", "窦": "C", "章": "D",
		"云": "E", "苏": "F", "潘": "G", "葛": "H", "奚": "I", "范": "J", "彭": "K", "郎": "L", "鲁": "M", "韦": "N",
		"昌": "O", "马": "P", "苗": "Q", "凤": "R", "花": "S", "方": "T", "俞": "U", "任": "V", "袁": "W", "柳": "X",
		"唐": "Y", "罗": "Z", "薛": ".", "伍": "-", "余": "_", "米": "+", "贝": "=", "姚": "/", "孟": "?", "顾": "#",
		"尹": "%", "江": "&", "钟": "*", "竺": ":", "赖": "|"
	}

	function transToMagnet(str) {
		str = str.replace(/^\s\s*/, '').replace(/\s\s*$/, '')
		var strc = str.split("")
		var c = ''
		for (var i = 0; i < strc.length; i++) {
			var o = cy(nameMap, strc[i])
			c += o
		}

		return isMagnet(magnetHeader + c) ? magnetHeader + c : c
	}

	function transToName(str) {
		str = str.replace(/^\s\s*/, '').replace(/\s\s*$/, '')
		var v = str.replace(/magnet:\?xt=urn:btih:/, "")
		var strc = v.split("")
		var a = ''
		for (var i = 0; i < strc.length; i++) {
			a += ay(nameMap, strc[i])
		}
		return a
	}

	function cy(array, val) {
		for (var key in array) {
			if (key == val) {
				return array[key]
			}
		}
		return ''
	}

	function ay(array, val) {
		for (var key in array) {
			if (array[key] == val) {
				return key
			}
		}
		return ''
	}

	function isLink(text) {
		let regex = /^[\u4E00-\u9FA5]+$/
		return regex.test(text)
	}

	function isMagnet(text) {
		let regex = /^magnet:\?xt=urn:btih:[0-9a-fA-F]{40,}.*$/
		return regex.test(text)
	}

	var timer
	var myTextArea = document.getElementById("myTextArea")
	var copyResult = document.getElementById("copyResult")
	var transResult = document.getElementById("transResult")

	myTextArea.addEventListener("input", function () {
		clearTimeout(timer)
		const inputValue = myTextArea.value.trim()

		if (!inputValue) {
			transResult.textContent = "等待输入内容进行转换..."
			return
		}

		transResult.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 转换中...'
		timer = setTimeout(function () {
			let tempText = myTextArea.value.replace(/\s+/g, "")
			const result = isLink(tempText) ? transToMagnet(tempText) : transToName(tempText)
			transResult.textContent = result || "转换失败，请检查输入格式"
		}, 500)
	})
	// Enhanced copy functionality with visual feedback
	async function copyToClipboard(text, button) {
		try {
			if (navigator.clipboard && window.isSecureContext) {
				await navigator.clipboard.writeText(text);
			} else {
				// Fallback for non-HTTPS environments
				const textArea = document.createElement('textarea');
				textArea.value = text;
				textArea.style.position = 'fixed';
				textArea.style.left = '-999999px';
				textArea.style.top = '-999999px';
				document.body.appendChild(textArea);
				textArea.focus();
				textArea.select();
				document.execCommand('copy');
				document.body.removeChild(textArea);
			}

			// Visual feedback
			const originalHTML = button.innerHTML;
			button.innerHTML = '<i class="fas fa-check"></i> 复制成功';
			button.classList.add('copied');

			setTimeout(() => {
				button.innerHTML = originalHTML;
				button.classList.remove('copied');
			}, 2000);

		} catch (err) {
			console.error('复制失败:', err);
			// Fallback alert
			alert('复制失败，请手动复制');
		}
	}

	copyResult.addEventListener("click", function () {
		let result = transResult.textContent.trim()

		if (!result || result === "等待输入内容进行转换..." || result === "转换失败，请检查输入格式" || result.includes("转换中")) {
			alert("请先输入有效内容并等待转换完成~")
			return
		}

		copyToClipboard(result, copyResult)
	})

	// Load theme on page load
	document.addEventListener('DOMContentLoaded', loadTheme);
</script>
</html>
