#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件批量重命名工具
支持自定义前缀和数字递增重命名
支持所有文件类型（图片、视频、文档等）
使用CustomTkinter创建现代化UI界面
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import filedialog, messagebox
import os
import pathlib
import threading
import re
import locale
from functools import cmp_to_key

# 设置CustomTkinter外观
ctk.set_appearance_mode("System")  # 系统模式
ctk.set_default_color_theme("blue")  # 蓝色主题

class ImageRenamerApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("文件批量重命名工具")

        # 设置窗口大小和居中显示
        window_width = 1000
        window_height = 800
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.minsize(900, 700)
        
        # 支持所有文件类型（移除格式限制）
        
        # 变量
        self.folder_path = tk.StringVar()
        self.filename_template = tk.StringVar(value="File_01")
        self.group_by_extension = tk.BooleanVar()
        self.sort_method = tk.StringVar(value="自然排序 (1-9)")

        # 排序选项
        self.sort_options = [
            "文件名排序 (A-Z)",
            "文件名排序 (Z-A)",
            "自然排序 (1-9)",
            "自然排序 (9-1)",
            "创建时间排序 (旧-新)",
            "创建时间排序 (新-旧)",
            "修改时间排序 (旧-新)",
            "修改时间排序 (新-旧)",
            "文件大小排序 (小-大)",
            "文件大小排序 (大-小)"
        ]

        # 文件列表
        self.files = []
        self.file_groups = {}  # 按扩展名分组的文件字典
        self.preview_data = []
        self.preview_settings = {}  # 存储预览时的设置
        
        self.create_widgets()

    def natural_sort_key(self, text):
        """自然排序的键函数，正确处理数字"""
        def convert(text):
            return int(text) if text.isdigit() else text.lower()
        return [convert(c) for c in re.split('([0-9]+)', str(text))]

    def sort_files(self, files):
        """根据选择的排序方式对文件进行排序"""
        sort_method = self.sort_method.get()

        if sort_method == "文件名排序 (A-Z)":
            return sorted(files, key=lambda f: f.name.lower())
        elif sort_method == "文件名排序 (Z-A)":
            return sorted(files, key=lambda f: f.name.lower(), reverse=True)
        elif sort_method == "自然排序 (1-9)":
            return sorted(files, key=lambda f: self.natural_sort_key(f.name))
        elif sort_method == "自然排序 (9-1)":
            return sorted(files, key=lambda f: self.natural_sort_key(f.name), reverse=True)
        elif sort_method == "创建时间排序 (旧-新)":
            return sorted(files, key=lambda f: f.stat().st_ctime)
        elif sort_method == "创建时间排序 (新-旧)":
            return sorted(files, key=lambda f: f.stat().st_ctime, reverse=True)
        elif sort_method == "修改时间排序 (旧-新)":
            return sorted(files, key=lambda f: f.stat().st_mtime)
        elif sort_method == "修改时间排序 (新-旧)":
            return sorted(files, key=lambda f: f.stat().st_mtime, reverse=True)
        elif sort_method == "文件大小排序 (小-大)":
            return sorted(files, key=lambda f: f.stat().st_size)
        elif sort_method == "文件大小排序 (大-小)":
            return sorted(files, key=lambda f: f.stat().st_size, reverse=True)
        else:
            # 默认使用自然排序
            return sorted(files, key=lambda f: self.natural_sort_key(f.name))

    def on_sort_method_changed(self, value=None):
        """排序方式改变时的回调函数"""
        if self.files:  # 如果已经加载了文件，重新排序
            self.scan_files()
            # 清空预览，提示用户重新预览
            self.preview_data = []
            self.preview_text.delete("1.0", "end")
            self.preview_text.insert("1.0", "排序方式已更改，请重新预览")

    def create_widgets(self):
        """创建UI组件"""
        # 主容器
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 左侧设置面板
        settings_frame = ctk.CTkFrame(main_frame)
        settings_frame.pack(side="left", fill="y", padx=(0, 8), pady=0)
        settings_frame.configure(width=350)
        
        # 标题
        title_label = ctk.CTkLabel(settings_frame, text="文件重命名设置",
                                 font=ctk.CTkFont(size=18, weight="bold"))
        title_label.pack(pady=(15, 20))
        
        # 文件夹选择
        folder_frame = ctk.CTkFrame(settings_frame)
        folder_frame.pack(fill="x", padx=15, pady=(0, 12))

        ctk.CTkLabel(folder_frame, text="📁 选择文件夹",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(12, 6))

        folder_entry = ctk.CTkEntry(folder_frame, textvariable=self.folder_path,
                                   placeholder_text="请选择包含文件的文件夹",
                                   height=32)
        folder_entry.pack(fill="x", padx=15, pady=(0, 12))

        # 按钮容器
        button_container = ctk.CTkFrame(folder_frame, fg_color="transparent")
        button_container.pack(fill="x", pady=(0, 15))

        browse_btn = ctk.CTkButton(button_container, text="浏览文件夹",
                                  command=self.browse_folder,
                                  height=32, width=120)
        browse_btn.pack(side="left", padx=(15, 8))

        reload_btn = ctk.CTkButton(button_container, text="重新加载",
                                  command=self.reload_files,
                                  height=32, width=100)
        reload_btn.pack(side="left", padx=(0, 15))
        
        # 重命名设置
        rename_frame = ctk.CTkFrame(settings_frame)
        rename_frame.pack(fill="x", padx=15, pady=(0, 12))

        ctk.CTkLabel(rename_frame, text="⚙️ 重命名设置",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(12, 8))

        # 文件名模板设置
        ctk.CTkLabel(rename_frame, text="文件名模板:",
                    font=ctk.CTkFont(size=12, weight="bold")).pack(anchor="w", padx=15, pady=(0, 5))
        template_entry = ctk.CTkEntry(rename_frame, textvariable=self.filename_template,
                                     placeholder_text="例如: File_01 或 Video_001_HD",
                                     height=32)
        template_entry.pack(fill="x", padx=15, pady=(0, 12))

        # 选项容器
        options_frame = ctk.CTkFrame(rename_frame, fg_color="transparent")
        options_frame.pack(fill="x", padx=15, pady=(0, 12))

        # 分组选项
        group_checkbox = ctk.CTkCheckBox(options_frame,
                                        text="按文件类型分组重命名",
                                        variable=self.group_by_extension,
                                        font=ctk.CTkFont(size=12))
        group_checkbox.pack(anchor="w", pady=(0, 8))

        # 排序选项
        sort_label_frame = ctk.CTkFrame(options_frame, fg_color="transparent")
        sort_label_frame.pack(fill="x", pady=(0, 5))

        ctk.CTkLabel(sort_label_frame, text="排序方式:",
                    font=ctk.CTkFont(size=12, weight="bold")).pack(anchor="w")

        sort_combobox = ctk.CTkComboBox(options_frame,
                                       values=self.sort_options,
                                       variable=self.sort_method,
                                       command=self.on_sort_method_changed,
                                       font=ctk.CTkFont(size=11),
                                       height=32,
                                       width=280)
        sort_combobox.pack(anchor="w", pady=(0, 8))

        # 说明文本
        help_frame = ctk.CTkFrame(rename_frame, fg_color=("gray90", "gray20"))
        help_frame.pack(fill="x", padx=15, pady=(0, 10))

        help_text = ctk.CTkLabel(help_frame,
                                text="💡 使用说明：\n"
                                     "• 输入第一个文件的完整名称（不含扩展名）\n"
                                     "• 程序会自动递增数字部分，支持所有文件类型\n"
                                     "• 例如：MyFile_001 → MyFile_002 → MyFile_003\n"
                                     "• 分组模式：每种文件类型独立编号\n"
                                     "• 排序方式：支持正序/倒序，自然排序最接近资源管理器",
                                font=ctk.CTkFont(size=9),
                                text_color=("gray40", "gray70"),
                                justify="left")
        help_text.pack(padx=10, pady=8)
        
        # 操作按钮
        button_frame = ctk.CTkFrame(settings_frame)
        button_frame.pack(fill="x", padx=15, pady=(0, 10))

        ctk.CTkLabel(button_frame, text="🚀 操作",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(12, 8))

        preview_btn = ctk.CTkButton(button_frame, text="📋 预览重命名",
                                   command=self.preview_rename,
                                   font=ctk.CTkFont(size=13, weight="bold"),
                                   height=34)
        preview_btn.pack(fill="x", padx=15, pady=(0, 6))

        execute_btn = ctk.CTkButton(button_frame, text="✅ 执行重命名",
                                   command=self.execute_rename,
                                   font=ctk.CTkFont(size=13, weight="bold"),
                                   fg_color="green",
                                   hover_color="darkgreen",
                                   height=34)
        execute_btn.pack(fill="x", padx=15, pady=(0, 12))
        
        # 状态区域
        status_frame = ctk.CTkFrame(settings_frame)
        status_frame.pack(fill="x", padx=15, pady=(0, 10))

        ctk.CTkLabel(status_frame, text="📊 状态",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(12, 6))

        # 进度条
        self.progress_bar = ctk.CTkProgressBar(status_frame, height=14)
        self.progress_bar.pack(fill="x", padx=15, pady=(0, 6))
        self.progress_bar.set(0)

        # 状态标签
        self.status_label = ctk.CTkLabel(status_frame, text="🟢 就绪",
                                        font=ctk.CTkFont(size=12))
        self.status_label.pack(pady=(0, 12))
        
        # 右侧预览面板
        preview_frame = ctk.CTkFrame(main_frame)
        preview_frame.pack(side="right", fill="both", expand=True, padx=(8, 0), pady=0)

        # 预览标题
        preview_title = ctk.CTkLabel(preview_frame, text="👁️ 重命名预览",
                                    font=ctk.CTkFont(size=18, weight="bold"))
        preview_title.pack(pady=(20, 15))

        # 预览列表
        self.preview_text = ctk.CTkTextbox(preview_frame,
                                          font=ctk.CTkFont(family="Consolas", size=11),
                                          corner_radius=8)
        self.preview_text.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
    def browse_folder(self):
        """浏览文件夹"""
        folder = filedialog.askdirectory(title="选择包含文件的文件夹")
        if folder:
            self.folder_path.set(folder)
            self.scan_files()

    def reload_files(self):
        """重新加载当前文件夹"""
        if not self.folder_path.get():
            messagebox.showwarning("提示", "请先选择文件夹")
            return

        self.scan_files()
        # 清空预览
        self.preview_data = []
        self.preview_text.delete("1.0", "end")
        self.preview_text.insert("1.0", "文件夹已重新加载，请重新预览")

        messagebox.showinfo("完成", f"已重新加载文件夹\n找到 {len(self.files)} 个文件")
            
    def scan_files(self):
        """扫描所有文件"""
        folder = self.folder_path.get()
        if not folder or not os.path.exists(folder):
            return

        self.files = []
        self.file_groups = {}
        try:
            for file_path in pathlib.Path(folder).iterdir():
                if file_path.is_file():  # 扫描所有文件，不限制格式
                    self.files.append(file_path)

                    # 按扩展名分组
                    ext = file_path.suffix.lower()
                    if ext not in self.file_groups:
                        self.file_groups[ext] = []
                    self.file_groups[ext].append(file_path)

            # 使用选择的排序方式对文件进行排序
            self.files = self.sort_files(self.files)

            # 对每个分组内的文件也进行排序
            for ext in self.file_groups:
                self.file_groups[ext] = self.sort_files(self.file_groups[ext])

            self.status_label.configure(text=f"找到 {len(self.files)} 个文件")

        except Exception as e:
            messagebox.showerror("错误", f"扫描文件夹时出错: {str(e)}")
        
    def parse_template(self, template: str) -> tuple:
        """解析文件名模板，提取前缀、数字、后缀"""
        # 使用正则表达式匹配：前缀 + 数字 + 后缀
        # 使用非贪婪匹配，找到最后一组连续数字
        match = re.search(r'(.*?)(\d+)([^0-9]*)$', template)
        if match:
            prefix = match.group(1)
            number_str = match.group(2)
            suffix = match.group(3)
            start_number = int(number_str)
            digit_count = len(number_str)
            return prefix, start_number, digit_count, suffix
        else:
            # 如果没有找到数字，默认在末尾添加数字
            return template, 1, 5, ""

    def generate_new_name(self, index: int, original_extension: str, group_index: int = None) -> str:
        """生成新文件名"""
        template = self.filename_template.get().strip()
        if not template:
            template = "File_01"

        prefix, start_number, digit_count, suffix = self.parse_template(template)

        # 如果启用分组模式，使用组内索引；否则使用全局索引
        if self.group_by_extension.get() and group_index is not None:
            current_number = start_number + group_index
        else:
            current_number = start_number + index

        number_str = str(current_number).zfill(digit_count)

        return f"{prefix}{number_str}{suffix}{original_extension}"
        
    def validate_inputs(self) -> bool:
        """验证用户输入"""
        if not self.folder_path.get():
            messagebox.showerror("错误", "请选择文件夹")
            return False

        if not self.files:
            messagebox.showerror("错误", "所选文件夹中没有找到文件")
            return False

        template = self.filename_template.get().strip()
        if not template:
            messagebox.showerror("错误", "请输入文件名模板")
            return False

        # 验证模板格式
        _, start_number, _, _ = self.parse_template(template)
        if start_number < 0:
            messagebox.showerror("错误", "模板中的数字不能为负数")
            return False

        return True

    def preview_rename(self):
        """预览重命名结果"""
        if not self.validate_inputs():
            return

        # 保存预览时的设置
        self.preview_settings = {
            'template': self.filename_template.get().strip(),
            'group_by_extension': self.group_by_extension.get(),
            'sort_method': self.sort_method.get()
        }

        self.preview_data = []
        preview_text = "重命名预览:\n" + "="*50 + "\n\n"

        if self.group_by_extension.get():
            # 分组模式预览
            total_count = 0
            for ext in sorted(self.file_groups.keys()):
                files_in_group = self.file_groups[ext]
                if not files_in_group:
                    continue

                # 显示分组标题
                ext_display = ext if ext else "(无扩展名)"
                preview_text += f"=== {ext_display} 文件 ({len(files_in_group)}个) ===\n"

                for group_index, file_path in enumerate(files_in_group):
                    old_name = file_path.name
                    new_name = self.generate_new_name(total_count, file_path.suffix, group_index)

                    self.preview_data.append((file_path, new_name))
                    preview_text += f"{group_index+1:3d}. {old_name}\n"
                    preview_text += f"     → {new_name}\n\n"
                    total_count += 1

                preview_text += "\n"
        else:
            # 普通模式预览
            for i, file_path in enumerate(self.files):
                old_name = file_path.name
                new_name = self.generate_new_name(i, file_path.suffix)

                self.preview_data.append((file_path, new_name))
                preview_text += f"{i+1:3d}. {old_name}\n"
                preview_text += f"     → {new_name}\n\n"

        # 检查重复文件名
        new_names = [item[1] for item in self.preview_data]
        if len(new_names) != len(set(new_names)):
            preview_text += "\n⚠️ 警告: 发现重复的新文件名!\n"

        self.preview_text.delete("1.0", "end")
        self.preview_text.insert("1.0", preview_text)

        self.status_label.configure(text=f"预览完成 - {len(self.files)} 个文件")

    def execute_rename(self):
        """执行重命名操作"""
        if not self.validate_inputs():
            return

        if not self.preview_data:
            messagebox.showwarning("警告", "请先预览重命名结果")
            return

        # 检查设置是否与预览时一致
        current_settings = {
            'template': self.filename_template.get().strip(),
            'group_by_extension': self.group_by_extension.get(),
            'sort_method': self.sort_method.get()
        }

        if self.preview_settings != current_settings:
            result = messagebox.askyesno("设置已更改",
                                       "检测到设置已更改，建议重新预览。\n\n"
                                       "是否继续执行重命名？")
            if not result:
                return

        # 确认对话框
        result = messagebox.askyesno("确认重命名",
                                   f"确定要重命名 {len(self.preview_data)} 个文件吗?\n\n"
                                   "此操作不可撤销!")
        if not result:
            return

        # 在新线程中执行重命名
        threading.Thread(target=self._do_rename, daemon=True).start()

    def _do_rename(self):
        """执行重命名的线程函数"""
        try:
            self.status_label.configure(text="正在重命名...")
            self.progress_bar.set(0)

            success_count = 0
            error_count = 0

            # 添加调试信息
            print(f"开始重命名，共 {len(self.preview_data)} 个文件")

            for i, (old_path, new_name) in enumerate(self.preview_data):
                try:
                    new_path = old_path.parent / new_name

                    # 添加调试信息
                    print(f"重命名: {old_path.name} → {new_name}")

                    # 检查目标文件是否已存在
                    if new_path.exists() and new_path != old_path:
                        error_count += 1
                        print(f"跳过重命名（目标文件已存在）: {new_name}")
                        continue

                    old_path.rename(new_path)
                    success_count += 1

                except Exception as e:
                    error_count += 1
                    print(f"重命名失败 {old_path.name}: {str(e)}")

                # 更新进度
                progress = (i + 1) / len(self.preview_data)
                self.progress_bar.set(progress)

            # 完成后更新状态
            if error_count == 0:
                self.status_label.configure(text=f"重命名完成! 成功: {success_count}")
                messagebox.showinfo("完成", f"重命名完成!\n成功: {success_count} 个文件")
            else:
                self.status_label.configure(text=f"完成 - 成功: {success_count}, 失败: {error_count}")
                messagebox.showwarning("完成", f"重命名完成!\n成功: {success_count} 个文件\n失败: {error_count} 个文件")

            # 重新扫描文件
            self.scan_files()
            self.preview_data = []
            self.preview_text.delete("1.0", "end")
            self.preview_text.insert("1.0", "重命名完成，请重新预览查看结果")

        except Exception as e:
            self.status_label.configure(text="重命名失败")
            messagebox.showerror("错误", f"重命名过程中出错: {str(e)}")

    def run(self):
        """运行应用程序"""
        self.root.mainloop()

def main():
    """主函数"""
    app = ImageRenamerApp()
    app.run()

if __name__ == "__main__":
    main()
