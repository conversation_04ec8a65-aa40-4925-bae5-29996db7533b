// ==UserScript==
// @name         智能图片批量下载器
// @namespace    http://tampermonkey.net/
// @version      1.1.0
// @description  在任意网站批量下载图片，支持指定容器、图片预览、自定义命名、压缩包下载
// <AUTHOR>
// @match        *://*/*
// @grant        none
// @run-at       document-end
// @require      https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js
// ==/UserScript==

(function() {
    'use strict';

    // 全局变量
    let isUIVisible = false;
    let selectedImages = new Set();
    let isInitialized = false; // 防止重复初始化

    // 拖拽状态管理对象
    const dragState = {
        isDragging: false,
        hasBeenDragged: false,
        startX: 0,
        startY: 0,
        startLeft: 0,
        startTop: 0,
        resetTimer: null,

        // 重置拖拽状态的安全方法
        reset() {
            this.isDragging = false;
            this.clearResetTimer();
        },

        // 安全地设置拖拽完成状态
        setDragged() {
            this.hasBeenDragged = true;
            this.scheduleReset();
        },

        // 清理重置定时器
        clearResetTimer() {
            if (this.resetTimer) {
                clearTimeout(this.resetTimer);
                this.resetTimer = null;
            }
        },

        // 安排状态重置
        scheduleReset() {
            this.clearResetTimer();
            this.resetTimer = setTimeout(() => {
                this.hasBeenDragged = false;
                this.resetTimer = null;
            }, DRAG_RESET_DELAY);
        }
    };

    // DOM元素缓存
    let cachedElements = {};

    // 事件监听器管理
    let eventListenersAdded = false;
    let eventListenerRefs = new Map(); // 存储事件监听器引用

    // URL资源管理
    let activeURLManagers = new Set(); // 存储活跃的URL管理器

    // 常量定义
    const DRAG_THRESHOLD = 5; // 拖拽阈值，防止误触
    const DRAG_RESET_DELAY = 100; // 拖拽标志重置延迟(ms)
    const TIP_DURATION = {
        SUCCESS: 4000,
        WARNING: 5000,
        ERROR: 5000,
        DEFAULT: 3000
    };
    const TIP_ICONS = {
        success: '✅',
        warning: '⚠️',
        error: '❌'
    };

    // 扫描性能配置
    const SCAN_CONFIG = {
        maxImages: 500,        // 最大处理图片数量
        batchSize: 50,         // 分批处理大小
        yieldInterval: 10,     // 让出控制权的间隔(ms)
        progressUpdateInterval: 20  // 进度更新间隔
    };

    // 网络请求配置
    const NETWORK_CONFIG = {
        timeout: 30000,        // 请求超时时间(ms)
        maxRetries: 3,         // 最大重试次数
        retryDelay: 1000,      // 基础重试延迟(ms)
        retryMultiplier: 2     // 重试延迟倍数(指数退避)
    };

    // 下载并发控制配置
    const DOWNLOAD_CONFIG = {
        maxConcurrent: 3,      // 最大并发下载数量
        queueCheckInterval: 100 // 队列检查间隔(ms)
    };

    // 图片过滤配置
    const FILTER_CONFIG = {
        defaultFormats: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'], // 默认支持的格式
        defaultMinWidth: 50,    // 默认最小宽度
        defaultMinHeight: 50,   // 默认最小高度
        defaultMaxFileSize: 50 * 1024 * 1024, // 默认最大文件大小 50MB
        enabledByDefault: false // 默认是否启用过滤
    };

    // 移动端配置
    const MOBILE_CONFIG = {
        isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
        touchThreshold: 10,     // 触摸拖拽阈值
        tapTimeout: 300,        // 点击超时时间(ms)
        doubleTapDelay: 300     // 双击延迟时间(ms)
    };

    // DOM元素缓存函数
    function getCachedElement(id) {
        if (!cachedElements[id]) {
            cachedElements[id] = document.getElementById(id);
        }
        return cachedElements[id];
    }

    // 清理函数
    function cleanup() {
        // 清理事件监听器
        if (eventListenersAdded) {
            document.removeEventListener('click', handleOutsideClick);
            document.removeEventListener('keydown', handleKeyboardShortcuts);
            window.removeEventListener('beforeunload', cleanup);

            // 清理所有存储的事件监听器引用
            eventListenerRefs.forEach((listeners, element) => {
                if (element && typeof element.removeEventListener === 'function') {
                    listeners.forEach(([eventType, handler]) => {
                        element.removeEventListener(eventType, handler);
                    });
                }
            });
            eventListenerRefs.clear();
            eventListenersAdded = false;
        }

        // 清理所有活跃的URL资源
        activeURLManagers.forEach(manager => {
            try {
                manager.cleanup();
            } catch (error) {
                console.warn('清理URL资源时出错:', error);
            }
        });
        activeURLManagers.clear();

        // 清理拖拽状态
        dragState.clearResetTimer();
        dragState.isDragging = false;
        dragState.hasBeenDragged = false;

        // 清理下载队列
        if (globalDownloadQueue) {
            globalDownloadQueue.reset();
            globalDownloadQueue = null;
        }

        // 清理进度管理器
        if (globalProgressManager) {
            globalProgressManager.reset();
            globalProgressManager = null;
        }

        // 清理缓存
        cachedElements = {};
        selectedImages.clear();

        // 移除UI
        const mainContainer = document.getElementById('img-downloader-main');
        if (mainContainer) {
            mainContainer.remove();
        }

        // 清理样式
        const styles = document.querySelectorAll('[data-img-downloader], [data-img-downloader-force]');
        styles.forEach(style => style.remove());

        isInitialized = false;
    }

    // 创建主UI容器
    function createMainUI() {
        // 防止重复初始化
        if (isInitialized) {
            return;
        }

        // 移动端优化：确保viewport设置正确
        if (MOBILE_CONFIG.isMobile) {
            let viewport = document.querySelector('meta[name="viewport"]');
            if (!viewport) {
                viewport = document.createElement('meta');
                viewport.name = 'viewport';
                viewport.content = 'width=device-width, initial-scale=1.0, user-scalable=yes';
                document.head.appendChild(viewport);
            } else if (!viewport.content.includes('user-scalable')) {
                // 确保允许用户缩放，以便更好的可访问性
                viewport.content += ', user-scalable=yes';
            }
        }

        const mainContainer = document.createElement('div');
        mainContainer.id = 'img-downloader-main';
        mainContainer.innerHTML = `
            <div class="img-downloader-toggle-btn" id="toggleBtn" style="color: white !important;">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="white" style="color: white !important; fill: white !important;">
                    <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" style="fill: white !important;"/>
                </svg>
                <span style="color: white !important;">图片下载</span>
            </div>
            <div class="img-downloader-panel" id="mainPanel" style="display: none;">
                <div class="panel-header">
                    <h3>图片批量下载器</h3>
                    <button class="close-btn" id="closeBtn">×</button>
                </div>
                <div class="panel-content">
                    <div class="control-section">
                        <label>容器选择器:</label>
                        <input type="text" id="containerSelector" value="body" placeholder="CSS选择器，如: .content, #main">
                    </div>
                    <div class="naming-section">
                        <div class="naming-row-inline">
                            <div class="naming-item">
                                <label>文件名前缀:</label>
                                <input type="text" id="namePrefix" value="image" placeholder="文件名前缀">
                            </div>
                            <div class="naming-item">
                                <label>起始序号:</label>
                                <input type="number" id="startNumber" value="1" min="1">
                            </div>
                        </div>
                        <button id="scanBtn">扫描图片</button>
                    </div>
                    <div class="download-mode-section">
                        <label>下载模式:</label>
                        <div class="radio-group">
                            <label class="radio-label">
                                <input type="radio" name="downloadMode" value="individual">
                                <span>单独下载</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="downloadMode" value="zip" checked>
                                <span>压缩包下载</span>
                            </label>
                        </div>
                    </div>
                    <div class="filter-section">
                        <div class="filter-header">
                            <label>
                                <input type="checkbox" id="enableFilter">
                                启用图片过滤
                            </label>
                            <button type="button" id="resetFilterBtn" class="reset-filter-btn">重置</button>
                        </div>
                        <div class="filter-content" id="filterContent" style="display: none;">
                            <div class="filter-row">
                                <label>图片格式:</label>
                                <div class="checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="imageFormat" value="jpg" checked>
                                        <span>JPG</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="imageFormat" value="png" checked>
                                        <span>PNG</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="imageFormat" value="gif" checked>
                                        <span>GIF</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="imageFormat" value="webp" checked>
                                        <span>WebP</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="imageFormat" value="svg" checked>
                                        <span>SVG</span>
                                    </label>
                                </div>
                            </div>
                            <div class="filter-row">
                                <label>最小尺寸:</label>
                                <div class="size-inputs">
                                    <input type="number" id="minWidth" value="50" min="1" placeholder="宽度">
                                    <span>×</span>
                                    <input type="number" id="minHeight" value="50" min="1" placeholder="高度">
                                    <span>像素</span>
                                </div>
                            </div>
                            <div class="filter-row">
                                <label>最大文件大小:</label>
                                <div class="size-inputs">
                                    <input type="number" id="maxFileSize" value="50" min="1" placeholder="大小">
                                    <select id="fileSizeUnit">
                                        <option value="1024">KB</option>
                                        <option value="1048576" selected>MB</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="action-section">
                        <button id="selectAllBtn">全选</button>
                        <button id="deselectAllBtn">取消全选</button>
                        <button id="downloadSelectedBtn">下载选中</button>
                        <span class="status-text" id="statusText">就绪</span>
                    </div>
                    <div class="progress-section" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div class="progress-info">
                            <div class="progress-text" id="progressText">0/0</div>
                            <div class="progress-detail" id="progressDetail"></div>
                        </div>
                    </div>
                    <div class="images-grid" id="imagesGrid">
                        <div class="no-images">点击"扫描图片"开始</div>
                    </div>
                    <div class="shortcuts-info">
                        <small>快捷键: Ctrl+S扫描 | Ctrl+A全选 | Ctrl+D下载 | Del取消全选 | Esc关闭<br>
                        💡 压缩包模式可将所有图片打包为一个ZIP文件下载</small>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = getStyles();
        style.setAttribute('data-img-downloader', 'true');
        document.head.appendChild(style);

        // 添加额外的强制样式
        const forceStyle = document.createElement('style');
        forceStyle.textContent = getForceStyles();
        forceStyle.setAttribute('data-img-downloader-force', 'true');
        document.head.appendChild(forceStyle);

        document.body.appendChild(mainContainer);
        bindEvents();

        // 强制设置按钮颜色，防止被其他样式覆盖
        forceButtonColor();

        // 标记已初始化
        isInitialized = true;

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', cleanup);
    }

    // 简化的按钮颜色保护函数
    function forceButtonColor() {
        const toggleBtn = document.getElementById('toggleBtn');
        if (!toggleBtn) return;

        // 简单有效的颜色设置
        function setWhiteColor() {
            toggleBtn.style.setProperty('color', 'white', 'important');

            // 设置所有子元素
            const allElements = toggleBtn.querySelectorAll('*');
            allElements.forEach(element => {
                element.style.setProperty('color', 'white', 'important');
                element.style.setProperty('fill', 'white', 'important');
            });
        }

        // 立即设置
        setWhiteColor();

        // 只在鼠标事件时检查，并记录引用以便清理
        addEventListenerWithRef(toggleBtn, 'mouseenter', setWhiteColor);
        addEventListenerWithRef(toggleBtn, 'click', setWhiteColor);
    }

    // CSS样式
    function getStyles() {
        return `
            #img-downloader-main {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                transition: transform 0.1s ease-out;
            }

            #img-downloader-main.dragging {
                opacity: 0.8;
                transform: scale(1.05);
                box-shadow: 0 8px 25px rgba(0,0,0,0.4);
                cursor: grabbing !important;
                z-index: 10001;
            }

            #img-downloader-main .img-downloader-toggle-btn {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                color: white !important;
                padding: 12px 16px !important;
                border-radius: 25px !important;
                cursor: grab !important;
                display: flex !important;
                align-items: center !important;
                gap: 8px !important;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2) !important;
                transition: all 0.3s ease !important;
                font-size: 14px !important;
                font-weight: 500 !important;
                user-select: none !important;
                text-shadow: 0 1px 2px rgba(0,0,0,0.3) !important;
                border: none !important;
                outline: none !important;
            }

            #img-downloader-main .img-downloader-toggle-btn,
            #img-downloader-main .img-downloader-toggle-btn *,
            #img-downloader-main .img-downloader-toggle-btn span,
            #img-downloader-main .img-downloader-toggle-btn svg,
            #img-downloader-main .img-downloader-toggle-btn path {
                color: white !important;
                fill: white !important;
                stroke: white !important;
            }

            #img-downloader-main .img-downloader-toggle-btn:hover {
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 20px rgba(0,0,0,0.3) !important;
                color: white !important;
            }

            #img-downloader-main .img-downloader-toggle-btn:hover,
            #img-downloader-main .img-downloader-toggle-btn:hover *,
            #img-downloader-main .img-downloader-toggle-btn:hover span,
            #img-downloader-main .img-downloader-toggle-btn:hover svg,
            #img-downloader-main .img-downloader-toggle-btn:hover path {
                color: white !important;
                fill: white !important;
                stroke: white !important;
            }

            .img-downloader-panel {
                position: absolute;
                top: 60px;
                right: 0;
                width: 400px;
                max-height: 600px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                overflow: hidden;
                border: 1px solid #e1e5e9;
            }

            .panel-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 16px 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .panel-header h3 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
            }

            .close-btn {
                background: none;
                border: none;
                color: white;
                font-size: 24px;
                cursor: pointer;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background-color 0.2s;
            }

            .close-btn:hover {
                background-color: rgba(255,255,255,0.2);
            }

            .panel-content {
                padding: 16px;
                max-height: 520px;
                overflow-y: auto;
                padding-bottom: 60px; /* 为底部说明文字留出空间 */
            }

            .control-section, .naming-section, .download-mode-section, .filter-section, .action-section {
                margin-bottom: 12px;
                padding-bottom: 10px;
                border-bottom: 1px solid #f0f0f0;
            }

            .control-section:last-child, .naming-section:last-child, .download-mode-section:last-child, .filter-section:last-child, .action-section:last-child {
                border-bottom: none;
                margin-bottom: 0;
                padding-bottom: 0;
            }

            #img-downloader-main label {
                display: block;
                margin-bottom: 8px;
                font-weight: 500;
                color: #333;
                font-size: 14px;
            }

            #img-downloader-main input[type="text"], #img-downloader-main input[type="number"] {
                width: 100%;
                padding: 10px 12px;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                font-size: 14px;
                transition: border-color 0.2s;
                box-sizing: border-box;
            }

            #img-downloader-main input[type="text"]:focus, #img-downloader-main input[type="number"]:focus {
                outline: none;
                border-color: #667eea;
            }

            #img-downloader-main button {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.2s;
                margin-right: 8px;
                margin-top: 8px;
            }

            #img-downloader-main button:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            }

            #img-downloader-main button:active {
                transform: translateY(0);
            }

            #scanBtn {
                width: 100%;
                margin-top: 0;
                margin-right: 0;
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                font-weight: 600;
            }

            #scanBtn:hover {
                background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
                box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
            }

            .naming-section {
                display: flex;
                flex-direction: column;
                gap: 12px;
            }

            .naming-row-inline {
                display: flex;
                gap: 12px;
                align-items: flex-end;
            }

            .naming-item {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .naming-item label {
                margin-bottom: 0;
                font-size: 13px;
            }

            .naming-item input {
                margin-bottom: 0;
            }

            .naming-row {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            #img-downloader-main .naming-row label {
                margin-bottom: 0;
            }

            .action-section {
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                gap: 8px;
            }

            .status-text {
                color: #666;
                font-size: 13px;
                margin-left: auto;
                transition: all 0.3s ease;
                font-weight: 500;
            }

            .status-text.success {
                color: #28a745;
                animation: statusPulse 0.5s ease-out;
            }

            .status-text.warning {
                color: #ffc107;
                animation: statusPulse 0.5s ease-out;
            }

            .status-text.error {
                color: #dc3545;
                animation: statusPulse 0.5s ease-out;
            }

            @keyframes statusPulse {
                0% { transform: scale(1); opacity: 0.7; }
                50% { transform: scale(1.05); opacity: 1; }
                100% { transform: scale(1); opacity: 1; }
            }

            .progress-section {
                margin-bottom: 20px;
            }

            .progress-bar {
                width: 100%;
                height: 8px;
                background: #f0f0f0;
                border-radius: 4px;
                overflow: hidden;
                margin-bottom: 8px;
            }

            .progress-fill {
                height: 100%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                width: 0%;
                transition: width 0.3s ease;
            }

            .progress-info {
                display: flex;
                flex-direction: column;
                gap: 6px;
            }

            .progress-text {
                font-size: 13px;
                color: #666;
                text-align: center;
                font-weight: 500;
            }

            .progress-detail {
                text-align: center;
                font-size: 12px;
                color: #888;
                font-style: italic;
                min-height: 16px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .images-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
                gap: 12px;
                max-height: 300px;
                overflow-y: auto;
            }

            .no-images {
                grid-column: 1 / -1;
                text-align: center;
                color: #999;
                padding: 40px 20px;
                font-size: 14px;
            }

            .image-item {
                position: relative;
                aspect-ratio: 1;
                border-radius: 8px;
                overflow: hidden;
                cursor: pointer;
                border: 2px solid transparent;
                transition: all 0.2s;
                user-select: none;
                box-sizing: border-box;
            }

            .image-item:hover {
                transform: scale(1.02);
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                border-color: #667eea;
            }

            .image-item.selected {
                border-color: #667eea;
                box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.5);
                background: rgba(102, 126, 234, 0.05);
            }

            .image-item.selected:hover {
                transform: scale(1.02);
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.6);
                border-color: #5a6fd8;
            }

            .image-item img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .image-item .checkbox {
                position: absolute;
                top: 6px;
                right: 6px;
                width: 22px;
                height: 22px;
                background: rgba(0,0,0,0.7);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s ease;
                z-index: 10;
                pointer-events: none;
            }

            .image-item:hover .checkbox {
                background: rgba(0,0,0,0.9);
                transform: scale(1.05);
            }

            .image-item.selected .checkbox {
                background: #667eea;
            }

            .image-item.selected:hover .checkbox {
                background: #5a6fd8;
            }

            .image-item .image-info {
                position: absolute;
                bottom: 4px;
                left: 4px;
                background: rgba(0,0,0,0.7);
                color: white;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 10px;
                font-weight: 500;
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.2s ease;
            }

            .image-item:hover .image-info {
                opacity: 1;
            }

            .shortcuts-info {
                margin-top: 8px;
                padding: 10px;
                background: #f8f9fa;
                border-radius: 6px;
                border-left: 3px solid #667eea;
                text-align: left;
                position: relative;
                z-index: 10;
            }

            .shortcuts-info small {
                color: #666;
                font-size: 11px;
                line-height: 1.4;
                display: block;
            }

            .download-mode-section .radio-group {
                display: flex;
                gap: 16px;
                margin-top: 8px;
            }

            .radio-label {
                display: flex;
                align-items: center;
                gap: 6px;
                cursor: pointer;
                font-size: 14px;
                font-weight: normal;
                margin-bottom: 0;
            }

            #img-downloader-main .radio-label input[type="radio"] {
                width: auto;
                margin: 0;
                padding: 0;
                border: none;
            }

            .radio-label span {
                color: #333;
            }

            /* 滚动条样式 */
            .panel-content::-webkit-scrollbar,
            .images-grid::-webkit-scrollbar {
                width: 6px;
            }

            .panel-content::-webkit-scrollbar-track,
            .images-grid::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
            }

            .panel-content::-webkit-scrollbar-thumb,
            .images-grid::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 3px;
            }

            .panel-content::-webkit-scrollbar-thumb:hover,
            .images-grid::-webkit-scrollbar-thumb:hover {
                background: #a8a8a8;
            }

            /* 过滤选项样式 */
            .filter-section {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 12px;
                margin-bottom: 12px;
            }

            .filter-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }

            .filter-header label {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 0;
                cursor: pointer;
                font-weight: 500;
            }

            .filter-header input[type="checkbox"] {
                margin: 0;
            }

            .reset-filter-btn {
                background: #6c757d !important;
                color: white !important;
                border: none !important;
                padding: 6px 12px !important;
                border-radius: 6px !important;
                font-size: 12px !important;
                cursor: pointer !important;
                margin: 0 !important;
            }

            .reset-filter-btn:hover {
                background: #5a6268 !important;
                transform: none !important;
                box-shadow: none !important;
            }

            .filter-content {
                transition: all 0.3s ease;
            }

            .filter-row {
                margin-bottom: 12px;
            }

            .filter-row:last-child {
                margin-bottom: 0;
            }

            .filter-row label {
                display: block;
                margin-bottom: 8px;
                font-weight: 500;
                color: #333;
                font-size: 14px;
            }

            .checkbox-group {
                display: flex;
                flex-wrap: wrap;
                gap: 12px;
            }

            .checkbox-label {
                display: flex;
                align-items: center;
                gap: 6px;
                cursor: pointer;
                font-size: 13px;
                margin-bottom: 0;
                font-weight: normal;
            }

            .checkbox-label input[type="checkbox"] {
                margin: 0;
            }

            .size-inputs {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .size-inputs input[type="number"] {
                width: 80px;
                padding: 8px 10px;
                border: 2px solid #e1e5e9;
                border-radius: 6px;
                font-size: 13px;
                text-align: center;
            }

            .size-inputs select {
                padding: 8px 10px;
                border: 2px solid #e1e5e9;
                border-radius: 6px;
                font-size: 13px;
                background: white;
                cursor: pointer;
            }

            .size-inputs span {
                font-size: 13px;
                color: #666;
                font-weight: 500;
            }

            /* 移动端适配样式 */
            @media (max-width: 768px) {
                #img-downloader-main {
                    position: fixed !important;
                    left: 10px !important;
                    right: 10px !important;
                    top: 10px !important;
                    bottom: auto !important;
                    width: auto !important;
                    max-width: none !important;
                    z-index: 999999 !important;
                }

                .img-downloader-toggle-btn {
                    padding: 12px 16px !important;
                    font-size: 14px !important;
                    min-height: 44px !important;
                    touch-action: manipulation !important;
                }

                .img-downloader-panel {
                    width: 100% !important;
                    max-width: none !important;
                    max-height: 80vh !important;
                    border-radius: 12px !important;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.3) !important;
                }

                .panel-content {
                    padding: 12px !important;
                    max-height: 70vh !important;
                    padding-bottom: 70px !important; /* 移动端为底部说明留出更多空间 */
                }

                .control-section input[type="text"] {
                    font-size: 16px !important;
                    padding: 12px !important;
                    min-height: 44px !important;
                }

                .control-section button {
                    padding: 12px 16px !important;
                    font-size: 14px !important;
                    min-height: 44px !important;
                    touch-action: manipulation !important;
                }

                .images-grid {
                    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr)) !important;
                    gap: 8px !important;
                    max-height: 40vh !important;
                }

                .image-item {
                    min-height: 60px !important;
                    border-radius: 8px !important;
                }

                .image-item img {
                    border-radius: 6px !important;
                }

                .checkbox {
                    width: 20px !important;
                    height: 20px !important;
                    font-size: 12px !important;
                }

                .image-info {
                    font-size: 10px !important;
                    padding: 2px 4px !important;
                }

                .filter-section {
                    padding: 10px !important;
                }

                .filter-row {
                    margin-bottom: 10px !important;
                }

                .checkbox-group {
                    gap: 8px !important;
                }

                .size-inputs input[type="number"] {
                    width: 70px !important;
                    font-size: 14px !important;
                    padding: 8px !important;
                }

                .size-inputs select {
                    font-size: 14px !important;
                    padding: 8px !important;
                }

                .progress-text {
                    font-size: 12px !important;
                }

                .progress-detail {
                    font-size: 11px !important;
                }

                .status-text {
                    font-size: 12px !important;
                }

                .shortcuts-info {
                    font-size: 11px !important;
                    padding: 8px !important;
                }

                .naming-row-inline {
                    flex-direction: column !important;
                    gap: 16px !important;
                }

                .naming-item {
                    flex: none !important;
                }
            }

            /* 小屏幕设备进一步优化 */
            @media (max-width: 480px) {
                #img-downloader-main {
                    left: 5px !important;
                    right: 5px !important;
                    top: 5px !important;
                }

                .img-downloader-toggle-btn {
                    padding: 10px 14px !important;
                    font-size: 13px !important;
                }

                .panel-content {
                    padding: 10px !important;
                    padding-bottom: 70px !important; /* 小屏幕设备也需要底部空间 */
                }

                .images-grid {
                    grid-template-columns: repeat(auto-fill, minmax(50px, 1fr)) !important;
                    gap: 6px !important;
                }

                .image-item {
                    min-height: 50px !important;
                }

                .control-section input[type="text"] {
                    font-size: 14px !important;
                    padding: 10px !important;
                }

                .control-section button {
                    padding: 10px 12px !important;
                    font-size: 13px !important;
                }

                .filter-section {
                    padding: 8px !important;
                }

                .size-inputs input[type="number"] {
                    width: 60px !important;
                    font-size: 13px !important;
                }
            }
        `;
    }

    // 简化的强制样式函数
    function getForceStyles() {
        return `
            /* 简单有效的强制样式 */
            #toggleBtn,
            #toggleBtn *,
            #toggleBtn span,
            #toggleBtn svg,
            #toggleBtn path {
                color: white !important;
                fill: white !important;
            }
        `;
    }

    // 拖拽处理函数 - 支持鼠标和触摸
    function handleMouseDown(e) {
        e.preventDefault();
        startDrag(e);
    }

    function handleTouchStart(e) {
        e.preventDefault();
        const touch = e.touches[0];
        startDrag(touch);
    }

    function startDrag(eventOrTouch) {
        // 重置拖拽状态
        dragState.reset();

        // 记录拖拽起始位置
        dragState.startX = eventOrTouch.clientX;
        dragState.startY = eventOrTouch.clientY;

        const mainContainer = document.getElementById('img-downloader-main');
        const rect = mainContainer.getBoundingClientRect();
        dragState.startLeft = rect.left;
        dragState.startTop = rect.top;

        // 添加移动和结束事件监听器
        if (MOBILE_CONFIG.isMobile) {
            document.addEventListener('touchmove', handleTouchMove, { passive: false });
            document.addEventListener('touchend', handleTouchEnd);
        } else {
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
        }
    }

    function handleMouseMove(e) {
        // 防止默认行为，避免文本选择等
        e.preventDefault();
        handleMove(e);
    }

    function handleTouchMove(e) {
        // 防止默认行为，避免页面滚动
        e.preventDefault();
        const touch = e.touches[0];
        handleMove(touch);
    }

    function handleMove(eventOrTouch) {
        const deltaX = eventOrTouch.clientX - dragState.startX;
        const deltaY = eventOrTouch.clientY - dragState.startY;

        // 移动端使用不同的拖拽阈值
        const threshold = MOBILE_CONFIG.isMobile ? MOBILE_CONFIG.touchThreshold : DRAG_THRESHOLD;

        // 检查是否超过拖拽阈值
        if (!dragState.isDragging && (Math.abs(deltaX) > threshold || Math.abs(deltaY) > threshold)) {
            dragState.isDragging = true;
            const mainContainer = document.getElementById('img-downloader-main');
            mainContainer.classList.add('dragging');

            // 立即设置拖拽标志，但不立即安排重置
            dragState.hasBeenDragged = true;
        }

        if (dragState.isDragging) {
            const newLeft = dragState.startLeft + deltaX;
            const newTop = dragState.startTop + deltaY;
            updateButtonPosition(newLeft, newTop);
        }
    }

    function handleMouseUp() {
        endDrag();
    }

    function handleTouchEnd(e) {
        e.preventDefault();
        endDrag();
    }

    function endDrag() {
        // 清理拖拽样式
        if (dragState.isDragging) {
            const mainContainer = document.getElementById('img-downloader-main');
            mainContainer.classList.remove('dragging');
        }

        // 移除事件监听器
        if (MOBILE_CONFIG.isMobile) {
            document.removeEventListener('touchmove', handleTouchMove);
            document.removeEventListener('touchend', handleTouchEnd);
        } else {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        }

        // 如果发生了拖拽，安排状态重置
        if (dragState.isDragging || dragState.hasBeenDragged) {
            dragState.scheduleReset(); // 安排延迟重置
        }

        // 重置拖拽进行状态
        dragState.isDragging = false;
    }

    function updateButtonPosition(left, top) {
        const mainContainer = document.getElementById('img-downloader-main');
        const constrainedPos = constrainToViewport(left, top, mainContainer);

        mainContainer.style.left = constrainedPos.left + 'px';
        mainContainer.style.top = constrainedPos.top + 'px';
        mainContainer.style.right = 'auto';
        mainContainer.style.bottom = 'auto';
    }

    function constrainToViewport(left, top, element) {
        const rect = element.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // 确保按钮不会超出视窗边界
        const constrainedLeft = Math.max(0, Math.min(left, viewportWidth - rect.width));
        const constrainedTop = Math.max(0, Math.min(top, viewportHeight - rect.height));

        return { left: constrainedLeft, top: constrainedTop };
    }

    // 提示系统函数
    function showTip(message, type = 'info', duration = null) {
        const statusText = getCachedElement('statusText') || document.getElementById('statusText');
        if (!statusText) return;

        // 设置默认持续时间
        if (duration === null) {
            duration = TIP_DURATION[type.toUpperCase()] || TIP_DURATION.DEFAULT;
        }

        // 清除之前的样式类
        statusText.classList.remove('success', 'warning', 'error');

        // 添加对应的样式类和图标
        if (TIP_ICONS[type]) {
            statusText.classList.add(type);
            message = TIP_ICONS[type] + ' ' + message;
        }

        // 显示消息
        statusText.textContent = message;

        // 自动恢复到默认状态
        if (duration > 0) {
            setTimeout(() => {
                statusText.classList.remove('success', 'warning', 'error');
                updateSelectionStatus(); // 复用现有函数
            }, duration);
        }
    }

    // 添加事件监听器的辅助函数
    function addEventListenerWithRef(element, eventType, handler) {
        if (element && typeof element.addEventListener === 'function') {
            element.addEventListener(eventType, handler);

            // 为每个元素维护一个监听器数组
            if (!eventListenerRefs.has(element)) {
                eventListenerRefs.set(element, []);
            }
            eventListenerRefs.get(element).push([eventType, handler]);
        }
    }

    // URL资源管理工具函数
    function createManagedURL(blob) {
        const url = URL.createObjectURL(blob);
        let isRevoked = false;

        const cleanup = () => {
            if (!isRevoked) {
                URL.revokeObjectURL(url);
                isRevoked = true;
                // 从活跃管理器集合中移除
                activeURLManagers.delete(manager);
            }
        };

        const manager = { url, cleanup };

        // 添加到活跃管理器集合
        activeURLManagers.add(manager);

        return manager;
    }

    // 网络请求工具函数

    // 延迟函数，用于重试间隔
    function delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 网络错误分类函数
    function classifyNetworkError(error) {
        if (error.name === 'AbortError') {
            return { type: 'timeout', message: '请求超时', retryable: true };
        }
        if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
            return { type: 'network', message: '网络连接失败', retryable: true };
        }
        if (error.message.includes('CORS')) {
            return { type: 'cors', message: 'CORS限制', retryable: false };
        }
        if (error.message.includes('HTTP 4')) {
            return { type: 'client_error', message: '客户端错误', retryable: false };
        }
        if (error.message.includes('HTTP 5')) {
            return { type: 'server_error', message: '服务器错误', retryable: true };
        }
        return { type: 'unknown', message: error.message || '未知错误', retryable: true };
    }

    // 带超时和重试的fetch函数
    async function fetchWithRetry(url, options = {}) {
        let lastError = null;

        for (let attempt = 1; attempt <= NETWORK_CONFIG.maxRetries; attempt++) {
            try {
                // 创建AbortController用于超时控制
                const controller = new AbortController();
                const timeoutId = setTimeout(() => {
                    controller.abort();
                }, NETWORK_CONFIG.timeout);

                // 合并选项，添加signal
                const fetchOptions = {
                    mode: 'cors',
                    credentials: 'omit',
                    ...options,
                    signal: controller.signal
                };

                console.log(`[网络请求] 尝试 ${attempt}/${NETWORK_CONFIG.maxRetries}: ${url}`);

                const response = await fetch(url, fetchOptions);

                // 清理超时定时器
                clearTimeout(timeoutId);

                // 检查响应状态
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                console.log(`[网络请求] 成功: ${url}`);
                return response;

            } catch (error) {
                lastError = error;
                const errorInfo = classifyNetworkError(error);

                console.warn(`[网络请求] 尝试 ${attempt} 失败: ${errorInfo.message}`, error);

                // 如果是不可重试的错误，直接抛出
                if (!errorInfo.retryable) {
                    throw new Error(`${errorInfo.message}: ${url}`);
                }

                // 如果是最后一次尝试，抛出错误
                if (attempt === NETWORK_CONFIG.maxRetries) {
                    throw new Error(`${errorInfo.message} (重试${NETWORK_CONFIG.maxRetries}次后失败): ${url}`);
                }

                // 计算重试延迟（指数退避）
                const retryDelay = NETWORK_CONFIG.retryDelay * Math.pow(NETWORK_CONFIG.retryMultiplier, attempt - 1);
                console.log(`[网络请求] ${retryDelay}ms 后重试...`);

                await delay(retryDelay);
            }
        }

        // 理论上不会到达这里，但为了类型安全
        throw lastError || new Error('未知的网络请求错误');
    }

    // 下载队列管理器
    class DownloadQueue {
        constructor(maxConcurrent = DOWNLOAD_CONFIG.maxConcurrent) {
            this.maxConcurrent = maxConcurrent;
            this.running = new Set();
            this.pending = [];
            this.completed = 0;
            this.failed = 0;
            this.total = 0;
            this.onProgress = null; // 进度回调函数
            this.onComplete = null; // 完成回调函数
        }

        // 添加下载任务到队列
        async add(downloadTask) {
            return new Promise((resolve, reject) => {
                this.pending.push({
                    task: downloadTask,
                    resolve,
                    reject,
                    id: Date.now() + Math.random() // 简单的任务ID
                });
                this.total++;
                this.process();
            });
        }

        // 处理队列
        async process() {
            while (this.running.size < this.maxConcurrent && this.pending.length > 0) {
                const { task, resolve, reject, id } = this.pending.shift();

                const promise = this.executeTask(task, id)
                    .then(result => {
                        this.completed++;
                        this.updateProgress();
                        resolve(result);
                        return result;
                    })
                    .catch(error => {
                        this.failed++;
                        this.updateProgress();
                        reject(error);
                        throw error;
                    })
                    .finally(() => {
                        this.running.delete(promise);
                        // 继续处理队列
                        setTimeout(() => this.process(), DOWNLOAD_CONFIG.queueCheckInterval);

                        // 检查是否全部完成
                        if (this.running.size === 0 && this.pending.length === 0) {
                            this.onQueueComplete();
                        }
                    });

                this.running.add(promise);
            }
        }

        // 执行单个下载任务
        async executeTask(task, taskId) {
            console.log(`[下载队列] 开始执行任务 ${taskId}, 当前并发: ${this.running.size}/${this.maxConcurrent}`);

            try {
                const result = await task();
                console.log(`[下载队列] 任务 ${taskId} 完成`);
                return result;
            } catch (error) {
                console.warn(`[下载队列] 任务 ${taskId} 失败:`, error.message);
                throw error;
            }
        }

        // 更新进度
        updateProgress() {
            if (this.onProgress && typeof this.onProgress === 'function') {
                this.onProgress({
                    completed: this.completed,
                    failed: this.failed,
                    total: this.total,
                    running: this.running.size,
                    pending: this.pending.length
                });
            }
        }

        // 队列完成处理
        onQueueComplete() {
            console.log(`[下载队列] 队列处理完成: 成功${this.completed}, 失败${this.failed}, 总计${this.total}`);

            if (this.onComplete && typeof this.onComplete === 'function') {
                this.onComplete({
                    completed: this.completed,
                    failed: this.failed,
                    total: this.total
                });
            }
        }

        // 重置队列状态
        reset() {
            this.running.clear();
            this.pending = [];
            this.completed = 0;
            this.failed = 0;
            this.total = 0;
        }

        // 获取队列状态
        getStatus() {
            return {
                running: this.running.size,
                pending: this.pending.length,
                completed: this.completed,
                failed: this.failed,
                total: this.total,
                maxConcurrent: this.maxConcurrent
            };
        }
    }

    // 全局下载队列实例
    let globalDownloadQueue = null;

    // 获取或创建下载队列
    function getDownloadQueue() {
        if (!globalDownloadQueue) {
            globalDownloadQueue = new DownloadQueue();
        }
        return globalDownloadQueue;
    }

    // 进度管理器类
    class ProgressManager {
        constructor() {
            this.startTime = null;
            this.completedCount = 0;
            this.totalCount = 0;
            this.failedCount = 0;
            this.currentFile = '';
            this.progressElement = null;
            this.statusElement = null;
            this.detailElement = null;
        }

        // 初始化进度管理器
        init(progressElement, statusElement, detailElement = null) {
            this.progressElement = progressElement;
            this.statusElement = statusElement;
            this.detailElement = detailElement;
        }

        // 开始进度跟踪
        start(total, operation = '处理') {
            this.startTime = Date.now();
            this.totalCount = total;
            this.completedCount = 0;
            this.failedCount = 0;
            this.currentFile = '';
            this.operation = operation;

            this.updateDisplay();
        }

        // 更新进度
        update(completed, failed = 0, currentFile = '') {
            this.completedCount = completed;
            this.failedCount = failed;
            this.currentFile = currentFile;

            this.updateDisplay();
        }

        // 更新显示
        updateDisplay() {
            if (!this.progressElement || !this.statusElement) return;

            const progress = this.totalCount > 0 ? (this.completedCount + this.failedCount) / this.totalCount * 100 : 0;
            const elapsed = this.startTime ? Date.now() - this.startTime : 0;

            // 更新进度条
            this.progressElement.style.width = `${progress}%`;

            // 计算剩余时间
            let remainingTimeText = '';
            if (this.completedCount > 0 && elapsed > 1000) {
                const avgTimePerItem = elapsed / (this.completedCount + this.failedCount);
                const remainingItems = this.totalCount - this.completedCount - this.failedCount;
                const remainingTime = avgTimePerItem * remainingItems;
                remainingTimeText = ` (剩余: ${this.formatTime(remainingTime)})`;
            }

            // 更新状态文本
            let statusText = `${this.completedCount + this.failedCount}/${this.totalCount}`;
            if (this.failedCount > 0) {
                statusText += ` (成功: ${this.completedCount}, 失败: ${this.failedCount})`;
            } else {
                statusText += ` (成功: ${this.completedCount})`;
            }
            statusText += remainingTimeText;

            this.statusElement.textContent = statusText;

            // 更新详细信息
            if (this.detailElement && this.currentFile) {
                this.detailElement.textContent = `当前: ${this.currentFile}`;
            }
        }

        // 格式化时间
        formatTime(ms) {
            if (ms < 1000) return '< 1秒';

            const seconds = Math.floor(ms / 1000);
            if (seconds < 60) return `${seconds}秒`;

            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            if (minutes < 60) {
                return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分`;
            }

            const hours = Math.floor(minutes / 60);
            const remainingMinutes = minutes % 60;
            return remainingMinutes > 0 ? `${hours}时${remainingMinutes}分` : `${hours}时`;
        }

        // 完成进度跟踪
        complete(message = '') {
            if (this.statusElement && message) {
                this.statusElement.textContent = message;
            }
            if (this.detailElement) {
                this.detailElement.textContent = '';
            }
        }

        // 重置进度管理器
        reset() {
            this.startTime = null;
            this.completedCount = 0;
            this.totalCount = 0;
            this.failedCount = 0;
            this.currentFile = '';

            if (this.progressElement) {
                this.progressElement.style.width = '0%';
            }
            if (this.statusElement) {
                this.statusElement.textContent = '0/0';
            }
            if (this.detailElement) {
                this.detailElement.textContent = '';
            }
        }
    }

    // 全局进度管理器实例
    let globalProgressManager = null;

    // 获取或创建进度管理器
    function getProgressManager() {
        if (!globalProgressManager) {
            globalProgressManager = new ProgressManager();
        }
        return globalProgressManager;
    }

    // 图片过滤相关函数

    // 切换过滤内容显示
    function toggleFilterContent() {
        const enableFilter = document.getElementById('enableFilter');
        const filterContent = document.getElementById('filterContent');

        if (enableFilter.checked) {
            filterContent.style.display = 'block';
        } else {
            filterContent.style.display = 'none';
        }
    }

    // 重置过滤设置
    function resetFilterSettings() {
        // 重置启用状态
        document.getElementById('enableFilter').checked = FILTER_CONFIG.enabledByDefault;

        // 重置格式选择
        const formatCheckboxes = document.querySelectorAll('input[name="imageFormat"]');
        formatCheckboxes.forEach(checkbox => {
            checkbox.checked = FILTER_CONFIG.defaultFormats.includes(checkbox.value);
        });

        // 重置尺寸设置
        document.getElementById('minWidth').value = FILTER_CONFIG.defaultMinWidth;
        document.getElementById('minHeight').value = FILTER_CONFIG.defaultMinHeight;

        // 重置文件大小设置
        document.getElementById('maxFileSize').value = FILTER_CONFIG.defaultMaxFileSize / 1048576; // 转换为MB
        document.getElementById('fileSizeUnit').value = '1048576'; // MB

        // 更新过滤内容显示
        toggleFilterContent();

        showTip('过滤设置已重置', 'success');
    }

    // 获取当前过滤设置
    function getFilterSettings() {
        const enableFilter = document.getElementById('enableFilter').checked;

        if (!enableFilter) {
            return null; // 未启用过滤
        }

        // 获取选中的格式
        const formatCheckboxes = document.querySelectorAll('input[name="imageFormat"]:checked');
        const allowedFormats = Array.from(formatCheckboxes).map(cb => cb.value);

        // 获取尺寸设置
        const minWidth = parseInt(document.getElementById('minWidth').value) || 0;
        const minHeight = parseInt(document.getElementById('minHeight').value) || 0;

        // 获取文件大小设置
        const maxFileSize = parseFloat(document.getElementById('maxFileSize').value) || 0;
        const fileSizeUnit = parseInt(document.getElementById('fileSizeUnit').value) || 1048576;
        const maxFileSizeBytes = maxFileSize * fileSizeUnit;

        return {
            allowedFormats,
            minWidth,
            minHeight,
            maxFileSizeBytes
        };
    }

    // 检查图片是否通过过滤条件
    function passesFilter(img, filterSettings) {
        if (!filterSettings) {
            return true; // 未启用过滤，全部通过
        }

        // 检查格式
        const imageUrl = img.src.toLowerCase();
        const hasValidFormat = filterSettings.allowedFormats.some(format => {
            // 支持多种格式检查方式
            return imageUrl.includes(`.${format}`) ||
                   imageUrl.includes(`format=${format}`) ||
                   imageUrl.includes(`type=${format}`) ||
                   (format === 'jpg' && imageUrl.includes('.jpeg'));
        });

        if (!hasValidFormat) {
            return false;
        }

        // 检查尺寸（如果图片已加载）
        if (img.naturalWidth > 0 && img.naturalHeight > 0) {
            if (img.naturalWidth < filterSettings.minWidth || img.naturalHeight < filterSettings.minHeight) {
                return false;
            }
        }

        // 文件大小检查需要在下载时进行，这里暂时跳过
        // 因为无法在不下载的情况下获取准确的文件大小

        return true;
    }

    // 应用过滤到图片列表
    function applyImageFilter(images) {
        const filterSettings = getFilterSettings();

        if (!filterSettings) {
            return images; // 未启用过滤，返回原列表
        }

        const filteredImages = images.filter(img => passesFilter(img, filterSettings));

        // 显示过滤结果
        const originalCount = images.length;
        const filteredCount = filteredImages.length;
        const removedCount = originalCount - filteredCount;

        if (removedCount > 0) {
            console.log(`[图片过滤] 原始: ${originalCount}张, 过滤后: ${filteredCount}张, 移除: ${removedCount}张`);
            showTip(`过滤完成：保留 ${filteredCount} 张图片，移除 ${removedCount} 张`, 'success');
        }

        return filteredImages;
    }

    // 绑定事件
    function bindEvents() {
        // 防止重复绑定
        if (eventListenersAdded) {
            return;
        }

        const toggleBtn = document.getElementById('toggleBtn');
        const closeBtn = document.getElementById('closeBtn');
        const scanBtn = document.getElementById('scanBtn');
        const selectAllBtn = document.getElementById('selectAllBtn');
        const deselectAllBtn = document.getElementById('deselectAllBtn');
        const downloadSelectedBtn = document.getElementById('downloadSelectedBtn');
        const enableFilterCheckbox = document.getElementById('enableFilter');
        const resetFilterBtn = document.getElementById('resetFilterBtn');

        // 绑定按钮事件并记录引用
        addEventListenerWithRef(toggleBtn, 'click', togglePanel);
        addEventListenerWithRef(toggleBtn, 'mousedown', handleMouseDown);
        addEventListenerWithRef(closeBtn, 'click', hidePanel);
        addEventListenerWithRef(scanBtn, 'click', scanImages);
        addEventListenerWithRef(selectAllBtn, 'click', selectAllImages);
        addEventListenerWithRef(deselectAllBtn, 'click', deselectAllImages);
        addEventListenerWithRef(downloadSelectedBtn, 'click', downloadSelectedImages);

        // 绑定移动端触摸事件
        if (MOBILE_CONFIG.isMobile) {
            addEventListenerWithRef(toggleBtn, 'touchstart', handleTouchStart);
        }

        // 绑定过滤相关事件
        addEventListenerWithRef(enableFilterCheckbox, 'change', toggleFilterContent);
        addEventListenerWithRef(resetFilterBtn, 'click', resetFilterSettings);

        // 点击面板外部关闭
        document.addEventListener('click', handleOutsideClick);

        // 键盘快捷键
        document.addEventListener('keydown', handleKeyboardShortcuts);

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', cleanup);

        // 标记事件监听器已添加
        eventListenersAdded = true;
    }

    // 命名函数，便于清理
    function handleOutsideClick(e) {
        if (!document.getElementById('img-downloader-main').contains(e.target)) {
            hidePanel();
        }
    }

    // 切换面板显示
    function togglePanel() {
        // 如果刚刚拖拽过，不触发面板切换
        if (dragState.hasBeenDragged) {
            return;
        }

        // 如果正在拖拽中，也不触发面板切换
        if (dragState.isDragging) {
            return;
        }

        if (isUIVisible) {
            hidePanel();
        } else {
            showPanel();
        }
    }

    function showPanel() {
        const panel = document.getElementById('mainPanel');
        panel.style.display = 'block';
        isUIVisible = true;
    }

    function hidePanel() {
        const panel = document.getElementById('mainPanel');
        panel.style.display = 'none';
        isUIVisible = false;
    }

    // 扫描图片 - 优化版本，支持大量图片的高效处理
    async function scanImages() {
        const containerSelector = document.getElementById('containerSelector').value.trim() || 'body';
        const statusText = getCachedElement('statusText') || document.getElementById('statusText');
        const imagesGrid = getCachedElement('imagesGrid') || document.getElementById('imagesGrid');

        // 性能监控
        const startTime = performance.now();

        // 清理之前的选择，释放内存
        selectedImages.clear();

        try {
            statusText.textContent = '扫描中...';
            const container = document.querySelector(containerSelector);

            if (!container) {
                statusText.textContent = '容器未找到';
                imagesGrid.innerHTML = '<div class="no-images">未找到指定容器</div>';
                return;
            }

            const allImages = container.querySelectorAll('img');

            // 检查图片数量，如果超过限制则警告用户
            if (allImages.length > SCAN_CONFIG.maxImages) {
                showTip(`发现${allImages.length}张图片，将只处理前${SCAN_CONFIG.maxImages}张以确保性能`, 'warning');
            }

            // 限制处理的图片数量
            const imagesToProcess = Array.from(allImages).slice(0, SCAN_CONFIG.maxImages);
            statusText.textContent = `扫描中... 发现 ${allImages.length} 个图片元素，处理 ${imagesToProcess.length} 个`;

            // 分批处理图片验证，避免阻塞UI
            const validImages = await processImagesInBatches(imagesToProcess, statusText);

            if (validImages.length === 0) {
                statusText.textContent = '未找到有效图片';
                imagesGrid.innerHTML = '<div class="no-images">该容器内未找到有效图片</div>';
                return;
            }

            selectedImages.clear();

            // 显示去重进度
            statusText.textContent = `处理中... 正在去重 ${validImages.length} 张图片`;

            // 优化的去重处理
            const uniqueImages = await deduplicateImagesOptimized(validImages, statusText);

            // 应用图片过滤
            const filteredImages = applyImageFilter(uniqueImages);

            renderImageGrid(filteredImages);

            // 显示简洁的扫描结果
            const duplicateCount = validImages.length - uniqueImages.length;
            const filterCount = uniqueImages.length - filteredImages.length;

            let resultMessage = `找到 ${filteredImages.length} 张图片`;
            const details = [];

            if (duplicateCount > 0) {
                details.push(`去重 ${duplicateCount} 张`);
            }
            if (filterCount > 0) {
                details.push(`过滤 ${filterCount} 张`);
            }

            if (details.length > 0) {
                resultMessage += ` (${details.join(', ')})`;
            }

            statusText.textContent = resultMessage;

            // 性能统计
            const endTime = performance.now();
            const duration = Math.round(endTime - startTime);

            // 在控制台输出详细统计（开发调试用）
            console.log(`图片扫描完成: 扫描${allImages.length}个元素 → 处理${imagesToProcess.length}个 → 验证后${validImages.length}张 → 去重后${uniqueImages.length}张 → 过滤后${filteredImages.length}张`);
            console.log(`扫描性能: 耗时${duration}ms, 平均${Math.round(duration/imagesToProcess.length*100)/100}ms/图片`);

            // 大量图片时建议垃圾回收
            if (uniqueImages.length > 200) {
                // 建议浏览器进行垃圾回收（如果支持）
                if (window.gc && typeof window.gc === 'function') {
                    setTimeout(() => window.gc(), 1000);
                }
                console.log('已处理大量图片，建议浏览器进行内存优化');
            }

        } catch (error) {
            statusText.textContent = '扫描失败';
            imagesGrid.innerHTML = '<div class="no-images">扫描出错，请检查选择器</div>';
            console.error('扫描图片失败:', error);
        }
    }

    // 分批处理图片验证，避免阻塞UI
    async function processImagesInBatches(images, statusText) {
        const validImages = [];
        const totalImages = images.length;

        for (let i = 0; i < totalImages; i += SCAN_CONFIG.batchSize) {
            const batch = images.slice(i, i + SCAN_CONFIG.batchSize);

            // 处理当前批次
            const batchValid = batch.filter(isValidImage);
            validImages.push(...batchValid);

            // 更新进度
            const processed = Math.min(i + SCAN_CONFIG.batchSize, totalImages);
            if (processed % SCAN_CONFIG.progressUpdateInterval === 0 || processed === totalImages) {
                statusText.textContent = `验证中... ${processed}/${totalImages} (有效: ${validImages.length})`;
            }

            // 让出控制权，避免阻塞UI
            if (i + SCAN_CONFIG.batchSize < totalImages) {
                await new Promise(resolve => setTimeout(resolve, SCAN_CONFIG.yieldInterval));
            }
        }

        return validImages;
    }

    // 优化的图片验证函数
    function isValidImage(img) {
        // 检查是否有有效的src
        if (!img.src) return false;

        // 支持多种URL格式：http/https、相对路径、data URL
        const hasValidUrl = img.src.startsWith('http') ||
                           img.src.startsWith('//') ||
                           img.src.startsWith('/') ||
                           img.src.startsWith('data:image/') ||
                           img.src.startsWith('./') ||
                           img.src.startsWith('../');

        if (!hasValidUrl) return false;

        // 移除img.complete限制，解决懒加载问题
        // 放宽尺寸检查：允许naturalWidth为0的情况（如SVG或未加载完成的图片）
        return true;
    }

    // 优化的去重处理，支持大量图片
    async function deduplicateImagesOptimized(images, statusText) {
        const seen = new Set();
        const uniqueImages = [];
        let duplicateCount = 0;
        const totalImages = images.length;

        for (let i = 0; i < totalImages; i += SCAN_CONFIG.batchSize) {
            const batch = images.slice(i, i + SCAN_CONFIG.batchSize);

            // 处理当前批次的去重
            for (const img of batch) {
                let normalizedUrl = img.src;

                try {
                    // 先用简单字符串处理，提高性能
                    if (img.src.includes('?')) {
                        // 只对有查询参数的URL进行复杂处理
                        const url = new URL(img.src, window.location.href);

                        // 移除常见的缓存和追踪参数
                        const paramsToRemove = ['t', 'timestamp', '_', 'cache', 'v', 'version', 'cb', 'cachebuster'];
                        let hasRemovedParams = false;

                        paramsToRemove.forEach(param => {
                            if (url.searchParams.has(param)) {
                                url.searchParams.delete(param);
                                hasRemovedParams = true;
                            }
                        });

                        // 只在实际移除了参数时才更新URL
                        if (hasRemovedParams) {
                            normalizedUrl = url.toString();
                        }
                    }
                } catch (error) {
                    // URL解析失败时使用原始URL
                    console.warn(`URL解析失败，使用原始URL: ${img.src}`, error);
                    normalizedUrl = img.src;
                }

                if (seen.has(normalizedUrl)) {
                    duplicateCount++;
                } else {
                    seen.add(normalizedUrl);
                    uniqueImages.push(img);
                }
            }

            // 更新进度
            const processed = Math.min(i + SCAN_CONFIG.batchSize, totalImages);
            if (processed % SCAN_CONFIG.progressUpdateInterval === 0 || processed === totalImages) {
                statusText.textContent = `去重中... ${processed}/${totalImages} (去重: ${duplicateCount})`;
            }

            // 让出控制权，避免阻塞UI
            if (i + SCAN_CONFIG.batchSize < totalImages) {
                await new Promise(resolve => setTimeout(resolve, SCAN_CONFIG.yieldInterval));
            }
        }

        // 汇总显示去重信息
        if (duplicateCount > 0) {
            console.log(`去重完成: 移除了 ${duplicateCount} 张重复图片`);
        }

        return uniqueImages;
    }

    // 去重图片URL - 优化性能和错误处理
    function deduplicateImages(images) {
        const seen = new Set();
        let duplicateCount = 0;

        const uniqueImages = images.filter(img => {
            let normalizedUrl = img.src;

            try {
                // 先用简单字符串处理，提高性能
                if (img.src.includes('?')) {
                    // 只对有查询参数的URL进行复杂处理
                    const url = new URL(img.src, window.location.href);

                    // 移除常见的缓存和追踪参数
                    const paramsToRemove = ['t', 'timestamp', '_', 'cache', 'v', 'version', 'cb', 'cachebuster'];
                    let hasRemovedParams = false;

                    paramsToRemove.forEach(param => {
                        if (url.searchParams.has(param)) {
                            url.searchParams.delete(param);
                            hasRemovedParams = true;
                        }
                    });

                    // 只在实际移除了参数时才更新URL
                    if (hasRemovedParams) {
                        normalizedUrl = url.toString();
                    }
                }
            } catch (error) {
                // URL解析失败时使用原始URL
                console.warn(`URL解析失败，使用原始URL: ${img.src}`, error);
                normalizedUrl = img.src;
            }

            if (seen.has(normalizedUrl)) {
                duplicateCount++;
                return false;
            }
            seen.add(normalizedUrl);
            return true;
        });

        // 汇总显示去重信息
        if (duplicateCount > 0) {
            console.log(`去重完成: 移除了 ${duplicateCount} 张重复图片`);
        }

        return uniqueImages;
    }

    // 渲染图片网格 - 优化版本，支持大量图片的高效渲染
    async function renderImageGrid(images) {
        const imagesGrid = document.getElementById('imagesGrid');
        imagesGrid.innerHTML = '';

        // 传入的images已经是去重后的，直接使用
        const uniqueImages = images;

        // 安全地管理图片网格的事件监听器
        if (eventListenerRefs.has(imagesGrid)) {
            const listeners = eventListenerRefs.get(imagesGrid);
            listeners.forEach(([eventType, handler]) => {
                imagesGrid.removeEventListener(eventType, handler);
            });
            eventListenerRefs.delete(imagesGrid);
        }

        // 添加新的事件监听器并记录引用
        addEventListenerWithRef(imagesGrid, 'click', handleImageGridClick);

        // 如果图片数量较少，直接渲染
        if (uniqueImages.length <= SCAN_CONFIG.batchSize) {
            renderImageBatch(uniqueImages, 0, imagesGrid);
            return;
        }

        // 大量图片时分批渲染，避免阻塞UI
        const statusText = getCachedElement('statusText') || document.getElementById('statusText');
        const totalImages = uniqueImages.length;

        for (let i = 0; i < totalImages; i += SCAN_CONFIG.batchSize) {
            const batch = uniqueImages.slice(i, i + SCAN_CONFIG.batchSize);
            renderImageBatch(batch, i, imagesGrid);

            // 更新渲染进度
            const rendered = Math.min(i + SCAN_CONFIG.batchSize, totalImages);
            if (rendered < totalImages) {
                statusText.textContent = `渲染中... ${rendered}/${totalImages}`;
                // 让出控制权，避免阻塞UI
                await new Promise(resolve => setTimeout(resolve, SCAN_CONFIG.yieldInterval));
            }
        }
    }

    // 渲染图片批次
    function renderImageBatch(images, startIndex, container) {
        const fragment = document.createDocumentFragment();

        images.forEach((img, batchIndex) => {
            const index = startIndex + batchIndex;
            const imageItem = document.createElement('div');
            imageItem.className = 'image-item';
            imageItem.dataset.index = index;
            imageItem.dataset.src = img.src;

            // 优化图片尺寸显示逻辑
            const sizeInfo = getSizeInfo(img);

            imageItem.innerHTML = `
                <img src="${img.src}" alt="图片 ${index + 1}" loading="lazy">
                <div class="checkbox">✓</div>
                <div class="image-info">${sizeInfo}</div>
            `;

            fragment.appendChild(imageItem);
        });

        // 一次性添加所有元素，减少DOM操作
        container.appendChild(fragment);
    }

    // 提取图片尺寸信息获取逻辑
    function getSizeInfo(img) {
        // 优先使用naturalWidth/Height（原始尺寸）
        if (img.naturalWidth > 0 && img.naturalHeight > 0) {
            return `${img.naturalWidth}×${img.naturalHeight}`;
        }

        // 其次使用显示尺寸
        if (img.width > 0 && img.height > 0) {
            return `${img.width}×${img.height}`;
        }

        // 检查是否是SVG（可能没有数值尺寸）
        if (img.src.toLowerCase().includes('.svg') || img.src.includes('data:image/svg')) {
            return 'SVG';
        }

        // 默认显示加载中
        return '加载中';
    }

    // 处理图片网格点击事件（事件委托）
    function handleImageGridClick(e) {
        e.stopPropagation();
        const imageItem = e.target.closest('.image-item');
        if (imageItem) {
            const src = imageItem.dataset.src;
            toggleImageSelection(imageItem, src);
        }
    }

    // 处理键盘快捷键
    function handleKeyboardShortcuts(e) {
        // 只在面板显示时响应快捷键
        if (!isUIVisible) return;

        // 避免在输入框中触发
        if (e.target.tagName === 'INPUT') return;

        // 移动端减少键盘快捷键，因为虚拟键盘可能不支持所有组合键
        if (MOBILE_CONFIG.isMobile) {
            switch(e.key) {
                case 'Escape':
                    hidePanel();
                    break;
                case 'Delete':
                case 'Backspace':
                    deselectAllImages();
                    break;
            }
            return;
        }

        // 桌面端完整快捷键支持
        switch(e.key) {
            case 'Escape':
                hidePanel();
                break;
            case 'a':
            case 'A':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    selectAllImages();
                }
                break;
            case 's':
            case 'S':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    scanImages();
                }
                break;
            case 'd':
            case 'D':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    downloadSelectedImages();
                }
                break;
            case 'Delete':
            case 'Backspace':
                deselectAllImages();
                break;
        }
    }

    // 切换图片选择状态
    function toggleImageSelection(imageItem, src) {
        if (selectedImages.has(src)) {
            selectedImages.delete(src);
            imageItem.classList.remove('selected');
        } else {
            selectedImages.add(src);
            imageItem.classList.add('selected');
        }
        updateSelectionStatus();
    }

    // 全选图片
    function selectAllImages() {
        const imageItems = document.querySelectorAll('.image-item');
        imageItems.forEach(item => {
            const src = item.dataset.src;
            selectedImages.add(src);
            item.classList.add('selected');
        });
        updateSelectionStatus();
    }

    // 取消全选
    function deselectAllImages() {
        selectedImages.clear();
        const imageItems = document.querySelectorAll('.image-item');
        imageItems.forEach(item => {
            item.classList.remove('selected');
        });
        updateSelectionStatus();
    }

    // 更新选择状态
    function updateSelectionStatus() {
        const statusText = getCachedElement('statusText') || document.getElementById('statusText');
        const totalImages = document.querySelectorAll('.image-item').length;
        const selectedCount = selectedImages.size;

        if (statusText) {
            if (selectedCount === 0) {
                statusText.textContent = totalImages > 0 ? `共 ${totalImages} 张图片` : '就绪';
            } else {
                statusText.textContent = `已选择 ${selectedCount}/${totalImages} 张图片`;
            }
        }
    }

    // 下载选中的图片
    async function downloadSelectedImages() {
        if (selectedImages.size === 0) {
            showTip('请先选择要下载的图片', 'warning');
            return;
        }

        const downloadMode = document.querySelector('input[name="downloadMode"]:checked').value;

        if (downloadMode === 'zip') {
            await downloadAsZip();
        } else {
            await downloadIndividually();
        }
    }

    // 单独下载图片 - 使用并发控制队列和增强进度显示
    async function downloadIndividually() {
        const namePrefix = document.getElementById('namePrefix').value.trim() || 'image';
        const startNumber = parseInt(document.getElementById('startNumber').value) || 1;
        const progressSection = document.querySelector('.progress-section');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const progressDetail = document.getElementById('progressDetail');
        const downloadBtn = document.getElementById('downloadSelectedBtn');

        // 显示进度条
        progressSection.style.display = 'block';
        downloadBtn.disabled = true;
        downloadBtn.textContent = '下载中...';

        const imageUrls = Array.from(selectedImages);
        let currentNumber = startNumber;
        const failedUrls = [];

        // 创建并初始化进度管理器
        const progressManager = getProgressManager();
        progressManager.init(progressFill, progressText, progressDetail);
        progressManager.start(imageUrls.length, '下载');

        // 创建新的下载队列
        const downloadQueue = getDownloadQueue();
        downloadQueue.reset(); // 重置队列状态

        // 设置进度回调
        downloadQueue.onProgress = (status) => {
            // 计算当前正在下载的文件名
            const currentIndex = status.completed + status.failed;
            const currentFileName = currentIndex < imageUrls.length ?
                `${sanitizeFilename(namePrefix)}_${startNumber + currentIndex}` : '';

            progressManager.update(status.completed, status.failed, currentFileName);
        };

        // 设置完成回调
        downloadQueue.onComplete = (result) => {
            // 收集失败的URL
            const completed = result.completed;
            const failed = result.failed;

            // 使用进度管理器完成显示
            let message = `下载完成！成功下载 ${completed} 张图片`;
            if (failed > 0) {
                message += `，失败 ${failed} 张`;
                if (failedUrls.length > 0) {
                    console.log('下载失败的图片URL:', failedUrls);
                }
            }
            progressManager.complete(message);

            // 重置UI
            setTimeout(() => {
                progressSection.style.display = 'none';
                downloadBtn.disabled = false;
                downloadBtn.textContent = '下载选中';
                progressManager.reset();

                // 更新起始序号
                document.getElementById('startNumber').value = currentNumber;

                // 显示结果提示
                if (failed > 0) {
                    showTip(message, 'warning');
                } else {
                    showTip(message, 'success');
                }
            }, 1500); // 延长显示时间，让用户看到完成信息
        };

        // 将所有下载任务添加到队列
        const downloadPromises = imageUrls.map((imageUrl, index) => {
            const cleanPrefix = sanitizeFilename(namePrefix);
            const filename = `${cleanPrefix}_${currentNumber + index}`;

            // 创建下载任务
            const downloadTask = async () => {
                try {
                    await downloadImage(imageUrl, filename);
                    return { success: true, url: imageUrl };
                } catch (error) {
                    failedUrls.push(imageUrl);
                    console.error(`下载图片失败: ${imageUrl}`, error);
                    throw error;
                }
            };

            return downloadQueue.add(downloadTask);
        });

        // 等待所有下载完成（无论成功还是失败）
        try {
            await Promise.allSettled(downloadPromises);
            currentNumber += imageUrls.length; // 更新序号
        } catch (error) {
            console.error('下载过程中发生错误:', error);
        }
    }

    // 压缩包下载 - 使用增强进度显示
    async function downloadAsZip() {
        const namePrefix = document.getElementById('namePrefix').value.trim() || 'image';
        const startNumber = parseInt(document.getElementById('startNumber').value) || 1;
        const progressSection = document.querySelector('.progress-section');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const progressDetail = document.getElementById('progressDetail');
        const downloadBtn = document.getElementById('downloadSelectedBtn');

        // 检查JSZip是否可用
        if (typeof JSZip === 'undefined') {
            showTip('压缩包功能不可用，请检查网络连接或使用单独下载模式', 'error');
            return;
        }

        // 显示进度条
        progressSection.style.display = 'block';
        downloadBtn.disabled = true;
        downloadBtn.textContent = '创建压缩包...';

        // 创建并初始化进度管理器
        const progressManager = getProgressManager();
        progressManager.init(progressFill, progressText, progressDetail);

        const zip = new JSZip();
        const imageUrls = Array.from(selectedImages);
        let completed = 0;
        let failed = 0;
        let currentNumber = startNumber;
        const failedUrls = [];

        // 开始进度跟踪
        progressManager.start(imageUrls.length, '下载图片');

        // 下载所有图片并添加到压缩包
        for (let i = 0; i < imageUrls.length; i++) {
            const imageUrl = imageUrls[i];
            try {
                // 生成当前文件名
                const cleanPrefix = sanitizeFilename(namePrefix);
                const currentFileName = `${cleanPrefix}_${currentNumber}`;

                // 更新进度管理器
                progressManager.update(completed, failed, currentFileName);

                // 使用带重试的网络请求函数
                const response = await fetchWithRetry(imageUrl);

                const blob = await response.blob();

                if (!blob.type.startsWith('image/')) {
                    throw new Error(`不是有效的图片格式: ${blob.type}`);
                }

                // 生成完整文件名
                const extension = getImageExtension(imageUrl, blob.type);
                const filename = `${currentFileName}.${extension}`;

                // 添加到压缩包
                zip.file(filename, blob);

                currentNumber++;
                completed++;

                // 添加小延迟
                await new Promise(resolve => setTimeout(resolve, 100));

            } catch (error) {
                failed++;
                failedUrls.push(imageUrl);
                console.error(`下载图片失败: ${imageUrl}`, error);

                // 更新进度管理器（失败情况）
                progressManager.update(completed, failed, '');
            }
        }

        if (completed === 0) {
            showTip('没有成功下载任何图片，无法创建压缩包', 'error');
            // 重置UI
            progressSection.style.display = 'none';
            downloadBtn.disabled = false;
            downloadBtn.textContent = '下载选中';
            progressManager.reset();
            return;
        }

        let zipUrlManager = null;

        try {
            // 生成压缩包
            downloadBtn.textContent = '生成压缩包...';
            progressManager.complete('正在生成压缩包...');

            const zipBlob = await zip.generateAsync({
                type: 'blob',
                compression: 'DEFLATE',
                compressionOptions: { level: 6 }
            });

            // 下载压缩包
            const cleanPrefix = sanitizeFilename(namePrefix);
            const zipFilename = `${cleanPrefix}_images_${new Date().toISOString().slice(0, 10)}.zip`;

            // 使用资源管理器创建URL
            zipUrlManager = createManagedURL(zipBlob);

            const a = document.createElement('a');
            a.href = zipUrlManager.url;
            a.download = zipFilename;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            // 延迟清理URL，确保下载完成
            setTimeout(() => {
                if (zipUrlManager) {
                    zipUrlManager.cleanup();
                }
            }, 1000);

            // 显示结果
            let message = `压缩包下载完成！包含 ${completed} 张图片`;
            if (failed > 0) {
                message += `，失败 ${failed} 张`;
                console.log('下载失败的图片URL:', failedUrls);
                showTip(message, 'warning');
            } else {
                showTip(message, 'success');
            }

        } catch (error) {
            console.error('生成压缩包失败:', error);
            showTip('生成压缩包失败，请尝试单独下载模式', 'error');
        } finally {
            // 确保在异常情况下也能清理资源
            if (zipUrlManager) {
                // 如果还没有设置延迟清理，立即清理
                setTimeout(() => {
                    zipUrlManager.cleanup();
                }, 2000); // 稍长的延迟作为备用清理
            }
        }

        // 重置UI
        setTimeout(() => {
            progressSection.style.display = 'none';
            downloadBtn.disabled = false;
            downloadBtn.textContent = '下载选中';
            progressManager.reset();

            // 更新起始序号
            document.getElementById('startNumber').value = currentNumber;
        }, 1500); // 延长显示时间，让用户看到完成信息
    }

    // 下载单张图片
    async function downloadImage(imageUrl, filename) {
        let urlManager = null;

        try {
            // 使用带重试的网络请求函数
            const response = await fetchWithRetry(imageUrl);

            const blob = await response.blob();

            // 检查是否是有效的图片
            if (!blob.type.startsWith('image/')) {
                throw new Error(`不是有效的图片格式: ${blob.type}`);
            }

            // 使用资源管理器创建URL
            urlManager = createManagedURL(blob);

            // 获取文件扩展名
            const extension = getImageExtension(imageUrl, blob.type);
            const fullFilename = `${filename}.${extension}`;

            // 创建下载链接
            const a = document.createElement('a');
            a.href = urlManager.url;
            a.download = fullFilename;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            // 延迟清理URL，确保下载完成
            setTimeout(() => {
                if (urlManager) {
                    urlManager.cleanup();
                }
            }, 1000);

        } catch (error) {
            // 网络请求函数已经提供了详细的错误信息
            console.error(`下载失败: ${imageUrl} - ${error.message}`);
            throw error; // 直接抛出，保持错误信息的完整性
        } finally {
            // 确保在异常情况下也能清理资源
            if (urlManager) {
                // 如果还没有设置延迟清理，立即清理
                setTimeout(() => {
                    urlManager.cleanup();
                }, 2000); // 稍长的延迟作为备用清理
            }
        }
    }

    // 清理文件名，移除非法字符
    function sanitizeFilename(filename) {
        // 移除或替换非法字符
        return filename
            .replace(/[<>:"/\\|?*]/g, '_')  // 替换Windows非法字符
            .replace(/[\x00-\x1f\x80-\x9f]/g, '')  // 移除控制字符
            .replace(/^\.+/, '')  // 移除开头的点
            .replace(/\.+$/, '')  // 移除结尾的点
            .replace(/\s+/g, '_')  // 替换空格为下划线
            .substring(0, 100);  // 限制长度
    }

    // 获取图片扩展名
    function getImageExtension(url, mimeType) {
        // 从MIME类型获取扩展名
        const mimeToExt = {
            'image/jpeg': 'jpg',
            'image/jpg': 'jpg',
            'image/png': 'png',
            'image/gif': 'gif',
            'image/webp': 'webp',
            'image/svg+xml': 'svg',
            'image/bmp': 'bmp'
        };

        if (mimeType && mimeToExt[mimeType]) {
            return mimeToExt[mimeType];
        }

        // 从URL获取扩展名
        const urlExt = url.split('.').pop().split('?')[0].toLowerCase();
        if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp'].includes(urlExt)) {
            return urlExt === 'jpeg' ? 'jpg' : urlExt;
        }

        // 默认为jpg
        return 'jpg';
    }

    // 初始化
    function init() {
        // 确保DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', createMainUI);
        } else {
            createMainUI();
        }
    }

    // 启动脚本
    init();

})();
