<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Dot Variations Generator</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Global Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated background particles */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(120deg); }
            66% { transform: translateY(30px) rotate(240deg); }
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            max-width: 500px;
            padding: 48px;
            width: 100%;
            position: relative;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .theme-toggle {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 45px;
            height: 45px;
            cursor: pointer;
            color: white;
            font-size: 18px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            z-index: 10;
        }

        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        h1 {
            color: #ffffff;
            margin-bottom: 32px;
            font-size: 32px;
            font-weight: 700;
            text-align: center;
            background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .input-group {
            display: flex;
            flex-direction: column;
            gap: 24px;
            margin-bottom: 24px;
        }

        .input-wrapper {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.7);
            font-size: 18px;
            z-index: 2;
        }

        input {
            width: 100%;
            padding: 18px 20px 18px 50px;
            font-size: 16px;
            border-radius: 16px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            color: white;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            font-family: inherit;
        }

        input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        input:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        button {
            width: 100%;
            padding: 18px 24px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 16px;
            border: none;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            font-family: inherit;
        }

        button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        button:hover::before {
            left: 100%;
        }

        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        button:active {
            transform: translateY(-1px);
        }

        .loading {
            pointer-events: none;
            opacity: 0.7;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results {
            margin-top: 32px;
            max-height: 400px;
            overflow-y: auto;
            opacity: 0;
            animation: fadeInUp 0.6s ease-out 0.3s forwards;
            display: none;
        }

        .results::-webkit-scrollbar {
            width: 6px;
        }

        .results::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .results::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .results::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .email-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 16px;
            transition: all 0.3s ease;
            animation: slideInLeft 0.5s ease-out;
            min-height: 60px;
        }

        .email-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .email-text {
            color: white;
            font-size: 15px;
            font-family: 'Courier New', monospace;
            flex: 1;
            margin-right: 16px;
            line-height: 1.4;
            word-break: normal;
            overflow-wrap: normal;
            white-space: normal;
        }

        .copy-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            padding: 0;
            color: white;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            flex-shrink: 0;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .copy-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .copy-btn.copied {
            background: rgba(46, 204, 113, 0.8);
        }

        .count {
            margin-top: 24px;
            font-weight: 600;
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
            padding: 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            display: none;
        }

        .alert {
            color: #ff6b6b;
            margin-top: 16px;
            font-size: 14px;
            text-align: center;
            padding: 12px;
            background: rgba(255, 107, 107, 0.1);
            border: 1px solid rgba(255, 107, 107, 0.3);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            display: none;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Light theme styles */
        body.light-theme {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }

        body.light-theme .container {
            background: rgba(255, 255, 255, 0.9);
            color: #2d3436;
        }

        body.light-theme h1 {
            background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        body.light-theme input {
            background: rgba(255, 255, 255, 0.8);
            color: #2d3436;
            border-color: rgba(45, 52, 54, 0.2);
        }

        body.light-theme input::placeholder {
            color: rgba(45, 52, 54, 0.6);
        }

        body.light-theme .input-icon {
            color: rgba(45, 52, 54, 0.7);
        }

        body.light-theme .email-card {
            background: rgba(255, 255, 255, 0.8);
            border-color: rgba(45, 52, 54, 0.2);
            color: #2d3436;
        }

        body.light-theme .email-card:hover {
            background: rgba(255, 255, 255, 0.9);
        }

        body.light-theme .email-text {
            color: #2d3436;
        }

        body.light-theme .copy-btn {
            background: rgba(45, 52, 54, 0.1);
            color: #2d3436;
        }

        body.light-theme .copy-btn:hover {
            background: rgba(45, 52, 54, 0.2);
        }

        body.light-theme .copy-btn.copied {
            background: rgba(46, 204, 113, 0.8);
            color: white;
        }

        body.light-theme .count {
            color: #2d3436;
            background: rgba(255, 255, 255, 0.8);
        }

        body.light-theme .alert {
            background: rgba(255, 107, 107, 0.2);
            border-color: rgba(255, 107, 107, 0.4);
        }

        body.light-theme .theme-toggle {
            background: rgba(0, 0, 0, 0.1);
            color: #2d3436;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        body.light-theme .theme-toggle:hover {
            background: rgba(0, 0, 0, 0.2);
            color: #2d3436;
            transform: scale(1.1);
        }

        /* Media Queries for Responsiveness */
        @media (max-width: 768px) {
            body {
                padding: 16px;
            }

            .container {
                padding: 32px 24px;
                max-width: 100%;
            }

            h1 {
                font-size: 28px;
                margin-bottom: 24px;
            }

            input {
                padding: 16px 18px 16px 46px;
                font-size: 16px;
            }

            button {
                padding: 16px 20px;
                font-size: 16px;
            }

            .email-card {
                padding: 14px;
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
                min-height: auto;
            }

            .email-text {
                margin-right: 0;
                margin-bottom: 0;
                font-size: 14px;
                text-align: center;
                white-space: normal;
                overflow: visible;
                text-overflow: unset;
            }

            .copy-btn {
                align-self: center;
                width: 36px;
                height: 36px;
                font-size: 14px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 24px 20px;
            }

            h1 {
                font-size: 24px;
            }

            .theme-toggle {
                top: 12px;
                right: 12px;
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
        }

        /* Footer Styling */
        .credit {
            margin-top: 32px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
        }

        .credit a {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .credit a:hover {
            color: white;
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
        }

        body.light-theme .credit {
            color: rgba(45, 52, 54, 0.8);
        }

        body.light-theme .credit a {
            color: #2d3436;
        }

        /* Copy All Button Styles */
        .copy-all-container {
            display: none;
            justify-content: center;
            margin-bottom: 20px;
            animation: fadeInUp 0.6s ease-out;
        }

        .copy-all-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 14px 28px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            min-width: 160px;
            justify-content: center;
        }

        .copy-all-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
        }

        .copy-all-btn.copied {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        }

        body.light-theme .copy-all-btn {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }

        body.light-theme .copy-all-btn.copied {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        }

        @media (max-width: 768px) {
            .copy-all-btn {
                padding: 12px 24px;
                font-size: 13px;
                min-width: 140px;
            }
        }

        @media (max-width: 480px) {
            .copy-all-btn {
                padding: 10px 20px;
                font-size: 12px;
                min-width: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <button class="theme-toggle" onclick="toggleTheme()" title="Toggle theme">
            <i class="fas fa-sun" id="theme-icon"></i>
        </button>

        <h1><i class="fas fa-envelope"></i> Gmail Dot Variations Generator</h1>

        <div class="input-group">
            <div class="input-wrapper">
                <i class="fas fa-at input-icon"></i>
                <input type="text" id="email" placeholder="Enter your Gmail address" autocomplete="email">
            </div>
            <button onclick="generateVariations()" id="generate-btn">
                <i class="fas fa-magic"></i> Generate Variations
            </button>
        </div>

        <div id="alert" class="alert"></div>

        <!-- Copy All Button Container -->
        <div class="copy-all-container" id="copy-all-container">
            <button class="copy-all-btn" id="copy-all-btn" onclick="copyAllVariations()" title="Copy all email variations">
                <i class="fas fa-clipboard"></i> Copy All Variations
            </button>
        </div>

        <div class="results" id="results"></div>
        <div class="count" id="count"></div>

        <!-- <div class="credit">
            &copy; 2025 <a href="https://masandigital.com" target="_blank">masandigital.com</a>
        </div> -->
    </div>



    <script>
        // Theme management
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');

            body.classList.toggle('light-theme');

            if (body.classList.contains('light-theme')) {
                themeIcon.className = 'fas fa-moon';
                localStorage.setItem('theme', 'light');
            } else {
                themeIcon.className = 'fas fa-sun';
                localStorage.setItem('theme', 'dark');
            }
        }

        // Load saved theme
        function loadTheme() {
            const savedTheme = localStorage.getItem('theme');
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');

            if (savedTheme === 'light') {
                body.classList.add('light-theme');
                themeIcon.className = 'fas fa-moon';
            }
        }

        // Initialize page
        function initializePage() {
            // Ensure all result elements are hidden on page load
            const alertBox = document.getElementById('alert');
            const resultsDiv = document.getElementById('results');
            const countDiv = document.getElementById('count');
            const copyAllContainer = document.getElementById('copy-all-container');

            if (alertBox) alertBox.style.display = 'none';
            if (resultsDiv) {
                resultsDiv.style.display = 'none';
                resultsDiv.style.opacity = '0';
            }
            if (countDiv) {
                countDiv.style.display = 'none';
                countDiv.style.opacity = '0';
            }
            if (copyAllContainer) {
                copyAllContainer.style.display = 'none';
                copyAllContainer.style.opacity = '0';
            }
        }

        // Copy to clipboard function
        async function copyToClipboard(text, button) {
            try {
                await navigator.clipboard.writeText(text);
                const originalContent = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.classList.add('copied');

                setTimeout(() => {
                    button.innerHTML = originalContent;
                    button.classList.remove('copied');
                }, 1500);
            } catch (err) {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                const originalContent = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.classList.add('copied');

                setTimeout(() => {
                    button.innerHTML = originalContent;
                    button.classList.remove('copied');
                }, 1500);
            }
        }

        // Global variable to store current variations
        let currentVariations = [];

        function generateVariations() {
            const email = document.getElementById('email').value.trim();
            const alertBox = document.getElementById('alert');
            const resultsDiv = document.getElementById('results');
            const countDiv = document.getElementById('count');
            const generateBtn = document.getElementById('generate-btn');
            const copyAllContainer = document.getElementById('copy-all-container');

            // Reset UI
            alertBox.innerHTML = '';
            resultsDiv.innerHTML = '';
            countDiv.innerHTML = '';
            alertBox.style.display = 'none';
            resultsDiv.style.display = 'none';
            resultsDiv.style.opacity = '0';
            countDiv.style.display = 'none';
            countDiv.style.opacity = '0';
            if (copyAllContainer) {
                copyAllContainer.style.display = 'none';
                copyAllContainer.style.opacity = '0';
            }

            // Validation
            if (!email) {
                showAlert('Please enter an email address!');
                return;
            }

            if (!email.includes('@gmail.com')) {
                showAlert('Please enter a valid Gmail address!');
                return;
            }

            // Show loading state
            generateBtn.classList.add('loading');
            generateBtn.innerHTML = 'Generating...';

            // Simulate processing time for better UX
            setTimeout(() => {
                const localPart = email.split('@')[0];
                const domainPart = email.split('@')[1];

                console.log('Email:', email);
                console.log('Local part:', localPart, 'Type:', typeof localPart);
                console.log('Domain part:', domainPart);

                // Generate variations and store globally
                const localVariations = getDotVariations(localPart);
                currentVariations = localVariations.map(varEmail => varEmail + '@' + domainPart);

                console.log('Final variations:', currentVariations);

                // Clear previous results
                resultsDiv.innerHTML = '';

                // Create email cards using DOM manipulation
                for (let i = 0; i < currentVariations.length; i++) {
                    const variation = currentVariations[i];
                    console.log('Processing variation:', variation, 'Index:', i);

                    // Create card element
                    const card = document.createElement('div');
                    card.className = 'email-card';
                    card.style.animationDelay = `${i * 0.05}s`;

                    // Create email text element
                    const emailText = document.createElement('div');
                    emailText.className = 'email-text';
                    emailText.textContent = variation;

                    // Create copy button
                    const copyBtn = document.createElement('button');
                    copyBtn.className = 'copy-btn';
                    copyBtn.innerHTML = '<i class="fas fa-copy"></i>';
                    copyBtn.title = 'Copy email address';
                    copyBtn.onclick = function() {
                        copyToClipboard(variation, this);
                    };

                    // Append elements
                    card.appendChild(emailText);
                    card.appendChild(copyBtn);
                    resultsDiv.appendChild(card);
                }
                countDiv.innerHTML = `<i class="fas fa-list-ol"></i> Total Variations: ${currentVariations.length}`;

                // Show results with animation
                if (copyAllContainer) {
                    copyAllContainer.style.display = 'flex';
                    copyAllContainer.style.opacity = '1';
                }
                resultsDiv.style.display = 'block';
                resultsDiv.style.opacity = '1';
                countDiv.style.display = 'block';
                countDiv.style.opacity = '1';

                // Reset button
                generateBtn.classList.remove('loading');
                generateBtn.innerHTML = '<i class="fas fa-magic"></i> Generate Variations';
            }, 800);
        }

        // Copy all variations function
        async function copyAllVariations() {
            if (currentVariations.length === 0) {
                showAlert('No variations to copy! Please generate variations first.');
                return;
            }

            const copyAllBtn = document.getElementById('copy-all-btn');
            const allVariationsText = currentVariations.join('\n');

            try {
                await navigator.clipboard.writeText(allVariationsText);
                const originalContent = copyAllBtn.innerHTML;
                copyAllBtn.innerHTML = '<i class="fas fa-check"></i> All Copied!';
                copyAllBtn.classList.add('copied');

                setTimeout(() => {
                    copyAllBtn.innerHTML = originalContent;
                    copyAllBtn.classList.remove('copied');
                }, 3000);
            } catch (err) {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = allVariationsText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                const originalContent = copyAllBtn.innerHTML;
                copyAllBtn.innerHTML = '<i class="fas fa-check"></i> All Copied!';
                copyAllBtn.classList.add('copied');

                setTimeout(() => {
                    copyAllBtn.innerHTML = originalContent;
                    copyAllBtn.classList.remove('copied');
                }, 3000);
            }
        }

        function showAlert(message) {
            const alertBox = document.getElementById('alert');
            alertBox.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
            alertBox.style.display = 'block';
        }

        function getDotVariations(localPart) {
            console.log('Input localPart:', localPart, 'Type:', typeof localPart);

            if (!localPart || localPart.length === 0) return [localPart];

            let results = [];
            const n = localPart.length;

            // Generate all possible combinations using binary representation
            // Each bit represents whether to add a dot before that character
            const totalCombinations = Math.pow(2, n - 1);

            for (let i = 0; i < totalCombinations; i++) {
                let variation = localPart[0]; // Start with first character

                for (let j = 1; j < n; j++) {
                    // Check if bit j-1 is set (add dot before character j)
                    if (i & (1 << (j - 1))) {
                        variation += '.';
                    }
                    variation += localPart[j];
                }

                results.push(variation);
            }

            console.log('Generated variations:', results);
            return results;
        }

        // Clear results when input is empty
        function clearResultsIfEmpty() {
            const email = document.getElementById('email').value.trim();
            if (!email) {
                const alertBox = document.getElementById('alert');
                const resultsDiv = document.getElementById('results');
                const countDiv = document.getElementById('count');
                const copyAllContainer = document.getElementById('copy-all-container');

                // Clear and hide all result elements
                if (alertBox) {
                    alertBox.innerHTML = '';
                    alertBox.style.display = 'none';
                }
                if (resultsDiv) {
                    resultsDiv.innerHTML = '';
                    resultsDiv.style.display = 'none';
                    resultsDiv.style.opacity = '0';
                }
                if (countDiv) {
                    countDiv.innerHTML = '';
                    countDiv.style.display = 'none';
                    countDiv.style.opacity = '0';
                }
                if (copyAllContainer) {
                    copyAllContainer.style.display = 'none';
                    copyAllContainer.style.opacity = '0';
                }

                // Clear current variations
                currentVariations = [];
            }
        }

        // Enter key support and input monitoring
        const emailInput = document.getElementById('email');
        emailInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                generateVariations();
            }
        });

        // Monitor input changes to clear results when empty
        emailInput.addEventListener('input', clearResultsIfEmpty);

        // Load theme and initialize page on load
        document.addEventListener('DOMContentLoaded', function() {
            loadTheme();
            initializePage();
        });
    </script>

</body>
</html>
