<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹弹珠打砖块</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(to bottom, #1a237e, #000051);
            font-family: Arial, sans-serif;
            color: white;
        }
        .container {
            display: flex;
            gap: 20px;
            padding: 20px;
            max-width: 1200px;
            width: 100%;
        }
        .game-section {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
        }
        .info-section {
            width: 300px;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
        }
        .game-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 18px;
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
        }
        canvas {
            border: 2px solid #4fc3f7;
            border-radius: 5px;
            background: rgba(0, 0, 0, 0.5);
            display: block;
            margin: 0 auto;
        }
        .controls {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #4fc3f7;
            color: #000;
            transition: all 0.3s;
            text-transform: uppercase;
            font-weight: bold;
        }
        button:hover {
            background: #81d4fa;
            transform: translateY(-2px);
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .instructions {
            margin-top: 20px;
        }
        .instructions h2 {
            color: #4fc3f7;
            margin-bottom: 15px;
            font-size: 24px;
        }
        .instructions p {
            margin: 10px 0;
            line-height: 1.5;
            color: #fff;
        }
        .key-control {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .score-display {
            font-size: 32px;
            text-align: center;
            margin: 20px 0;
            color: #4fc3f7;
        }
        .level-display {
            text-align: center;
            font-size: 24px;
            margin: 10px 0;
            color: #ffeb3b;
        }
        .level-select {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 20px;
            border-radius: 10px;
            z-index: 1000;
            max-width: 80%;
            max-height: 80vh;
            overflow-y: auto;
        }
        .level-select.active {
            display: block;
        }
        .level-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .level-btn {
            width: 100%;
            aspect-ratio: 1;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .level-select-title {
            text-align: center;
            margin-bottom: 20px;
            color: #4fc3f7;
            font-size: 24px;
        }
        .close-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: #fff;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
        }
        .close-btn:hover {
            color: #4fc3f7;
        }
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }
        .overlay.active {
            display: block;
        }
        .leaderboard {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.95);
            padding: 30px;
            border-radius: 15px;
            z-index: 1000;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            border: 2px solid #4fc3f7;
        }
        .leaderboard.active {
            display: block;
        }
        .leaderboard-title {
            text-align: center;
            margin-bottom: 20px;
            color: #4fc3f7;
            font-size: 28px;
        }
        .leaderboard-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            gap: 10px;
        }
        .tab-btn {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid #4fc3f7;
            color: #4fc3f7;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .tab-btn.active {
            background: #4fc3f7;
            color: #000;
        }
        .tab-btn:hover {
            background: rgba(79, 195, 247, 0.3);
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .leaderboard-list {
            min-height: 300px;
        }
        .leaderboard-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border-left: 4px solid #4fc3f7;
        }
        .leaderboard-item:nth-child(1) {
            border-left-color: #ffd700;
            background: rgba(255, 215, 0, 0.1);
        }
        .leaderboard-item:nth-child(2) {
            border-left-color: #c0c0c0;
            background: rgba(192, 192, 192, 0.1);
        }
        .leaderboard-item:nth-child(3) {
            border-left-color: #cd7f32;
            background: rgba(205, 127, 50, 0.1);
        }
        .leaderboard-rank {
            font-size: 20px;
            font-weight: bold;
            color: #4fc3f7;
            min-width: 40px;
        }
        .leaderboard-info {
            flex: 1;
            margin: 0 15px;
        }
        .leaderboard-score {
            font-size: 18px;
            font-weight: bold;
            color: #ffeb3b;
        }
        .leaderboard-details {
            font-size: 14px;
            color: #ccc;
            margin-top: 5px;
        }
        .leaderboard-time {
            font-size: 16px;
            color: #4fc3f7;
        }
        .leaderboard-actions {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
        .leaderboard-actions button {
            background: #f44336;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .leaderboard-actions button:hover {
            background: #d32f2f;
        }
        .empty-leaderboard {
            text-align: center;
            color: #ccc;
            font-style: italic;
            padding: 50px 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="game-section">
            <div class="game-info">
                <div>关卡: <span id="level">1</span></div>
                <div>分数: <span id="score">0</span></div>
                <div>生命: <span id="lives">3</span></div>
                <div>时间: <span id="gameTime">0:00</span></div>
            </div>
            <canvas id="gameCanvas" width="480" height="640"></canvas>
            <div class="controls">
                <button id="startBtn">开始游戏</button>
                <button id="resetBtn">重新开始</button>
                <button id="selectLevelBtn">选择关卡</button>
                <button id="leaderboardBtn">排行榜</button>
            </div>
        </div>
        <div class="info-section">
            <h2>游戏说明</h2>
            <div class="level-display">当前关卡进度</div>
            <div class="score-display">得分: <span id="bigScore">0</span></div>
            <div class="instructions">
                <div class="key-control">
                    <p>← → 方向键或鼠标移动挡板</p>
                </div>
                <div class="key-control">
                    <p>空格键或点击开始发球</p>
                </div>
                <p>• 用挡板接住小球</p>
                <p>• 击碎所有砖块过关</p>
                <p>• 不同颜色砖块分数不同</p>
                <p>• 连续击中可获得额外分数</p>
                <p>• 连击2次：双倍伤害</p>
                <p>• 连击3次：三倍伤害 + 颜色连锁</p>
                <p>• 每关难度递增</p>
                <p>• 球掉落失去一条生命</p>
                <p>• 查看排行榜记录你的进步</p>
            </div>
        </div>
    </div>

    <div class="overlay" id="overlay"></div>
    <div class="level-select" id="levelSelect">
        <button class="close-btn" id="closeLevelSelect">&times;</button>
        <h2 class="level-select-title">选择关卡</h2>
        <div class="level-grid" id="levelGrid">
            <!-- 关卡按钮将通过 JavaScript 动态生成 -->
        </div>
    </div>

    <div class="leaderboard" id="leaderboard">
        <button class="close-btn" id="closeLeaderboard">&times;</button>
        <h2 class="leaderboard-title">排行榜</h2>
        <div class="leaderboard-tabs">
            <button class="tab-btn active" data-tab="score">最高分数</button>
            <button class="tab-btn" data-tab="time">最快通关</button>
            <button class="tab-btn" data-tab="level">最高关卡</button>
        </div>
        <div class="leaderboard-content">
            <div class="tab-content active" id="scoreTab">
                <div class="leaderboard-list" id="scoreList">
                    <!-- 分数排行榜将通过 JavaScript 动态生成 -->
                </div>
            </div>
            <div class="tab-content" id="timeTab">
                <div class="leaderboard-list" id="timeList">
                    <!-- 时间排行榜将通过 JavaScript 动态生成 -->
                </div>
            </div>
            <div class="tab-content" id="levelTab">
                <div class="leaderboard-list" id="levelList">
                    <!-- 关卡排行榜将通过 JavaScript 动态生成 -->
                </div>
            </div>
        </div>
        <div class="leaderboard-actions">
            <button id="clearLeaderboard">清空记录</button>
        </div>
    </div>

    <script src="game.js"></script>
</body>
</html>
