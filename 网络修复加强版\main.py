import sys
import ctypes
import logging
import traceback
import win32gui
import win32con
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtGui import QPalette, QColor
from network_tool import ProxyNetworkTool
from PyQt5.QtCore import QMutex
import os
from time import time

def hide_console():
    """隐藏控制台窗口"""
    try:
        # 获取控制台窗口句柄
        console_hwnd = win32gui.GetForegroundWindow()
        # 隐藏控制台
        win32gui.ShowWindow(console_hwnd, win32con.SW_HIDE)
    except Exception as e:
        logging.error(f"隐藏控制台失败: {str(e)}")

def run_as_admin():
    """以管理员权限运行程序"""
    try:
        if ctypes.windll.shell32.IsUserAnAdmin():
            return True
            
        # 获取当前执行文件的路径
        if hasattr(sys, '_MEIPASS'):  # 如果是打包后的exe
            executable = sys.executable
            arguments = __file__
        else:  # 如果是python脚本
            executable = sys.executable
            arguments = '"' + __file__ + '"'

        logging.info(f"尝试以管理员权限重启: {executable} {arguments}")
        
        # 请求UAC权限
        ret = ctypes.windll.shell32.ShellExecuteW(
            None, 
            "runas",
            executable,
            arguments,
            None, 
            1  # SW_SHOWNORMAL
        )
        
        if ret > 32:  # 成功启动新进程
            logging.info("成功启动管理员权限进程")
            return True
        else:
            logging.error(f"启动管理员权限进程失败，返回值: {ret}")
            return False
            
    except Exception as e:
        logging.error(f"请求管理员权限时出错: {str(e)}")
        logging.error(traceback.format_exc())
        return False

def main():
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('network_tool.log', encoding='utf-8', mode='a'),
            logging.StreamHandler()
        ]
    )
    
    logging.info("程序开始启动...")

    try:
        # 检查是否具有管理员权限
        if not ctypes.windll.shell32.IsUserAnAdmin():
            logging.info("检测到程序不是以管理员权限运行")
            if run_as_admin():
                logging.info("已请求管理员权限，程序将重新启动")
                sys.exit(0)
            else:
                logging.error("获取管理员权限失败")
                QMessageBox.critical(None, "错误", "程序需要管理员权限才能正常运行！\n请右键点击程序，选择'以管理员身份运行'。")
                sys.exit(1)
        
        # 隐藏控制台窗口
        hide_console()

        # 创建应用实例
        app = QApplication(sys.argv)
        logging.info("QApplication 实例已创建")

        # 设置全局样式
        app.setStyle("Fusion")
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(245, 245, 245))
        app.setPalette(palette)
        logging.info("应用样式已设置")

        # 创建主窗口
        try:
            window = ProxyNetworkTool()
            logging.info("主窗口已创建")
            window.show()
            logging.info("主窗口已显示")
        except Exception as e:
            logging.error(f"创建主窗口时出错: {str(e)}")
            logging.error(traceback.format_exc())
            QMessageBox.critical(None, "错误", f"创建主窗口失败: {str(e)}")
            sys.exit(1)

        # 运行应用
        logging.info("开始运行应用...")
        sys.exit(app.exec_())
    except Exception as e:
        error_msg = f"程序启动错误: {str(e)}\n{traceback.format_exc()}"
        logging.error(error_msg)
        QMessageBox.critical(None, "错误", f"程序启动失败: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
