import sys
import requests
from bs4 import BeautifulSoup
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
                           QPushButton, QTextEdit, QLabel, QProgressBar, 
                           QMessageBox, QMainWindow, QStatusBar, QDialog)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont, QPalette, QColor
import json
from datetime import datetime
import os

class FetchWorker(QThread):
    result1 = pyqtSignal(str)
    result2 = pyqtSignal(str)
    error = pyqtSignal(str)
    progress = pyqtSignal(int)

    def run(self):
        try:
            url = 'https://ggm.deno.dev'
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'
            }

            self.progress.emit(20)
            response = requests.get(url, headers=headers, timeout=10)
            self.progress.emit(50)
            
            if response.status_code != 200:
                self.error.emit(f"服务器返回错误: {response.status_code}")
                return

            soup = BeautifulSoup(response.text, 'html.parser')
            self.progress.emit(70)

            # 处理第一个响应
            div_content1 = soup.find('div', {'id': 'response1'})
            if div_content1 is not None:
                div_content1 = div_content1.text
            else:
                div_content1 = "未找到beta模型信息"
            strings1 = div_content1.split(',')
            result1 = '\n'.join(string.strip() for string in strings1)
            self.result1.emit(result1)

            self.progress.emit(85)

            # 处理第二个响应
            div_content2 = soup.find('div', {'id': 'response2'})
            if div_content2 is not None:
                div_content2 = div_content2.text
            else:
                div_content2 = "未找到正式版模型信息"
            strings2 = div_content2.split(',')
            result2 = '\n'.join(string.strip() for string in strings2)
            self.result2.emit(result2)

            self.progress.emit(100)

        except requests.exceptions.Timeout:
            self.error.emit("连接超时，请检查网络连接")
        except requests.exceptions.RequestException as e:
            self.error.emit(f"网络错误: {str(e)}")
        except Exception as e:
            self.error.emit(f"发生错误: {str(e)}")

class ModelFetcher(QMainWindow):
    def __init__(self):
        super().__init__()
        # 确保在程序当前目录下创建data文件夹
        self.app_dir = os.path.dirname(os.path.abspath(__file__))
        self.data_dir = os.path.join(self.app_dir, 'data')
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
            
        self.history_file = os.path.join(self.data_dir, "model_history.json")
        self.history = []
        self.initUI()
        self.load_history()

    def initUI(self):
        self.setWindowTitle('Gemini模型查询工具')
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QLabel {
                font-size: 12px;
                color: #333;
            }
            QPushButton {
                background-color: #4a90e2;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #357abd;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px;
                background-color: white;
                font-family: "Consolas", "Microsoft YaHei", monospace;
            }
            QProgressBar {
                border: 1px solid #ddd;
                border-radius: 4px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #4a90e2;
            }
        """)

        # 创建中央部件和主布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 标题区域
        title_label = QLabel("Gemini可用模型列表")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px 0;")
        main_layout.addWidget(title_label)

        # 按钮区域
        button_layout = QHBoxLayout()
        self.fetch_button = QPushButton('获取模型列表')
        self.fetch_button.setFixedWidth(120)
        self.fetch_button.clicked.connect(self.start_fetching)
        
        button_layout.addWidget(self.fetch_button)
        button_layout.addStretch()
        main_layout.addLayout(button_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(10)
        self.progress_bar.hide()
        main_layout.addWidget(self.progress_bar)

        # 结果显示区域
        results_layout = QHBoxLayout()
        
        # Beta模型区域
        beta_layout = QVBoxLayout()
        beta_label = QLabel("Beta版本模型")
        beta_label.setStyleSheet("font-weight: bold;")
        self.result_text1 = QTextEdit()
        self.result_text1.setReadOnly(True)
        self.result_text1.setPlaceholderText('点击"获取模型列表"更新数据')
        beta_layout.addWidget(beta_label)
        beta_layout.addWidget(self.result_text1)
        
        # 正式版模型区域
        stable_layout = QVBoxLayout()
        stable_label = QLabel("正式版本模型")
        stable_label.setStyleSheet("font-weight: bold;")
        self.result_text2 = QTextEdit()
        self.result_text2.setReadOnly(True)
        self.result_text2.setPlaceholderText('点击"获取模型列表"更新数据')
        stable_layout.addWidget(stable_label)
        stable_layout.addWidget(self.result_text2)
        
        results_layout.addLayout(beta_layout)
        results_layout.addLayout(stable_layout)
        main_layout.addLayout(results_layout)

        # 添加查看历史按钮
        self.history_button = QPushButton('查看历史')
        self.history_button.setFixedWidth(120)
        self.history_button.clicked.connect(self.show_history)
        button_layout.addWidget(self.history_button)

        # 状态栏
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage('就绪')

        # 设置窗口大小和位置
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.setGeometry(
            (screen.width() - 800) // 2,
            (screen.height() - 600) // 2,
            800,
            600
        )

    def start_fetching(self):
        self.fetch_button.setEnabled(False)
        self.progress_bar.show()
        self.progress_bar.setValue(0)
        self.statusBar.showMessage('正在获取数据...')
        
        self.worker = FetchWorker()
        self.worker.result1.connect(self.update_result1)
        self.worker.result2.connect(self.update_result2)
        self.worker.error.connect(self.show_error)
        self.worker.progress.connect(self.update_progress)
        self.worker.finished.connect(self.fetch_completed)
        self.worker.start()

    def update_result1(self, text):
        self.result_text1.setPlainText(text)
        self.save_to_history("Beta模型", text)

    def update_result2(self, text):
        self.result_text2.setPlainText(text)
        self.save_to_history("正式版模型", text)

    def show_error(self, message):
        QMessageBox.warning(self, '错误', message)
        self.statusBar.showMessage('获取数据失败')
        self.progress_bar.hide()
        self.fetch_button.setEnabled(True)

    def update_progress(self, value):
        self.progress_bar.setValue(value)

    def fetch_completed(self):
        self.fetch_button.setEnabled(True)
        self.statusBar.showMessage('数据获取完成')
        self.progress_bar.hide()

    def load_history(self):
        if os.path.exists(self.history_file):
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    self.history = json.load(f)
            except:
                self.history = []
        self.update_history_display()

    def save_to_history(self, model_type, content):
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.history.insert(0, {
            "time": current_time,
            "type": model_type,
            "content": content
        })
        self.history = self.history[:5]  # 只保留最近5条记录
        
        with open(self.history_file, 'w', encoding='utf-8') as f:
            json.dump(self.history, f, ensure_ascii=False, indent=2)
        
        self.update_history_display()

    def show_history(self):
        history_window = HistoryWindow(self.history, self)
        history_window.exec_()

    def update_history_display(self):
        # 历史记录不再在主界面显示，只保存到文件中
        pass
        
    def clear_history(self):
        self.history = []
        if os.path.exists(self.history_file):
            try:
                os.remove(self.history_file)
            except Exception as e:
                QMessageBox.warning(self, '错误', f'清除历史记录文件时出错：{str(e)}')

class HistoryWindow(QDialog):
    def __init__(self, history, parent=None):
        super().__init__(parent)
        self.history = history
        self.initUI()
        
    def display_history(self):
        self.history_text.clear()
        if not self.history:
            self.history_text.append("暂无历史记录")
            return
            
        for record in self.history:
            self.history_text.append(
                f"时间: {record['time']}\n"
                f"类型: {record['type']}\n"
                f"内容: {record['content']}\n"
                f"{'-'*50}\n"
            )
            
    def clear_history(self):
        reply = QMessageBox.question(self, '确认', '确定要清除所有历史记录吗？',
                                   QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.parent().clear_history()
            self.history = []
            self.display_history()
            QMessageBox.information(self, '成功', '历史记录已清除')
        
    def initUI(self):
        self.setWindowTitle('查询历史')
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QLabel {
                font-size: 12px;
                color: #333;
            }
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px;
                background-color: white;
                font-family: "Consolas", "Microsoft YaHei", monospace;
            }
        """)
        
        # 设置窗口大小和位置
        self.setFixedSize(500, 400)
        
        # 创建布局
        layout = QVBoxLayout(self)
        
        # 创建标题
        title = QLabel("查询历史记录")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px 0;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 创建历史记录显示区域
        self.history_text = QTextEdit()
        self.history_text.setReadOnly(True)
        layout.addWidget(self.history_text)
        
        # 显示历史记录
        self.display_history()
        
        # 创建清除历史按钮
        button_layout = QHBoxLayout()
        clear_button = QPushButton("清除历史")
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #ff6b6b;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #ff5252;
            }
        """)
        clear_button.clicked.connect(self.clear_history)
        button_layout.addStretch()
        button_layout.addWidget(clear_button)
        layout.addLayout(button_layout)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用Fusion风格
    fetcher = ModelFetcher()
    fetcher.show()
    sys.exit(app.exec_())
