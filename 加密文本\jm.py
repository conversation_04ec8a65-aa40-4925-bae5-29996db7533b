#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
import hashlib
import os
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

def derive_key(password: str, salt: bytes) -> bytes:
    """从密码和盐值派生密钥"""
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    return base64.urlsafe_b64encode(kdf.derive(password.encode()))

def encrypt(text: str, password: str) -> str:
    """加密文本"""
    try:
        # 生成随机盐值
        salt = os.urandom(16)
        
        # 派生密钥
        key = derive_key(password, salt)
        
        # 创建Fernet实例
        f = Fernet(key)
        
        # 加密文本
        encrypted_data = f.encrypt(text.encode('utf-8'))
        
        # 将盐值和加密数据组合并编码
        combined = salt + encrypted_data
        result = base64.b64encode(combined).decode('utf-8')
        
        return result
        
    except Exception as e:
        return f"加密失败: {str(e)}"

def decrypt(encrypted_text: str, password: str) -> str:
    """解密文本"""
    try:
        # 解码base64
        combined = base64.b64decode(encrypted_text.encode('utf-8'))
        
        # 分离盐值和加密数据
        salt = combined[:16]
        encrypted_data = combined[16:]
        
        # 派生密钥
        key = derive_key(password, salt)
        
        # 创建Fernet实例
        f = Fernet(key)
        
        # 解密数据
        decrypted_data = f.decrypt(encrypted_data)
        
        return decrypted_data.decode('utf-8')
        
    except Exception as e:
        return f"解密失败: {str(e)}"

if __name__ == "__main__":
    # 简单测试
    test_text = "Hello, 世界！这是一个测试文本。"
    test_password = "test123"
    
    print("原文:", test_text)
    
    # 加密
    encrypted = encrypt(test_text, test_password)
    print("密文:", encrypted)
    
    # 解密
    decrypted = decrypt(encrypted, test_password)
    print("解密:", decrypted)
    
    # 验证
    if test_text == decrypted:
        print("✅ 加密解密测试成功！")
    else:
        print("❌ 加密解密测试失败！")
