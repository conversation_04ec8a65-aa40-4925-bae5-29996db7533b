#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
百度翻译应用主模块
"""

import sys
import threading
import pyperclip
import tkinter as tk
from tkinter import ttk, messagebox, Menu, Frame, StringVar, BooleanVar
from tkinter.font import Font
import os
import json
import time

# 导入自定义模块
from config import UI, LANGUAGES
from translator import BaiduTranslator
from history import TranslationHistory
import fanyi_support

class StatusBar(Frame):
    """状态栏组件"""
    
    def __init__(self, master, **kwargs):
        """初始化状态栏"""
        Frame.__init__(self, master, **kwargs)
        self.configure(bg=UI['bg'])
        self.label = tk.Label(self, bd=0, relief=tk.FLAT, anchor=tk.W,
                          font=UI['font'], bg=UI['bg'], fg=UI['fg'],
                          padx=0, pady=3)
        self.label.pack(fill=tk.X)
        
    def set(self, text):
        """设置状态栏文本"""
        self.label.config(text=text)
        self.label.update_idletasks()
        
    def clear(self):
        """清空状态栏"""
        self.label.config(text="")
        self.label.update_idletasks()

class HistoryDialog(tk.Toplevel):
    """历史记录对话框"""
    
    def __init__(self, parent, history_manager, apply_callback):
        """初始化历史记录对话框
        
        参数:
            parent: 父窗口
            history_manager: 历史记录管理器
            apply_callback: 应用历史记录的回调函数
        """
        super().__init__(parent)
        self.title("翻译历史记录")
        self.geometry("800x500")
        self.minsize(600, 400)
        self.history_manager = history_manager
        self.apply_callback = apply_callback
        
        # 创建界面
        self._create_widgets()
        self._load_history()
        
        # 设置模态
        self.transient(parent)
        self.grab_set()
        self.protocol("WM_DELETE_WINDOW", self.destroy)
        
    def _create_widgets(self):
        """创建界面组件"""
        # 创建工具栏
        toolbar = Frame(self)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)
        
        # 刷新按钮
        refresh_btn = ttk.Button(toolbar, text="刷新", command=self._load_history)
        refresh_btn.pack(side=tk.LEFT, padx=5)
        
        # 清空按钮
        clear_btn = ttk.Button(toolbar, text="清空历史", command=self._clear_history)
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # 创建Treeview
        columns = ("时间", "源语言", "目标语言", "原文", "译文")
        self.tree = ttk.Treeview(self, columns=columns, show="headings")
        
        # 设置列标题
        for col in columns:
            self.tree.heading(col, text=col)
            
        # 设置列宽
        self.tree.column("时间", width=150)
        self.tree.column("源语言", width=80)
        self.tree.column("目标语言", width=80)
        self.tree.column("原文", width=200)
        self.tree.column("译文", width=200)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=scrollbar.set)
        
        # 布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.tree.bind("<Double-1>", self._on_item_double_click)
        
        # 绑定右键菜单
        self.context_menu = Menu(self, tearoff=0)
        self.context_menu.add_command(label="应用此记录", command=self._apply_selected)
        self.context_menu.add_command(label="删除此记录", command=self._delete_selected)
        self.tree.bind("<Button-3>", self._show_context_menu)
        
    def _load_history(self):
        """加载历史记录"""
        # 清空现有记录
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # 加载历史记录
        history = self.history_manager.get_history()
        for i, record in enumerate(history):
            values = (
                record["datetime"],
                LANGUAGES.get(record["from_lang"], record["from_lang"]),
                LANGUAGES.get(record["to_lang"], record["to_lang"]),
                record["source_text"][:50] + ("..." if len(record["source_text"]) > 50 else ""),
                record["translated_text"][:50] + ("..." if len(record["translated_text"]) > 50 else "")
            )
            self.tree.insert("", tk.END, iid=str(i), values=values)
            
    def _clear_history(self):
        """清空历史记录"""
        if messagebox.askyesno("确认", "确定要清空所有历史记录吗？"):
            self.history_manager.clear_history()
            self._load_history()
            
    def _on_item_double_click(self, event):
        """双击记录时的处理"""
        self._apply_selected()
        
    def _apply_selected(self):
        """应用选中的记录"""
        selected = self.tree.selection()
        if not selected:
            return
            
        # 获取选中的记录
        index = int(selected[0])
        record = self.history_manager.get_history()[index]
        
        # 调用回调函数
        self.apply_callback(record)
        
        # 关闭对话框
        self.destroy()
        
    def _delete_selected(self):
        """删除选中的记录"""
        selected = self.tree.selection()
        if not selected:
            return
            
        # 获取选中的记录索引
        index = int(selected[0])
        
        # 删除记录
        if self.history_manager.delete_record(index):
            self._load_history()
            
    def _show_context_menu(self, event):
        """显示右键菜单"""
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

class TranslatorApp:
    """百度翻译应用主类"""
    
    def __init__(self, root):
        """初始化应用
        
        参数:
            root: Tkinter根窗口
        """
        self.root = root
        self.translator = BaiduTranslator()
        self.history_manager = TranslationHistory()
        
        # 设置窗口属性
        self.root.title(UI['title'])
        self.root.minsize(600, 400)
        self.root.configure(bg=UI['bg'])
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # 使用定时器定期保存窗口状态
        self._last_save_time = 0
        self._save_interval = 5000  # 5秒
        self._schedule_state_save()
        
        # 语言设置
        self.from_lang = StringVar(value="auto")
        self.to_lang = StringVar(value="zh")
        
        # 自动复制设置
        self.auto_copy = BooleanVar(value=True)
        
        # 创建界面
        self._create_widgets()
        self._create_menu()
        self._apply_style()
        
        # 绑定快捷键
        self._bind_shortcuts()
        
    def _schedule_state_save(self):
        """定期保存窗口状态"""
        current_time = int(time.time() * 1000)
        if current_time - self._last_save_time >= self._save_interval:
            self._save_window_state()
            self._last_save_time = current_time
        self.root.after(1000, self._schedule_state_save)
        
    def _save_window_state(self):
        """保存窗口状态到文件"""
        try:
            state = {
                'width': self.root.winfo_width(),
                'height': self.root.winfo_height()
            }
            
            # 在后台线程中保存文件
            threading.Thread(
                target=self._write_state_file,
                args=(state,),
                daemon=True
            ).start()
        except:
            pass
            
    def _write_state_file(self, state):
        """在后台线程中写入状态文件
        
        参数:
            state: 要保存的状态字典
        """
        try:
            with open('window_state.json', 'w') as f:
                json.dump(state, f)
        except:
            pass
            
    def _on_closing(self):
        """窗口关闭时的处理"""
        # 保存最终状态
        self._save_window_state()
        self.root.destroy()
        
    def _apply_style(self):
        """应用界面样式"""
        # 创建ttk样式
        style = ttk.Style()
        
        # 配置Combobox样式
        style.configure('TCombobox', 
                       fieldbackground=UI['text_bg'],
                       background=UI['button_bg'],
                       foreground=UI['text_fg'],
                       arrowcolor=UI['fg'],
                       selectbackground=UI['highlight_bg'],
                       selectforeground=UI['highlight_fg'])
        
        # 配置Button样式
        style.configure('TButton',
                       background=UI['button_bg'],
                       foreground=UI['button_fg'],
                       bordercolor=UI['border'],
                       focuscolor=UI['highlight_bg'],
                       lightcolor=UI['button_bg'],
                       darkcolor=UI['button_bg'])
        
        # 配置滚动条样式
        style.configure('TScrollbar',
                       background=UI['button_bg'],
                       arrowcolor=UI['fg'],
                       bordercolor=UI['border'],
                       troughcolor=UI['bg'],
                       gripcount=0)
        
        # 设置文本框样式
        text_config = {
            'bg': UI['text_bg'],
            'fg': UI['text_fg'],
            'insertbackground': UI['text_fg'],
            'selectbackground': UI['highlight_bg'],
            'selectforeground': UI['highlight_fg'],
            'relief': 'solid',
            'borderwidth': 1,
            'highlightthickness': 1,
            'highlightbackground': UI['border'],
            'highlightcolor': UI['highlight_bg']
        }
        self.source_text.configure(**text_config)
        self.target_text.configure(**text_config)
        
        # 设置状态栏样式
        self.status_bar.configure(bg=UI['bg'])
        self.status_bar.label.configure(
            bg=UI['bg'],
            fg=UI['fg'],
            relief='flat',
            bd=0
        )
        
    def _create_widgets(self):
        """创建界面组件"""
        padding = UI['padding']
        
        # 创建主框架
        main_frame = Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=padding, pady=padding)
        
        # 顶部工具栏
        toolbar = Frame(main_frame)
        toolbar.pack(fill=tk.X, pady=(0, padding))
        
        # 语言选择区域
        lang_frame = Frame(toolbar)
        lang_frame.pack(side=tk.LEFT)
        
        # 源语言选择
        from_lang_label = tk.Label(lang_frame, text="源语言:")
        from_lang_label.pack(side=tk.LEFT, padx=(0, 5))
        
        from_lang_combo = ttk.Combobox(lang_frame, textvariable=self.from_lang, 
                                       values=list(LANGUAGES.values()), state="readonly", width=10)
        from_lang_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # 语言交换按钮
        swap_btn = ttk.Button(lang_frame, text="⇄", width=3, command=self._swap_languages)
        swap_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 目标语言选择
        to_lang_label = tk.Label(lang_frame, text="目标语言:")
        to_lang_label.pack(side=tk.LEFT, padx=(0, 5))
        
        to_lang_combo = ttk.Combobox(lang_frame, textvariable=self.to_lang, 
                                     values=list(LANGUAGES.values()), state="readonly", width=10)
        to_lang_combo.pack(side=tk.LEFT)
        
        # 设置默认语言
        from_lang_combo.set(LANGUAGES["auto"])
        to_lang_combo.set(LANGUAGES["zh"])
        
        # 绑定语言选择事件
        from_lang_combo.bind("<<ComboboxSelected>>", self._on_language_change)
        to_lang_combo.bind("<<ComboboxSelected>>", self._on_language_change)
        
        # 右侧工具栏
        right_toolbar = Frame(toolbar)
        right_toolbar.pack(side=tk.RIGHT)
        
        # 自动复制选项
        auto_copy_check = ttk.Checkbutton(right_toolbar, text="自动复制结果", 
                                          variable=self.auto_copy)
        auto_copy_check.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 创建文本区域
        text_frame = Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        # 源文本区域
        source_frame = Frame(text_frame)
        source_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, padding//2))
        
        source_label = tk.Label(source_frame, text="原文:")
        source_label.pack(anchor=tk.W, pady=(0, 5))
        
        # 创建源文本框和滚动条的容器
        source_container = Frame(source_frame)
        source_container.pack(fill=tk.BOTH, expand=True)
        
        self.source_text = tk.Text(source_container, wrap=tk.WORD, 
                                  font=UI['text_font'], undo=True,
                                  width=40, height=15)  # 设置初始大小
        self.source_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 源文本滚动条
        source_scrollbar = ttk.Scrollbar(source_container, orient=tk.VERTICAL, 
                                        command=self.source_text.yview)
        source_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.source_text.configure(yscrollcommand=source_scrollbar.set)
        
        # 目标文本区域
        target_frame = Frame(text_frame)
        target_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(padding//2, 0))
        
        target_label = tk.Label(target_frame, text="译文:")
        target_label.pack(anchor=tk.W, pady=(0, 5))
        
        # 创建目标文本框和滚动条的容器
        target_container = Frame(target_frame)
        target_container.pack(fill=tk.BOTH, expand=True)
        
        self.target_text = tk.Text(target_container, wrap=tk.WORD, 
                                  font=UI['text_font'], undo=True,
                                  width=40, height=15)  # 设置初始大小
        self.target_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 目标文本滚动条
        target_scrollbar = ttk.Scrollbar(target_container, orient=tk.VERTICAL,
                                        command=self.target_text.yview)
        target_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.target_text.configure(yscrollcommand=target_scrollbar.set)

        # 绑定滚动同步事件
        self.source_text.bind("<MouseWheel>", self._on_mousewheel)
        self.target_text.bind("<MouseWheel>", self._on_mousewheel)
        self.source_text.bind("<Up>", self._on_up_down)
        self.target_text.bind("<Up>", self._on_up_down)
        self.source_text.bind("<Down>", self._on_up_down)
        self.target_text.bind("<Down>", self._on_up_down)
        self.source_text.bind("<Prior>", self._on_page_updown)
        self.target_text.bind("<Prior>", self._on_page_updown)
        self.source_text.bind("<Next>", self._on_page_updown)
        self.target_text.bind("<Next>", self._on_page_updown)
        
        # 按钮区域
        button_frame = Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(padding, 0))
        
        # 左侧按钮
        left_buttons = Frame(button_frame)
        left_buttons.pack(side=tk.LEFT)
        
        # 翻译按钮
        self.translate_btn = ttk.Button(left_buttons, text="翻译", command=self._translate, width=10)
        self.translate_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 清空按钮
        clear_btn = ttk.Button(left_buttons, text="清空", command=self._clear_text, width=10)
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # 复制原文按钮
        copy_source_btn = ttk.Button(left_buttons, text="复制原文", 
                             command=lambda: self._copy_text(self.source_text), width=10)
        copy_source_btn.pack(side=tk.LEFT, padx=5)
        
        # 复制译文按钮
        copy_target_btn = ttk.Button(left_buttons, text="复制译文", 
                             command=lambda: self._copy_text(self.target_text), width=10)
        copy_target_btn.pack(side=tk.LEFT, padx=5)
        
        # 右侧按钮
        right_buttons = Frame(button_frame)
        right_buttons.pack(side=tk.RIGHT)
        
        # 历史记录按钮
        history_btn = ttk.Button(right_buttons, text="历史记录", command=self._show_history, width=10)
        history_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 状态栏
        self.status_bar = StatusBar(main_frame)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=(0, UI['padding']), pady=(UI['padding'], 0))
        self.status_bar.set("就绪")
        
    def _create_menu(self):
        """创建菜单"""
        menubar = Menu(self.root)
        
        # 文件菜单
        file_menu = Menu(menubar, tearoff=0)
        file_menu.add_command(label="新建", command=self._clear_text, accelerator="Ctrl+N")
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit, accelerator="Alt+F4")
        menubar.add_cascade(label="文件", menu=file_menu)
        
        # 编辑菜单
        edit_menu = Menu(menubar, tearoff=0)
        edit_menu.add_command(label="撤销", command=lambda: self._focused_text().edit_undo(), accelerator="Ctrl+Z")
        edit_menu.add_command(label="重做", command=lambda: self._focused_text().edit_redo(), accelerator="Ctrl+Y")
        edit_menu.add_separator()
        edit_menu.add_command(label="剪切", command=self._cut, accelerator="Ctrl+X")
        edit_menu.add_command(label="复制", command=self._copy, accelerator="Ctrl+C")
        edit_menu.add_command(label="粘贴", command=self._paste, accelerator="Ctrl+V")
        edit_menu.add_separator()
        edit_menu.add_command(label="全选", command=self._select_all, accelerator="Ctrl+A")
        menubar.add_cascade(label="编辑", menu=edit_menu)
        
        # 翻译菜单
        translate_menu = Menu(menubar, tearoff=0)
        translate_menu.add_command(label="翻译", command=self._translate, accelerator="F5")
        translate_menu.add_command(label="清空", command=self._clear_text, accelerator="F8")
        translate_menu.add_separator()
        translate_menu.add_command(label="历史记录", command=self._show_history, accelerator="F3")
        menubar.add_cascade(label="翻译", menu=translate_menu)
        
        # 帮助菜单
        help_menu = Menu(menubar, tearoff=0)
        help_menu.add_command(label="关于", command=self._show_about)
        menubar.add_cascade(label="帮助", menu=help_menu)
        
        # 配置菜单样式
        menubar.configure(
            bg=UI['button_bg'],
            fg=UI['button_fg'],
            activebackground=UI['bg'],
            activeforeground=UI['fg']
        )
        
        self.root.config(menu=menubar)
        
    def _bind_shortcuts(self):
        """绑定快捷键"""
        self.root.bind("<F5>", lambda e: self._translate())
        self.root.bind("<F8>", lambda e: self._clear_text())
        self.root.bind("<F3>", lambda e: self._show_history())
        self.root.bind("<Control-n>", lambda e: self._clear_text())
        self.root.bind("<Control-a>", lambda e: self._select_all())
        
        # 为文本框添加右键菜单
        self._create_text_context_menu(self.source_text)
        self._create_text_context_menu(self.target_text)
        
    def _create_text_context_menu(self, text_widget):
        """为文本框创建右键菜单"""
        context_menu = Menu(text_widget, tearoff=0)
        context_menu.add_command(label="撤销", command=lambda: text_widget.edit_undo())
        context_menu.add_command(label="重做", command=lambda: text_widget.edit_redo())
        context_menu.add_separator()
        context_menu.add_command(label="剪切", command=lambda: text_widget.event_generate("<<Cut>>"))
        context_menu.add_command(label="复制", command=lambda: text_widget.event_generate("<<Copy>>"))
        context_menu.add_command(label="粘贴", command=lambda: text_widget.event_generate("<<Paste>>"))
        context_menu.add_separator()
        context_menu.add_command(label="全选", command=lambda: text_widget.tag_add(tk.SEL, "1.0", tk.END))
        
        text_widget.bind("<Button-3>", lambda e: self._show_text_context_menu(e, context_menu))
        
    def _show_text_context_menu(self, event, menu):
        """显示文本框右键菜单"""
        menu.post(event.x_root, event.y_root)
        
    def _focused_text(self):
        """获取当前焦点所在的文本框"""
        focused = self.root.focus_get()
        if focused in (self.source_text, self.target_text):
            return focused
        return self.source_text
        
    def _cut(self):
        """剪切文本"""
        self._focused_text().event_generate("<<Cut>>")
        
    def _copy(self):
        """复制文本"""
        self._focused_text().event_generate("<<Copy>>")
        
    def _paste(self):
        """粘贴文本"""
        self._focused_text().event_generate("<<Paste>>")
        
    def _select_all(self):
        """全选文本"""
        self._focused_text().tag_add(tk.SEL, "1.0", tk.END)
        return "break"  # 阻止默认行为
        
    def _on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        # 获取当前滚动位置
        source = event.widget
        target = self.target_text if source == self.source_text else self.source_text
        
        # Windows下滚轮事件的delta值需要除以120
        delta = -1 * (event.delta // 120)
        
        # 同步滚动两个文本框
        source.yview_scroll(delta, "units")
        target.yview_scroll(delta, "units")
        return "break"

    def _on_up_down(self, event):
        """处理上下键事件"""
        source = event.widget
        target = self.target_text if source == self.source_text else self.source_text
        
        if event.keysym == "Up":
            delta = -1
        else:
            delta = 1
            
        source.yview_scroll(delta, "units")
        target.yview_scroll(delta, "units")
        return "break"

    def _on_page_updown(self, event):
        """处理Page Up/Down事件"""
        source = event.widget
        target = self.target_text if source == self.source_text else self.source_text
        
        if event.keysym == "Prior":  # Page Up
            delta = -1
        else:  # Page Down
            delta = 1
            
        source.yview_scroll(delta, "pages")
        target.yview_scroll(delta, "pages")
        return "break"

    def _on_language_change(self, event=None):
        """语言选择变更处理"""
        # 获取语言代码
        from_lang_name = self.from_lang.get()
        to_lang_name = self.to_lang.get()
        
        # 将语言名称转换为语言代码
        from_lang_code = next((code for code, name in LANGUAGES.items() if name == from_lang_name), "auto")
        to_lang_code = next((code for code, name in LANGUAGES.items() if name == to_lang_name), "zh")
        
        # 更新语言代码
        self.from_lang.set(from_lang_name)
        self.to_lang.set(to_lang_name)
        
    def _swap_languages(self):
        """交换源语言和目标语言"""
        # 获取当前语言
        from_lang = self.from_lang.get()
        to_lang = self.to_lang.get()
        
        # 如果源语言是自动检测，则不交换
        if from_lang == LANGUAGES["auto"]:
            return
            
        # 交换语言
        self.from_lang.set(to_lang)
        self.to_lang.set(from_lang)
        
        # 交换文本
        source_text = self.source_text.get("1.0", tk.END).strip()
        target_text = self.target_text.get("1.0", tk.END).strip()
        
        if source_text and target_text:
            self.source_text.delete("1.0", tk.END)
            self.source_text.insert("1.0", target_text)
            
            self.target_text.delete("1.0", tk.END)
            self.target_text.insert("1.0", source_text)
        
    def _translate(self):
        """执行翻译"""
        # 获取源文本，保留换行符
        text = self.source_text.get("1.0", "end-1c")
        if not text.strip():
            self._show_error("请输入要翻译的文本")
            return
            
        # 获取语言设置并转换为语言代码
        from_lang_name = self.from_lang.get()
        to_lang_name = self.to_lang.get()
        
        # 将语言名称转换为语言代码
        from_lang_code = next((code for code, name in LANGUAGES.items() if name == from_lang_name), "auto")
        to_lang_code = next((code for code, name in LANGUAGES.items() if name == to_lang_name), "zh")
        
        # 禁用翻译按钮，显示进度状态
        self.translate_btn.configure(state='disabled')
        self.status_bar.set("正在翻译...")
        self.root.update_idletasks()
        
        # 在后台线程中执行翻译
        threading.Thread(
            target=self._do_translate,
            args=(text, from_lang_code, to_lang_code),
            daemon=True
        ).start()
        
    def _do_translate(self, text, from_lang, to_lang):
        """在线程中执行翻译
        
        参数:
            text: 要翻译的文本
            from_lang: 源语言代码
            to_lang: 目标语言代码
        """
        try:
            # 执行翻译
            result = self.translator.translate(text, from_lang, to_lang)
            
            # 使用事件队列更新UI
            self.root.after(0, self._update_translation_result, result)
            
        except Exception as e:
            # 发生错误时在主线程中显示错误信息
            self.root.after(0, self._show_error, f"翻译出错: {str(e)}")
            
        finally:
            # 恢复按钮状态和清除状态栏
            self.root.after(0, self._reset_translate_state)
            
    def _update_translation_result(self, result):
        """更新翻译结果
        
        参数:
            result: 翻译结果字典
        """
        try:
            if result['success']:
                # 更新译文，保持原有格式
                self.target_text.delete("1.0", tk.END)
                translated_text = result['text']
                self.target_text.insert("1.0", translated_text)
                
                # 添加到历史记录，不要strip掉格式
                source_text = self.source_text.get("1.0", "end-1c")
                self.history_manager.add_record(
                    source_text,
                    translated_text,
                    result['from_lang'],
                    result['to_lang']
                )
                
                # 如果启用了自动复制，复制译文到剪贴板
                if self.auto_copy.get():
                    self.root.after(100, lambda: pyperclip.copy(translated_text))
                    self.status_bar.set("翻译完成，已复制到剪贴板")
                else:
                    self.status_bar.set("翻译完成")
            else:
                self._show_error(result['error'])
        except Exception as e:
            self._show_error(f"处理翻译结果时出错: {str(e)}")
            
    def _reset_translate_state(self):
        """重置翻译状态"""
        self.translate_btn.configure(state='normal')
        if not self.status_bar.label.cget('text').startswith("翻译完成"):
            self.status_bar.set("就绪")
            
    def _show_error(self, message):
        """显示错误信息"""
        self.status_bar.set(f"错误: {message}")
        self.translate_btn.configure(state='normal')
        
    def _clear_text(self):
        """清空文本"""
        self.source_text.delete("1.0", tk.END)
        self.target_text.delete("1.0", tk.END)
        self.status_bar.set("已清空")
        
    def _copy_text(self, text_widget):
        """复制文本框内容到剪贴板"""
        text = text_widget.get("1.0", tk.END).strip()
        if text:
            pyperclip.copy(text)
            self.status_bar.set("已复制到剪贴板")
        
    def _show_history(self):
        """显示历史记录对话框"""
        HistoryDialog(self.root, self.history_manager, self._apply_history_record)
        
    def _apply_history_record(self, record):
        """应用历史记录"""
        # 更新文本
        self.source_text.delete("1.0", tk.END)
        self.source_text.insert("1.0", record["source_text"])
        
        self.target_text.delete("1.0", tk.END)
        self.target_text.insert("1.0", record["translated_text"])
        
        # 更新语言选择
        from_lang_name = LANGUAGES.get(record["from_lang"], LANGUAGES["auto"])
        to_lang_name = LANGUAGES.get(record["to_lang"], LANGUAGES["zh"])
        
        self.from_lang.set(from_lang_name)
        self.to_lang.set(to_lang_name)
        
        self.status_bar.set("已加载历史记录")
        
    def _show_about(self):
        """显示关于对话框"""
        about_text = """百度翻译 v2.0
        
一个基于百度翻译API的翻译工具

支持多种语言互译
支持历史记录

快捷键:
F5 - 翻译
F8 - 清空
F3 - 历史记录
"""
        messagebox.showinfo("关于", about_text)

def vp_start_gui():
    """启动应用"""
    root = tk.Tk()
    root.resizable(True, True)
    
    # 在后台线程中加载窗口状态
    def load_window_state():
        try:
            if os.path.exists('window_state.json'):
                with open('window_state.json', 'r') as f:
                    return json.load(f)
        except:
            pass
        return None
        
    # 启动加载线程
    load_thread = threading.Thread(target=load_window_state, daemon=True)
    load_thread.start()
    
    # 设置默认大小
    width = UI['width']
    height = UI['height']
    
    # 等待加载线程完成（最多等待100ms）
    load_thread.join(0.1)
    if not load_thread.is_alive():
        # 如果线程已完成，获取结果
        state = load_thread.join()
        if state:
            width = state.get('width', width)
            height = state.get('height', height)
    
    # 获取屏幕尺寸
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    
    # 确保窗口大小不超过屏幕
    width = min(max(width, 600), screen_width)
    height = min(max(height, 400), screen_height)
    
    # 计算窗口位置
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    
    # 设置窗口大小和位置
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    app = TranslatorApp(root)
    root.mainloop()

if __name__ == '__main__':
    vp_start_gui()
