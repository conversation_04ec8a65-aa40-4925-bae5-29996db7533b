<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>五子棋</title>
    <style>
        body {
            margin: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .title {
            font-size: 2em;
            color: #2c3e50;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        #board {
            border: none;
            background: #f0cea8;
            border-radius: 5px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }

        .info {
            background: #2c3e50;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 1.1em;
            margin: 15px 0;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
        }

        .current-player {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .player-indicator {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            display: inline-block;
        }

        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
        }

        button:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        button:active {
            transform: translateY(0);
        }

        .footer {
            margin-top: 20px;
            color: #7f8c8d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">五子棋</h1>
        <div class="info">
            <span class="current-player">
                当前回合: <span class="player-indicator"></span> 黑棋
            </span>
        </div>
        <canvas id="board"></canvas>
        <button onclick="resetGame()">重新开始</button>
        <div class="footer">享受游戏的乐趣</div>
    </div>

    <script>
        // 将初始化代码包装在 DOMContentLoaded 事件监听器中
        document.addEventListener('DOMContentLoaded', () => {
            const canvas = document.getElementById('board');
            const ctx = canvas.getContext('2d');
            const boardSize = 15;
            const cellSize = 35;
            const pieceSize = 15;

            canvas.width = cellSize * (boardSize + 1);
            canvas.height = cellSize * (boardSize + 1);

            let currentPlayer = 1;
            let gameOver = false;
            let gameBoard = Array(boardSize).fill().map(() => Array(boardSize).fill(0));

            // 初始化游戏
            drawBoard();
            updatePlayerIndicator();

            canvas.addEventListener('click', (e) => {
                if (gameOver) return;

                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                const { row, col } = getBoardPosition(x, y);

                if (row >= 0 && row < boardSize && 
                    col >= 0 && col < boardSize && 
                    gameBoard[row][col] === 0) {
                    
                    gameBoard[row][col] = currentPlayer;
                    drawPiece(row, col, currentPlayer);

                    if (checkWin(row, col)) {
                        const winner = currentPlayer === 1 ? '黑棋' : '白棋';
                        setTimeout(() => {
                            alert(`🎉 恭喜！${winner}获胜！`);
                            gameOver = true;
                        }, 100);
                        return;
                    }

                    // 检查是否平局
                    if (checkDraw()) {
                        setTimeout(() => {
                            alert('😅 平局！双方都发挥出色！');
                            gameOver = true;
                        }, 100);
                        return;
                    }

                    currentPlayer = currentPlayer === 1 ? 2 : 1;
                    updatePlayerIndicator();
                }
            });

            // 将所有函数定义移到这里面
            function updatePlayerIndicator() {
                const indicator = document.querySelector('.player-indicator');
                indicator.style.backgroundColor = currentPlayer === 1 ? 'black' : 'white';
                indicator.style.border = currentPlayer === 2 ? '2px solid black' : 'none';
                // 更新当前玩家文本
                const currentPlayerSpan = document.querySelector('.current-player');
                currentPlayerSpan.innerHTML = '当前回合: ';
                currentPlayerSpan.appendChild(indicator);
                currentPlayerSpan.appendChild(document.createTextNode(currentPlayer === 1 ? '黑棋' : '白棋'));
            }

            function drawBoard() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
            
                // 绘制棋盘背景
                ctx.fillStyle = '#f0cea8';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                ctx.beginPath();
                ctx.lineWidth = 1;
                ctx.strokeStyle = '#4a4a4a';
                
                // 绘制横线
                for (let i = 0; i < boardSize; i++) {
                    ctx.moveTo(cellSize, cellSize + i * cellSize);
                    ctx.lineTo(cellSize * boardSize, cellSize + i * cellSize);
                }
                
                // 绘制竖线
                for (let i = 0; i < boardSize; i++) {
                    ctx.moveTo(cellSize + i * cellSize, cellSize);
                    ctx.lineTo(cellSize + i * cellSize, cellSize * boardSize);
                }
                
                ctx.stroke();

                // 绘制棋盘中心点
                const center = Math.floor(boardSize / 2);
                ctx.beginPath();
                ctx.arc(cellSize + center * cellSize, cellSize + center * cellSize, 4, 0, Math.PI * 2);
                ctx.fillStyle = '#4a4a4a';
                ctx.fill();
            }

            function drawPiece(row, col, player) {
                const x = cellSize + col * cellSize;
                const y = cellSize + row * cellSize;
                
                // 添加阴影效果
                ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
                ctx.shadowBlur = 4;
                ctx.shadowOffsetX = 2;
                ctx.shadowOffsetY = 2;
                
                ctx.beginPath();
                ctx.arc(x, y, pieceSize, 0, Math.PI * 2);
                
                if (player === 1) {
                    // 黑棋渐变
                    const gradient = ctx.createRadialGradient(x - 3, y - 3, 1, x, y, pieceSize);
                    gradient.addColorStop(0, '#666');
                    gradient.addColorStop(1, '#000');
                    ctx.fillStyle = gradient;
                } else {
                    // 白棋渐变
                    const gradient = ctx.createRadialGradient(x - 3, y - 3, 1, x, y, pieceSize);
                    gradient.addColorStop(0, '#fff');
                    gradient.addColorStop(1, '#ddd');
                    ctx.fillStyle = gradient;
                }
                
                ctx.fill();
                
                // 重置阴影
                ctx.shadowColor = 'transparent';
                ctx.shadowBlur = 0;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;
                
                if (player === 2) {
                    ctx.strokeStyle = '#000';
                    ctx.stroke();
                }
            }

            function getBoardPosition(x, y) {
                const offsetX = x - cellSize;
                const offsetY = y - cellSize;
                
                // 计算点击位置到最近交叉点的距离
                const col = Math.round(offsetX / cellSize);
                const row = Math.round(offsetY / cellSize);
                
                // 计算点击位置到最近交叉点的实际距离
                const distX = Math.abs(offsetX - col * cellSize);
                const distY = Math.abs(offsetY - row * cellSize);
                
                // 设置允许落子的最大距离（像素）
                const maxDistance = cellSize / 3;
                
                // 确保点击位置足够接近交叉点，并且在棋盘范围内
                if (distX <= maxDistance && distY <= maxDistance &&
                    row >= 0 && row < boardSize && 
                    col >= 0 && col < boardSize) {
                    return { row, col };
                }
                return { row: -1, col: -1 };
            }

            function checkWin(row, col) {
                const directions = [
                    [[0, 1], [0, -1]],  // 水平
                    [[1, 0], [-1, 0]],  // 垂直
                    [[1, 1], [-1, -1]], // 对角线
                    [[1, -1], [-1, 1]]  // 反对角线
                ];

                for (const direction of directions) {
                    let count = 1;
                    for (const [dx, dy] of direction) {
                        let r = row + dx;
                        let c = col + dy;
                        while (
                            r >= 0 && r < boardSize &&
                            c >= 0 && c < boardSize &&
                            gameBoard[r][c] === currentPlayer
                        ) {
                            count++;
                            r += dx;
                            c += dy;
                        }
                    }
                    if (count >= 5) return true;
                }
                return false;
            }

            // 添加平局检查函数
            function checkDraw() {
                return gameBoard.every(row => row.every(cell => cell !== 0));
            }

            window.resetGame = function() {
                gameBoard = Array(boardSize).fill().map(() => Array(boardSize).fill(0));
                currentPlayer = 1;
                gameOver = false;
                drawBoard();
                updatePlayerIndicator();
            }
        });
    </script>
</body>
</html>
