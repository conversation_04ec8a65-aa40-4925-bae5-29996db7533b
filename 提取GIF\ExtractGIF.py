import tkinter as tk
from tkinter import filedialog, messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from PIL import Image
import os
import threading
import json
import queue
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime


class ModernButton(ttk.Button):
    def __init__(self, master=None, style="primary", **kwargs):
        # 使用ttkbootstrap的现代化按钮样式
        super().__init__(master, bootstyle=style, **kwargs)

        # 添加悬停效果
        self.bind('<Enter>', self._on_enter)
        self.bind('<Leave>', self._on_leave)
        self.original_style = style

    def _on_enter(self, event):
        # 悬停时改变样式
        if 'outline' not in self.original_style:
            self.configure(bootstyle=f"{self.original_style}-outline")

    def _on_leave(self, event):
        # 离开时恢复原样式
        self.configure(bootstyle=self.original_style)


def extract_frames_from_gif(gif_path, output_folder, event_queue=None):
    """从GIF中提取帧"""
    try:
        gif = Image.open(gif_path)
        
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
        
        # 获取总帧数
        total_frames = 0
        try:
            while True:
                gif.seek(total_frames)
                total_frames += 1
        except EOFError:
            pass
        
        gif.seek(0)
        for frame in range(total_frames):
            gif.seek(frame)
            # 保存为PNG以保持质量
            gif.save(os.path.join(output_folder, f"frame_{frame:03d}.png"))
            if event_queue:
                event_queue.put({
                    'type': 'update_progress',
                    'progress': (frame + 1) / total_frames * 100,
                    'frame': frame + 1,
                    'total': total_frames
                })
                
        return True, "提取完成！"
    except Exception as e:
        return False, f"错误: {str(e)}"


class GifExtractorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("🎬 GIF帧提取器")

        # 设置窗口最小尺寸和图标
        self.root.minsize(900, 650)

        # 设置深色主题
        self.style = ttk.Style("darkly")  # 使用darkly主题，现代化的深色风格
        
        # 变量
        self.gif_path_var = tk.StringVar()
        self.output_folder_var = tk.StringVar()
        self.status_var = tk.StringVar(value="准备就绪")
        self.is_extracting = False
        
        # 创建事件队列和线程池
        self.event_queue = queue.Queue()
        self.thread_pool = ThreadPoolExecutor(max_workers=2)
        
        # 设置默认输出目录
        default_output = os.path.join(os.path.expanduser("~"), "Desktop", "GIF_Frames")
        self.output_folder_var.set(default_output)
        
        self.setup_styles()
        self.setup_ui()
        self.center_window(900, 600)
        
        # 加载配置
        self.load_config()
        
        # 启动事件处理
        self.root.after(100, self.process_events)
    
    def setup_styles(self):
        # 使用ttkbootstrap的现代化样式，无需大量自定义
        # 配置一些特殊的样式
        self.style.configure('Header.TLabel',
                           font=('Microsoft YaHei UI', 18, 'bold'))

        self.style.configure('Subheader.TLabel',
                           font=('Microsoft YaHei UI', 12, 'bold'))

        self.style.configure('Info.TLabel',
                           font=('Microsoft YaHei UI', 10))

        self.style.configure('Status.TLabel',
                           font=('Microsoft YaHei UI', 9))

        # 配置卡片样式的Frame
        self.style.configure('Card.TFrame',
                           relief='solid',
                           borderwidth=1)
    
    def setup_ui(self):
        # 主容器 - 使用现代化的padding
        main_container = ttk.Frame(self.root, padding="30 20 30 30")
        main_container.pack(fill=tk.BOTH, expand=True)

        # 应用标题区域
        title_frame = ttk.Frame(main_container)
        title_frame.pack(fill=tk.X, pady=(0, 30))

        # 主标题
        header = ttk.Label(title_frame,
                          text="🎬 GIF 帧提取器",
                          style='Header.TLabel',
                          bootstyle="primary")
        header.pack()

        # 副标题
        subtitle = ttk.Label(title_frame,
                           text="轻松提取GIF动画中的每一帧",
                           style='Info.TLabel',
                           bootstyle="secondary")
        subtitle.pack(pady=(5, 0))
        
        # 文件选择卡片
        file_card = ttk.LabelFrame(main_container,
                                  text="📁 选择GIF文件",
                                  padding="20",
                                  bootstyle="primary")
        file_card.pack(fill=tk.X, pady=(0, 20))

        file_frame = ttk.Frame(file_card)
        file_frame.pack(fill=tk.X)

        self.path_entry = ttk.Entry(file_frame,
                                  textvariable=self.gif_path_var,
                                  font=('Microsoft YaHei UI', 11),
                                  bootstyle="primary")
        self.path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15))

        ModernButton(file_frame,
                    text="📂 浏览文件",
                    style="primary",
                    command=self.select_gif_file).pack(side=tk.RIGHT)
        
        # 输出文件夹选择卡片
        output_card = ttk.LabelFrame(main_container,
                                   text="💾 输出文件夹",
                                   padding="20",
                                   bootstyle="success")
        output_card.pack(fill=tk.X, pady=(0, 20))

        output_frame = ttk.Frame(output_card)
        output_frame.pack(fill=tk.X)

        self.output_entry = ttk.Entry(output_frame,
                                    textvariable=self.output_folder_var,
                                    font=('Microsoft YaHei UI', 11),
                                    bootstyle="success")
        self.output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15))

        ModernButton(output_frame,
                    text="📁 选择文件夹",
                    style="success",
                    command=self.select_output_folder).pack(side=tk.RIGHT)
        
        # 进度显示卡片
        progress_card = ttk.LabelFrame(main_container,
                                     text="⚡ 处理进度",
                                     padding="20",
                                     bootstyle="info")
        progress_card.pack(fill=tk.X, pady=(0, 20))

        # 进度信息行
        progress_info_frame = ttk.Frame(progress_card)
        progress_info_frame.pack(fill=tk.X, pady=(0, 10))

        self.frame_info = ttk.Label(progress_info_frame,
                                  text="等待开始...",
                                  style='Info.TLabel',
                                  bootstyle="info")
        self.frame_info.pack(side=tk.LEFT)

        self.progress_percent = ttk.Label(progress_info_frame,
                                        text="0%",
                                        style='Info.TLabel',
                                        bootstyle="info")
        self.progress_percent.pack(side=tk.RIGHT)

        # 现代化进度条
        self.progress_var = tk.DoubleVar()
        self.progress = ttk.Progressbar(progress_card,
                                      variable=self.progress_var,
                                      maximum=100,
                                      bootstyle="info-striped",
                                      length=400)
        self.progress.pack(fill=tk.X)
        
        # 状态显示
        status_frame = ttk.Frame(main_container)
        status_frame.pack(fill=tk.X, pady=(0, 20))

        self.status_label = ttk.Label(status_frame,
                                    textvariable=self.status_var,
                                    style='Status.TLabel',
                                    bootstyle="secondary")
        self.status_label.pack()

        # 操作按钮区域
        button_frame = ttk.Frame(main_container)
        button_frame.pack(pady=(0, 20))

        self.extract_button = ModernButton(button_frame,
                                         text="🚀 开始提取",
                                         style="warning",
                                         command=self.start_extraction)
        self.extract_button.pack(side=tk.LEFT, padx=(0, 15))

        ModernButton(button_frame,
                    text="📂 打开输出目录",
                    style="info-outline",
                    command=lambda: self.open_output_folder()).pack(side=tk.LEFT)

        # 分隔线
        separator = ttk.Separator(main_container, orient='horizontal')
        separator.pack(fill=tk.X, pady=(10, 15))

        # 版权信息
        footer_frame = ttk.Frame(main_container)
        footer_frame.pack(side=tk.BOTTOM, fill=tk.X)

        ttk.Label(footer_frame,
                 text=f"© {datetime.now().year} GIF Frame Extractor - 现代化界面版本",
                 style='Status.TLabel',
                 bootstyle="secondary").pack()
    
    def process_events(self):
        """处理事件队列中的事件"""
        try:
            while True:  # 处理队列中的所有事件
                event = self.event_queue.get_nowait()
                event_type = event.get('type')
                
                if event_type == 'update_status':
                    self.status_var.set(event['message'])
                elif event_type == 'update_progress':
                    self.progress_var.set(event['progress'])
                    self.progress_percent.config(text=f"{event['progress']:.1f}%")
                    if 'frame' in event and 'total' in event:
                        self.frame_info.config(text=f"帧 {event['frame']}/{event['total']}")
                elif event_type == 'extraction_complete':
                    self.handle_extraction_complete(event)
                elif event_type == 'error':
                    self.handle_error(event['message'])
                
                self.event_queue.task_done()
        except queue.Empty:
            pass
        finally:
            # 继续监听事件队列
            self.root.after(100, self.process_events)
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_var.set(value)
        self.progress_percent.configure(text=f"{int(value)}%")
        self.root.update_idletasks()
    
    def select_gif_file(self):
        """选择GIF文件"""
        file_path = filedialog.askopenfilename(
            filetypes=[("GIF文件", "*.gif")],
            title="选择GIF文件"
        )
        if file_path:
            self.gif_path_var.set(file_path)
            # 自动设置输出文件夹
            gif_dir = os.path.dirname(file_path)
            gif_name = os.path.splitext(os.path.basename(file_path))[0]
            output_dir = os.path.join(gif_dir, f"{gif_name}_frames")
            self.output_folder_var.set(output_dir)
            self.save_config()
    
    def select_output_folder(self):
        """选择输出文件夹"""
        folder_path = filedialog.askdirectory(title="选择输出文件夹")
        if folder_path:
            self.output_folder_var.set(folder_path)
            self.save_config()
    
    def start_extraction(self):
        """开始提取帧"""
        if self.is_extracting:
            return
        
        gif_path = self.gif_path_var.get()
        output_folder = self.output_folder_var.get()
        
        if not gif_path:
            messagebox.showerror("错误", "请选择GIF文件")
            return
        
        if not output_folder:
            messagebox.showerror("错误", "请选择输出文件夹")
            return
        
        self.is_extracting = True
        self.extract_button.config(state=tk.DISABLED)
        self.status_var.set("正在提取帧...")
        self.progress_var.set(0)
        
        # 在线程池中执行提取
        self.thread_pool.submit(self.extract_frames_thread, gif_path, output_folder)
    
    def extract_frames_thread(self, gif_path, output_folder):
        """在后台线程中执行帧提取"""
        try:
            success, message = extract_frames_from_gif(gif_path, output_folder, self.event_queue)
            
            if success:
                self.event_queue.put({
                    'type': 'extraction_complete',
                    'output_folder': output_folder
                })
            else:
                self.event_queue.put({
                    'type': 'error',
                    'message': message
                })
        except Exception as e:
            self.event_queue.put({
                'type': 'error',
                'message': str(e)
            })
        finally:
            self.is_extracting = False
            self.root.after(0, lambda: self.extract_button.config(state=tk.NORMAL))
    
    def handle_extraction_complete(self, event):
        """处理提取完成事件"""
        self.status_var.set("提取完成！")
        self.is_extracting = False
        self.extract_button.config(state=tk.NORMAL)
        
        if messagebox.askyesno("完成", "提取完成！是否打开输出文件夹？"):
            self.open_output_folder(event['output_folder'])
    
    def handle_error(self, message):
        """处理错误事件"""
        self.status_var.set(f"错误: {message}")
        self.is_extracting = False
        self.extract_button.config(state=tk.NORMAL)
        messagebox.showerror("错误", message)
    
    def open_output_folder(self, folder_path=None):
        """打开输出文件夹"""
        try:
            if folder_path is None:
                folder_path = self.output_folder_var.get()
            
            if os.path.exists(folder_path):
                if os.name == 'nt':  # Windows
                    os.startfile(folder_path)
                else:  # Linux/Mac
                    import subprocess
                    subprocess.run(['xdg-open', folder_path])
            else:
                messagebox.showerror("错误", "输出文件夹不存在")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件夹: {str(e)}")
    
    def load_config(self):
        """加载配置"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), "gif_extractor_config.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    if 'last_gif_path' in config:
                        self.gif_path_var.set(config['last_gif_path'])
                    if 'last_output_folder' in config:
                        self.output_folder_var.set(config['last_output_folder'])
        except Exception:
            pass
    
    def save_config(self):
        """保存配置"""
        try:
            config = {
                'last_gif_path': self.gif_path_var.get(),
                'last_output_folder': self.output_folder_var.get()
            }
            config_path = os.path.join(os.path.dirname(__file__), "gif_extractor_config.json")
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
        except Exception:
            pass
    
    def center_window(self, width=None, height=None):
        """将窗口居中显示"""
        if width is None:
            width = self.root.winfo_width()
        if height is None:
            height = self.root.winfo_height()
        
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.root.geometry(f'{width}x{height}+{x}+{y}')


if __name__ == "__main__":
    try:
        # 使用ttkbootstrap的深色主题窗口
        root = ttk.Window(themename="darkly")
        app = GifExtractorApp(root)
        root.mainloop()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {str(e)}")
