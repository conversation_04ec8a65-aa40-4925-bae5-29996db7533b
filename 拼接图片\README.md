# 图片竖向拼接工具 - 现代化版本

## 🎉 版本升级说明

这是图片拼接工具的全新PySide6版本，相比原tkinter版本有以下重大改进：

### ✨ 新功能特性

- **🎨 现代化UI设计**：采用PySide6框架，界面美观大方
- **🖱️ 拖拽支持**：直接拖拽文件夹到程序窗口
- **📋 图片预览**：实时显示待拼接的图片列表
- **⚡ 线程安全**：后台处理，界面不卡顿
- **📊 进度显示**：现代化进度对话框，实时显示处理状态
- **💾 配置管理**：自动保存用户设置
- **🔧 内存优化**：智能内存管理，支持大图片处理

### 🐛 BUG修复

- **线程安全问题**：使用QThread + Signal/Slot机制
- **内存泄漏**：及时释放图片资源，添加垃圾回收
- **进度显示错误**：精确的进度计算和更新
- **错误处理**：完善的异常捕获和用户提示
- **文件验证**：严格的图片文件格式验证

## 🚀 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行程序

```bash
python spliced_pictures_qt.py
```

## 📖 使用说明

### 基本操作

1. **选择图片文件夹**：
   - 点击"📂 选择文件夹"按钮
   - 或直接拖拽文件夹到左侧区域

2. **选择输出文件**：
   - 点击"💾 选择输出文件"按钮
   - 支持PNG、JPG等格式

3. **开始拼接**：
   - 点击"🚀 开始拼接"按钮
   - 等待进度对话框完成

### 支持的图片格式

- PNG (.png)
- JPEG (.jpg, .jpeg)
- BMP (.bmp)
- TIFF (.tiff)

### 配置选项

程序会自动保存以下设置：
- 上次使用的输入文件夹路径
- 上次使用的输出文件路径
- 窗口大小和位置
- 图片质量设置

## 🔧 技术特性

### 架构设计

- **主窗口**：ImageSplicerApp (QMainWindow)
- **配置管理**：ConfigManager (JSON配置)
- **拖拽区域**：DragDropArea (自定义QLabel)
- **工作线程**：SpliceWorker (QObject + QThread)
- **进度对话框**：QLoadingDialog (现代化设计)

### 性能优化

- **分批处理**：避免大量图片同时加载到内存
- **图片大小限制**：自动调整过大图片的尺寸
- **内存管理**：及时释放图片资源，强制垃圾回收
- **线程处理**：后台处理，保持UI响应性

### 错误处理

- **文件验证**：检查图片文件的有效性
- **权限检查**：验证文件读写权限
- **异常捕获**：完善的错误提示机制
- **用户反馈**：友好的错误信息显示

## 📋 版本对比

| 功能 | 原tkinter版本 | 新PySide6版本 |
|------|---------------|---------------|
| UI框架 | tkinter | PySide6 |
| 界面风格 | 传统 | 现代化 |
| 拖拽支持 | ❌ | ✅ |
| 图片预览 | ❌ | ✅ |
| 线程安全 | ⚠️ | ✅ |
| 进度显示 | 简单 | 现代化 |
| 内存管理 | 基础 | 优化 |
| 错误处理 | 基础 | 完善 |

## 🛠️ 开发信息

- **开发框架**：PySide6
- **图像处理**：Pillow (PIL)
- **设计参考**：ocr_qt.py的现代化设计风格
- **架构模式**：MVC + 线程分离
- **代码风格**：遵循项目统一规范

## 📝 更新日志

### v2.0.0 (当前版本)
- 🎉 完全重写为PySide6版本
- ✨ 添加现代化UI设计
- 🖱️ 实现拖拽功能
- 📋 添加图片预览列表
- ⚡ 优化线程安全性
- 📊 改进进度显示
- 💾 完善配置管理
- 🐛 修复所有已知BUG

### v1.0.0 (原tkinter版本)
- 基础的图片拼接功能
- 简单的tkinter界面
- 基本的配置保存

---

**享受全新的图片拼接体验！** 🚀
