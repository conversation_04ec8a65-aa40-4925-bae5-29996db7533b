import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import cv2
import os
import threading
from PIL import Image, ImageTk
import subprocess
import sys
from datetime import timedelta
import queue
import time
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor
import numpy as np

class VideoFrameExtractor:
    def __init__(self, root):
        self.root = root
        self.root.title("视频帧提取工具")
        
        # 设置主题色
        self.colors = {
            'primary': '#2196F3',
            'secondary': '#FFC107',
            'background': '#F5F5F5',
            'text': '#212121',
            'success': '#4CAF50',
            'error': '#F44336'
        }
        
        # 变量初始化
        self.video_path = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.start_frame = tk.IntVar(value=0)
        self.end_frame = tk.IntVar(value=0)
        self.output_format = tk.StringVar(value='png')  # 输出格式
        self.total_frames = 0
        self.video_fps = 0
        self.video_duration = 0
        self.preview_thread = None
        self.display_thread = None
        self.extraction_thread = None
        self.pause_event = threading.Event()
        self.pause_event.set()
        self.stop_preview = threading.Event()
        self.stop_preview.set()
        self.current_cap = None
        self.frame_queue = queue.Queue(maxsize=30)  # 30帧的缓冲区
        self.last_frame_time = 0
        
        # 设置样式
        self.setup_styles()
        
        # 创建UI
        self.create_ui()
        
        # 设置窗口大小和位置
        self.center_window(800, 720)

    def setup_styles(self):
        style = ttk.Style()
        style.configure("Custom.TFrame", background=self.colors['background'])
        style.configure("Custom.TLabel",
                       background=self.colors['background'],
                       font=("Arial", 10))
        style.configure("Header.TLabel",
                       background=self.colors['background'],
                       font=("Arial", 12, "bold"))
        style.configure("Custom.Horizontal.TProgressbar",
                       background=self.colors['primary'],
                       troughcolor=self.colors['background'])

    def create_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, style="Custom.TFrame", padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 文件选择区域
        self.create_file_selection(main_frame)
        
        # 设置区域
        self.create_settings_panel(main_frame)
        
        # 预览区域
        self.create_preview_panel(main_frame)
        
        # 控制和状态区域
        self.create_control_panel(main_frame)

    def create_file_selection(self, parent):
        file_frame = ttk.Frame(parent, style="Custom.TFrame")
        file_frame.pack(fill=tk.X, pady=(0, 20))

        # 视频选择
        ttk.Label(file_frame, text="视频文件:", style="Custom.TLabel").pack(side=tk.LEFT)
        ttk.Entry(file_frame, textvariable=self.video_path, width=50).pack(side=tk.LEFT, padx=10)
        tk.Button(file_frame,
                 text="选择视频",
                 command=self.select_video,
                 bg=self.colors['primary'],
                 fg="white",
                 font=("Arial", 10)).pack(side=tk.LEFT)

        # 输出文件夹选择
        output_frame = ttk.Frame(parent, style="Custom.TFrame")
        output_frame.pack(fill=tk.X, pady=(0, 20))
        ttk.Label(output_frame, text="输出目录:", style="Custom.TLabel").pack(side=tk.LEFT)
        ttk.Entry(output_frame, textvariable=self.output_folder, width=50).pack(side=tk.LEFT, padx=10)
        tk.Button(output_frame,
                 text="选择目录",
                 command=self.select_output_folder,
                 bg=self.colors['primary'],
                 fg="white",
                 font=("Arial", 10)).pack(side=tk.LEFT)

    def create_settings_panel(self, parent):
        settings_frame = ttk.LabelFrame(parent, text="提取设置", padding="10")
        settings_frame.pack(fill=tk.X, pady=(0, 20))

        # 帧范围设置
        frame_range_frame = ttk.Frame(settings_frame)
        frame_range_frame.pack(fill=tk.X, pady=5)
        ttk.Label(frame_range_frame, text="起始帧:").pack(side=tk.LEFT)
        ttk.Entry(frame_range_frame, textvariable=self.start_frame, width=8).pack(side=tk.LEFT, padx=5)
        ttk.Label(frame_range_frame, text="结束帧:").pack(side=tk.LEFT)
        ttk.Entry(frame_range_frame, textvariable=self.end_frame, width=8).pack(side=tk.LEFT, padx=5)

        # 输出格式选择
        format_frame = ttk.Frame(settings_frame)
        format_frame.pack(fill=tk.X, pady=5)
        ttk.Label(format_frame, text="输出格式:").pack(side=tk.LEFT)
        ttk.Radiobutton(format_frame,
                       text="PNG",
                       variable=self.output_format,
                       value="png").pack(side=tk.LEFT, padx=10)
        ttk.Radiobutton(format_frame,
                       text="JPEG",
                       variable=self.output_format,
                       value="jpg").pack(side=tk.LEFT, padx=10)

        # 视频信息显示
        self.info_label = ttk.Label(settings_frame,
                                  text="视频信息: 请选择视频文件",
                                  style="Custom.TLabel")
        self.info_label.pack(fill=tk.X, pady=5)

    def create_preview_panel(self, parent):
        preview_frame = ttk.LabelFrame(parent, text="视频预览", padding="10")
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # 预览画布
        self.preview_canvas = tk.Canvas(preview_frame,
                                      width=400,
                                      height=300,
                                      bg="black")
        self.preview_canvas.pack(pady=10)

    def create_control_panel(self, parent):
        control_frame = ttk.Frame(parent, style="Custom.TFrame")
        control_frame.pack(fill=tk.X)

        # 控制按钮
        self.start_button = tk.Button(control_frame,
                                    text="开始提取",
                                    command=self.start_extraction,
                                    bg=self.colors['success'],
                                    fg="white",
                                    font=("Arial", 10, "bold"))
        self.start_button.pack(side=tk.LEFT, padx=5)

        self.pause_button = tk.Button(control_frame,
                                    text="暂停",
                                    command=self.toggle_pause,
                                    bg=self.colors['secondary'],
                                    fg="white",
                                    font=("Arial", 10))
        self.pause_button.pack(side=tk.LEFT, padx=5)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame,
                                          mode='determinate',
                                          variable=self.progress_var,
                                          style="Custom.Horizontal.TProgressbar")
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=10)

        # 进度标签
        self.progress_label = ttk.Label(control_frame,
                                      text="就绪",
                                      style="Custom.TLabel")
        self.progress_label.pack(side=tk.LEFT, padx=5)

    def select_video(self):
        video_path = filedialog.askopenfilename(
            filetypes=[("视频文件", "*.mp4 *.avi *.mkv *.mov"), ("所有文件", "*.*")])
        if video_path:
            self.video_path.set(video_path)
            self.update_video_info()
            self.start_preview()

    def select_output_folder(self):
        output_folder = filedialog.askdirectory()
        if output_folder:
            self.output_folder.set(output_folder)

    def update_video_info(self):
        try:
            cap = cv2.VideoCapture(self.video_path.get())
            self.total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.video_fps = int(cap.get(cv2.CAP_PROP_FPS))
            self.video_duration = self.total_frames / self.video_fps
            
            info_text = f"总帧数: {self.total_frames} | "
            info_text += f"时长: {timedelta(seconds=int(self.video_duration))} | "
            info_text += f"原始帧率: {self.video_fps} fps | "
            info_text += f"分辨率: {int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))}x{int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))}"
            
            self.info_label.config(text=info_text)
            cap.release()
        except Exception as e:
            messagebox.showerror("错误", f"无法读取视频信息: {str(e)}")

    def start_preview(self):
        try:
            # Stop any existing preview
            self.stop_preview.set()
            if self.preview_thread and self.preview_thread.is_alive():
                self.preview_thread.join(timeout=1.0)
            if self.display_thread and self.display_thread.is_alive():
                self.display_thread.join(timeout=1.0)
                
            # Clean up old capture
            if self.current_cap is not None:
                self.current_cap.release()
                self.current_cap = None
                
            # Clear the preview canvas
            self.preview_canvas.delete('all')
            self.preview_canvas.update()

            # 清空帧队列
            while not self.frame_queue.empty():
                try:
                    self.frame_queue.get_nowait()
                except queue.Empty:
                    break

            # Start new preview
            self.stop_preview.clear()
            self.preview_thread = threading.Thread(target=self.preview_video, daemon=True)
            self.display_thread = threading.Thread(target=self.display_frames, daemon=True)
            self.preview_thread.start()
            self.display_thread.start()
        except Exception as e:
            print(f"启动预览时出错: {str(e)}")
            self.stop_preview.set()
            if self.current_cap is not None:
                self.current_cap.release()
                self.current_cap = None

    def calculate_preview_size(self, frame_width, frame_height, max_width=400, max_height=300):
        # 计算缩放比例，保持宽高比
        width_ratio = max_width / frame_width
        height_ratio = max_height / frame_height
        scale_ratio = min(width_ratio, height_ratio)
        
        new_width = int(frame_width * scale_ratio)
        new_height = int(frame_height * scale_ratio)
        
        return new_width, new_height

    def preview_video(self):
        try:
            video_path = self.video_path.get()
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"找不到视频文件: {video_path}")

            self.current_cap = cv2.VideoCapture(video_path)
            if not self.current_cap.isOpened():
                raise IOError(f"无法打开视频文件: {video_path}")

            # 获取原始视频尺寸
            frame_width = int(self.current_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            frame_height = int(self.current_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            preview_width, preview_height = self.calculate_preview_size(frame_width, frame_height)

            # 获取视频帧率
            video_fps = self.current_cap.get(cv2.CAP_PROP_FPS)
            frame_delay = 1.0 / video_fps if video_fps > 0 else 0.033  # 默认30fps

            while not self.stop_preview.is_set():
                try:
                    ret, frame = self.current_cap.read()
                    if not ret:
                        self.current_cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                        continue

                    # 调整预览大小并转换颜色
                    frame = cv2.resize(frame, (preview_width, preview_height))
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    
                    # 将帧放入队列
                    try:
                        self.frame_queue.put(frame, timeout=0.1)
                    except queue.Full:
                        # 如果队列满了，跳过这一帧
                        continue

                    # 控制读取速度
                    time.sleep(frame_delay)

                except Exception as frame_error:
                    print(f"预览帧处理错误: {str(frame_error)}")
                    time.sleep(0.1)

        except FileNotFoundError as e:
            messagebox.showerror("错误", str(e))
        except IOError as e:
            messagebox.showerror("错误", str(e))
        except Exception as e:
            messagebox.showerror("错误", f"预览失败: {str(e)}")
        finally:
            if self.current_cap is not None:
                self.current_cap.release()
                self.current_cap = None

    def display_frames(self):
        try:
            while not self.stop_preview.is_set():
                try:
                    # 从队列中获取帧
                    frame = self.frame_queue.get(timeout=0.1)
                    
                    # 计算帧率控制
                    current_time = time.time()
                    if self.last_frame_time > 0:
                        elapsed = current_time - self.last_frame_time
                        if elapsed < 0.033:  # 限制最高30fps
                            time.sleep(0.033 - elapsed)
                    
                    # 转换为PhotoImage
                    image = Image.fromarray(frame)
                    photo = ImageTk.PhotoImage(image)
                    
                    # 更新预览
                    if not hasattr(self, '_preview_image_id'):
                        self._preview_image_id = self.preview_canvas.create_image(
                            self.preview_canvas.winfo_width() // 2,
                            self.preview_canvas.winfo_height() // 2,
                            anchor=tk.CENTER
                        )
                    
                    self.preview_canvas.itemconfig(self._preview_image_id, image=photo)
                    self._current_photo = photo
                    
                    self.last_frame_time = current_time
                    
                except queue.Empty:
                    continue
                except Exception as frame_error:
                    print(f"显示帧错误: {str(frame_error)}")
                    time.sleep(0.1)

        except Exception as e:
            print(f"显示线程错误: {str(e)}")
        finally:
            if hasattr(self, '_preview_image_id'):
                self.preview_canvas.delete(self._preview_image_id)
                delattr(self, '_preview_image_id')
            self._current_photo = None

    def start_extraction(self):
        if not self.video_path.get() or not self.output_folder.get():
            messagebox.showwarning("警告", "请选择视频文件和输出目录")
            return

        if not os.path.exists(self.output_folder.get()):
            os.makedirs(self.output_folder.get())

        self.extraction_thread = threading.Thread(target=self.extract_frames)
        self.extraction_thread.start()
        self.start_button.config(state=tk.DISABLED)

    def save_frame(self, frame_data):
        try:
            frame, frame_count, total = frame_data
            
            # 预处理帧，减少IO操作
            if self.output_format.get() == 'jpg':
                # JPEG优化：调整质量和压缩
                encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 95]
                _, buffer = cv2.imencode('.jpg', frame, encode_param)
            else:  # png
                # PNG优化：调整压缩级别
                encode_param = [int(cv2.IMWRITE_PNG_COMPRESSION), 3]
                _, buffer = cv2.imencode('.png', frame, encode_param)
            
            # 直接写入缓冲区数据
            frame_path = os.path.join(
                self.output_folder.get(),
                f"frame_{frame_count:05d}.{self.output_format.get()}"
            )
            with open(frame_path, 'wb') as f:
                f.write(buffer.tobytes())
            
            return frame_count
        except Exception as e:
            print(f"保存帧 {frame_count} 时出错: {str(e)}")
            return None

    def update_progress(self, frame_count, start, end, processed_count, start_time):
        progress = (frame_count + 1 - start) / (end - start) * 100
        elapsed_time = time.time() - start_time
        fps = processed_count / elapsed_time if elapsed_time > 0 else 0
        eta = ((end - start) - processed_count) / fps if fps > 0 else 0
        
        self.progress_var.set(progress)
        self.progress_label.config(
            text=f"进度: {frame_count + 1}/{end} ({progress:.1f}%) | "
                 f"已保存: {processed_count}帧 | "
                 f"速度: {fps:.1f}fps | "
                 f"预计剩余时间: {timedelta(seconds=int(eta))}"
        )
        self.root.update()

    def extract_frames(self):
        try:
            # 设置视频捕获缓冲区大小
            cap = cv2.VideoCapture(self.video_path.get())
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 128)  # 增加缓冲区大小
            if not cap.isOpened():
                raise Exception("无法打开视频文件")

            # 设置帧范围
            start = self.start_frame.get()
            end = self.end_frame.get() if self.end_frame.get() > 0 else self.total_frames
            if start >= end:
                raise ValueError("起始帧必须小于结束帧")
            if end > self.total_frames:
                raise ValueError(f"结束帧不能超过总帧数({self.total_frames})")

            # 创建帧队列和结果队列
            frame_queue = queue.Queue(maxsize=128)  # 增加队列大小
            result_queue = queue.Queue()
            
            # 创建线程池，根据CPU核心数和内存情况调整
            num_threads = max(min(os.cpu_count() or 1, 16), 4)  # 至少4个线程，最多16个
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                # 启动保存结果处理线程
                processed_count = 0
                start_time = time.time()
                futures = []

                cap.set(cv2.CAP_PROP_POS_FRAMES, start)
                frame_count = start

                while frame_count < end and not self.stop_preview.is_set():
                    self.pause_event.wait()  # 等待继续信号
                    
                    ret, frame = cap.read()
                    if not ret:
                        break

                    # 提交保存任务到线程池
                    future = executor.submit(self.save_frame, (frame, frame_count, end - start))
                    futures.append(future)
                    
                    # 处理已完成的任务
                    completed = [f for f in futures if f.done()]
                    for f in completed:
                        futures.remove(f)
                        if f.result() is not None:
                            processed_count += 1
                            
                    # 更新进度
                    if frame_count % 10 == 0:  # 每10帧更新一次进度，减少UI更新开销
                        self.update_progress(frame_count, start, end, processed_count, start_time)
                    
                    frame_count += 1

                # 等待所有任务完成
                for future in concurrent.futures.as_completed(futures):
                    if future.result() is not None:
                        processed_count += 1
                    self.update_progress(end-1, start, end, processed_count, start_time)

            # 最终更新
            final_progress = 100.0
            self.progress_var.set(final_progress)
            total_time = time.time() - start_time
            avg_fps = processed_count / total_time if total_time > 0 else 0
            self.progress_label.config(
                text=f"完成! 共处理: {processed_count}帧 | "
                     f"平均速度: {avg_fps:.1f}fps | "
                     f"总耗时: {timedelta(seconds=int(total_time))}"
            )
            self.root.update_idletasks()
            
            # 完成后的清理工作
            cap.release()
            self.start_button.config(state=tk.NORMAL)
            extracted_count = end - start
            
            # 显示完成信息
            self.progress_label.config(text=f"完成！共提取 {extracted_count} 帧")
            messagebox.showinfo("完成", f"提取完成！共提取了 {extracted_count} 帧。")
            self.open_output_folder()
            
        except Exception as e:
            messagebox.showerror("错误", f"提取过程出错: {str(e)}")
            self.progress_label.config(text="提取失败")
            
        finally:
            if cap is not None:
                cap.release()
            self.start_button.config(state=tk.NORMAL)

    def toggle_pause(self):
        if self.pause_event.is_set():
            self.pause_event.clear()
            self.pause_button.config(text="继续")
        else:
            self.pause_event.set()
            self.pause_button.config(text="暂停")

    def open_output_folder(self):
        folder_path = self.output_folder.get()
        if os.name == 'nt':  # Windows
            os.startfile(folder_path)
        elif os.name == 'posix':  # macOS, Linux
            subprocess.run(['open', folder_path] if sys.platform == 'darwin' else ['xdg-open', folder_path])

    def center_window(self, width=800, height=720):
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.root.geometry(f'{width}x{height}+{x}+{y}')

if __name__ == "__main__":
    try:
        root = tk.Tk()
        app = VideoFrameExtractor(root)
        root.mainloop()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {str(e)}")
