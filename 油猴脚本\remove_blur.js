// ==UserScript==
// @name         Lenso.ai 显示元素脚本
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  点击页面后隐藏指定元素并去除模糊效果
// <AUTHOR> name
// @match        https://lenso.ai/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 监听页面的点击事件
    document.body.addEventListener('click', function(e) {
        // 找到并隐藏指定元素
        const elementToHide = document.querySelector('#app > div:nth-of-type(2) > div:nth-of-type(2) > div:nth-of-type(1) > div:nth-of-type(3) > div:nth-of-type(1)');
        if (elementToHide) {
            elementToHide.style.display = 'none';
        }

        // 找到文本区域并移除模糊效果
        const textSection = document.querySelector('.text-section');
        if (textSection) {
            // 移除所有可能的模糊效果
            textSection.style.filter = textSection.style.filter.replace(/blur\([^)]*\)/g, '');
            // 如果没有其他filter效果，将完全清除filter属性
            if (!textSection.style.filter.trim()) {
                textSection.style.filter = 'none';
            }
        }
    });
})(); 