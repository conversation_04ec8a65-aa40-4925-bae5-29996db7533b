import tkinter as tk
from tkinter import filedialog, messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import os
import sys
import subprocess
import hashlib
import threading
import queue
from concurrent.futures import Thread<PERSON>oolExecutor
from PIL import Image
import imagehash
from tkinterdnd2 import DND_FILES, TkinterDnD

class ScanWorker:
    def __init__(self, directory, threshold, queue, cancel_event):
        self.directory = directory
        self.threshold_percent = threshold
        # Perceptual hash cutoff is based on Hamming distance (0-64).
        # A lower cutoff means higher similarity is required.
        self.image_hash_cutoff = int((100 - threshold) / 100 * 64)
        self.queue = queue
        self.cancel_event = cancel_event
        self.image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff']

    def _is_image(self, filename):
        return os.path.splitext(filename)[1].lower() in self.image_extensions

    def _hash_file(self, path):
        hasher = hashlib.sha256()
        try:
            with open(path, 'rb') as f:
                while chunk := f.read(8192):
                    hasher.update(chunk)
            return hasher.hexdigest()
        except (IOError, OSError):
            return None

    def _process_file(self, path):
        if self.cancel_event.is_set():
            return None
        try:
            size_kb = round(os.path.getsize(path) / 1024, 2)
            file_hash = self._hash_file(path)
            img_hash = None

            if self._is_image(path):
                try:
                    with Image.open(path) as img:
                        img_hash = imagehash.phash(img)
                except Exception:
                    img_hash = None # Ignore images that can't be processed
            
            return path, size_kb, file_hash, img_hash
        except Exception:
            return None

    def run(self):
        filepaths = []
        self.queue.put(('status', '正在收集文件列表...'))
        for root, _, files in os.walk(self.directory):
            if self.cancel_event.is_set():
                self.queue.put(('done', '扫描已取消。'))
                return
            for name in files:
                filepaths.append(os.path.join(root, name))
        
        self.queue.put(('progress_max', len(filepaths)))
        self.queue.put(('status', '正在处理文件...'))

        exact_hashes = {}
        image_hashes = {}
        num_workers = os.cpu_count() or 1

        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            results = executor.map(self._process_file, filepaths)
            for i, result in enumerate(results):
                if self.cancel_event.is_set():
                    executor.shutdown(wait=False, cancel_futures=True)
                    self.queue.put(('done', '扫描已取消。'))
                    return

                self.queue.put(('progress', i + 1))
                if result is None: continue

                path, size_kb, file_hash, img_hash = result
                self.queue.put(('status', f"处理中：{os.path.basename(path)}"))

                if file_hash in exact_hashes:
                    exact_hashes[file_hash].append((path, size_kb))
                else:
                    exact_hashes[file_hash] = [(path, size_kb)]

                if img_hash is not None:
                    image_hashes[path] = (img_hash, size_kb)

        if self.cancel_event.is_set():
            self.queue.put(('done', '扫描已取消。'))
            return

        self.queue.put(('status', '正在比较哈希值...'))
        self._find_duplicates(exact_hashes, image_hashes)
        self.queue.put(('done', '扫描完成。'))

    def _find_duplicates(self, exact_hashes, image_hashes):
        group_index = 0

        # 报告完全相同的文件
        self.queue.put(('status', '正在整理完全相同的文件...'))
        for files in exact_hashes.values():
            if self.cancel_event.is_set(): return
            if len(files) > 1:
                group_index += 1
                representative_name = os.path.basename(files[0][0])
                self.queue.put(('result_group', (f"完全相同 - 分组 {group_index} ({representative_name[:20]}...)", files, "完全相同")))

        # 报告相似的图片
        image_paths = list(image_hashes.keys())
        processed_images = set()
        total_images = len(image_paths)
        
        if total_images < 2:
            return

        self.queue.put(('status', f'准备比较 {total_images} 张图片...'))

        for i in range(total_images):
            if self.cancel_event.is_set(): return
            
            # 更新UI状态，避免过于频繁地更新
            if (i % 5 == 0) or (i == total_images - 1):
                self.queue.put(('status', f'正在比较图片: {i + 1}/{total_images}'))

            path1 = image_paths[i]
            if path1 in processed_images:
                continue

            similar_group = [path1]
            for j in range(i + 1, total_images):
                path2 = image_paths[j]
                if path2 in processed_images:
                    continue

                hash1 = image_hashes[path1][0]
                hash2 = image_hashes[path2][0]

                if hash1 - hash2 <= self.image_hash_cutoff:
                    similar_group.append(path2)
            
            if len(similar_group) > 1:
                processed_images.update(similar_group)
                group_index += 1
                files_with_sizes = [(p, image_hashes[p][1]) for p in similar_group]
                representative_name = os.path.basename(similar_group[0])
                self.queue.put(('result_group', (f"相似图片 - 分组 {group_index} ({representative_name[:20]}...)", files_with_sizes, f"相似度 > {self.threshold_percent}%")))

class DuplicateFinderApp:
    def __init__(self, root):
        self.root = root
        self.root.title("重复文件查找器 (支持拖拽)")
        self.root.geometry("950x700")

        self.directory = ""
        self.scan_thread = None
        self.queue = queue.Queue()
        self.cancel_event = threading.Event()
        self.group_count = 0

        self._create_widgets()
        self._create_context_menu()

        # Register window for drag and drop
        self.root.drop_target_register(DND_FILES)
        self.root.dnd_bind('<<Drop>>', self._handle_drop)

        # Manually center the window since we are not using ttk.Window's helper
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def _create_widgets(self):
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.pack(fill=BOTH, expand=True)

        # --- Controls Frame ---
        controls_frame = ttk.LabelFrame(main_frame, text="控制面板", padding="15", bootstyle=INFO)
        controls_frame.pack(fill=X, pady=(0, 10), anchor=N)
        controls_frame.columnconfigure(1, weight=1)

        self.select_dir_button = ttk.Button(controls_frame, text="选择目录", command=self._select_directory, bootstyle=PRIMARY)
        self.select_dir_button.grid(row=0, column=0, padx=(0, 10), pady=5, sticky=W)
        self.dir_entry = ttk.Entry(controls_frame, text="")
        self.dir_entry.grid(row=0, column=1, columnspan=3, sticky=EW, padx=(0, 10))
        self.dir_entry.insert(0, "请选择或拖拽文件夹到此处")

        ttk.Separator(controls_frame, orient=HORIZONTAL).grid(row=1, column=0, columnspan=4, sticky=EW, pady=10)

        ttk.Label(controls_frame, text="相似度阈值 (%):").grid(row=2, column=0, sticky=W, pady=5)
        self.threshold_var = tk.IntVar(value=95)
        self.threshold_slider = ttk.Scale(controls_frame, from_=80, to=100, orient=HORIZONTAL, variable=self.threshold_var, command=self._update_threshold_label, bootstyle=INFO)
        self.threshold_slider.grid(row=2, column=1, sticky=EW, padx=(10, 10))
        self.threshold_value_label = ttk.Label(controls_frame, text=f"{self.threshold_var.get()}%", width=4)
        self.threshold_value_label.grid(row=2, column=2, padx=(0, 20))

        action_frame = ttk.Frame(controls_frame)
        action_frame.grid(row=2, column=3, sticky=E)
        self.start_button = ttk.Button(action_frame, text="开始扫描", command=self._start_scan, bootstyle=SUCCESS)
        self.start_button.pack(side=LEFT, padx=5)
        self.cancel_button = ttk.Button(action_frame, text="取消扫描", command=self._cancel_scan, state=DISABLED, bootstyle="danger-outline")
        self.cancel_button.pack(side=LEFT, padx=5)

        # --- Results Frame ---
        results_frame = ttk.LabelFrame(main_frame, text="扫描结果", padding="10", bootstyle=INFO)
        results_frame.pack(fill=BOTH, expand=True, pady=10)
        results_frame.rowconfigure(0, weight=1)
        results_frame.columnconfigure(0, weight=1)

        # Style configuration
        style = ttk.Style()
        style.configure("Treeview.Heading", font=(None, 10, 'bold'))
        style.configure("Treeview", rowheight=28)
        style.map("Treeview", background=[('selected', '#e0e0e0')], foreground=[('selected', 'black')])

        cols = ("name", "size", "type")
        self.tree = ttk.Treeview(results_frame, columns=cols, show='headings', bootstyle=INFO)
        self.tree.heading("name", text="文件/分组")
        self.tree.heading("size", text="大小 (KB)")
        self.tree.heading("type", text="匹配类型")
        self.tree.column("name", width=600)
        self.tree.column("size", width=120, anchor=E)
        self.tree.column("type", width=150, anchor=CENTER)

        self.tree.tag_configure('oddrow', background=style.colors.light)
        self.tree.tag_configure('evenrow', background=style.colors.bg)

        vsb = ttk.Scrollbar(results_frame, orient="vertical", command=self.tree.yview, bootstyle="round-info")
        self.tree.configure(yscrollcommand=vsb.set)
        self.tree.grid(row=0, column=0, sticky='nsew')
        vsb.grid(row=0, column=1, sticky='ns')
        self.tree.bind("<Button-3>", self._show_context_menu)

        self.results_summary_label = ttk.Label(results_frame, text="共找到 0 组结果", anchor=W)
        self.results_summary_label.grid(row=1, column=0, columnspan=2, sticky=EW, pady=(10, 0), padx=5)

        # --- Status Bar ---
        status_frame = ttk.Frame(main_frame, padding=(5, 5))
        status_frame.pack(fill=X, side=BOTTOM)
        status_frame.columnconfigure(1, weight=1)
        self.status_label = ttk.Label(status_frame, text="状态：空闲", anchor=W)
        self.status_label.grid(row=0, column=0, sticky=W)
        self.progress_bar = ttk.Progressbar(status_frame, orient=HORIZONTAL, mode='determinate', bootstyle="success-striped")
        self.progress_bar.grid(row=0, column=1, sticky=EW, padx=10)

    def _update_threshold_label(self, value):
        self.threshold_value_label.config(text=f"{float(value):.0f}%")

    def _set_directory(self, path):
        if os.path.isdir(path):
            self.directory = path
            self.dir_entry.delete(0, END)
            self.dir_entry.insert(0, path)
            self.start_button.config(state=NORMAL)
            self.results_summary_label.config(text="")
            self.status_label.config(text="准备就绪。请点击开始扫描。")
            # Clear previous results
            for item in self.tree.get_children():
                self.tree.delete(item)
            self.group_count = 0
        else:
            self.status_label.config(text="错误: 请提供一个有效的文件夹路径。")

    def _select_directory(self):
        directory = filedialog.askdirectory()
        if directory:
            self._set_directory(directory)

    def _handle_drop(self, event):
        # The event.data might be a single path or a list of paths, often with curly braces on Windows
        path = event.data.strip()
        if path.startswith('{') and path.endswith('}'):
            path = path[1:-1]
        
        # If multiple files/folders are dropped, tkinterdnd2 might return them as a string list.
        # We'll just take the first valid directory.
        # A more robust solution would parse this string, but for now, we handle the common case.
        if ' ' in path and not os.path.isdir(path):
            # Likely a list of items, find the first directory
            potential_paths = path.split('} {')
            found = False
            for p in potential_paths:
                clean_p = p.strip().strip('{}')
                if os.path.isdir(clean_p):
                    self._set_directory(clean_p)
                    found = True
                    break
            if not found:
                self.status_label.config(text="错误：拖拽的项目中未找到有效文件夹。")
        else:
            self._set_directory(path)

    def _start_scan(self):
        if not self.directory:
            messagebox.showerror("错误", "请先选择一个目录。", parent=self.root)
            return

        self.tree.delete(*self.tree.get_children())
        self.progress_bar.configure(bootstyle="success-striped", value=0)
        self.progress_bar.start()
        self.status_label.config(text="状态：扫描中...")
        self.start_button.config(state=DISABLED)
        self.select_dir_button.config(state=DISABLED)
        self.cancel_button.config(state=NORMAL)
        self.cancel_event.clear()
        self.group_count = 0
        self.results_summary_label.config(text="共找到 0 组结果")

        self.scan_thread = threading.Thread(
            target=ScanWorker(self.directory, self.threshold_var.get(), self.queue, self.cancel_event).run
        )
        self.scan_thread.start()
        self.root.after(100, self._process_queue)

    def _cancel_scan(self):
        if self.scan_thread and self.scan_thread.is_alive():
            self.cancel_event.set()
            self.cancel_button.config(state=DISABLED)
            self.status_label.config(text="状态：取消中...")

    def _process_queue(self):
        try:
            while not self.queue.empty():
                msg_type, data = self.queue.get_nowait()

                if msg_type == 'progress_max':
                    self.progress_bar.stop()
                    self.progress_bar['maximum'] = data
                elif msg_type == 'progress':
                    self.progress_bar['value'] = data
                elif msg_type == 'status':
                    self.status_label.config(text=data)
                elif msg_type == 'result_group':
                    group_name, files, match_type = data
                    tag = 'oddrow' if self.group_count % 2 else 'evenrow'
                    parent_id = self.tree.insert('', 'end', values=(group_name, '', match_type), open=True, tags=(tag,))
                    for path, size in files:
                        self.tree.insert(parent_id, 'end', values=(f'  {path}', size, ''), tags=(tag,))
                    self.group_count += 1
                    self.results_summary_label.config(text=f"共找到 {self.group_count} 组结果")
                elif msg_type == 'done':
                    # Stop polling the queue as the worker is finished
                    self.select_dir_button.config(state=NORMAL)
                    self.start_button.config(state=NORMAL)
                    self.cancel_button.config(state=DISABLED)
                    self.cancel_event.clear()

                    if data == '扫描完成。':
                        final_message = f"扫描完成。共找到 {self.group_count} 组重复/相似文件。"
                        self.status_label.config(text=final_message)
                        self.progress_bar.configure(bootstyle="success", value=self.progress_bar['maximum'])
                    elif data == '扫描已取消。':
                        self.status_label.config(text="扫描已取消。")
                        self.progress_bar.configure(bootstyle="danger")
                    
                    # Stop the recursive call to _process_queue
                    return

        except queue.Empty:
            pass

        if self.scan_thread and self.scan_thread.is_alive():
            self.root.after(100, self._process_queue)

    def _create_context_menu(self):
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="打开文件所在目录", command=self._open_file_location)

    def _show_context_menu(self, event):
        item_id = self.tree.identify_row(event.y)
        if item_id:
            item_values = self.tree.item(item_id, "values")
            if item_values and item_values[2] == '':
                self.tree.selection_set(item_id)
                self.context_menu.post(event.x_root, event.y_root)

    def _open_file_location(self):
        try:
            selected_item_id = self.tree.selection()[0]
            item_values = self.tree.item(selected_item_id, "values")
            file_path = item_values[0].strip()
            if os.path.isfile(file_path):
                directory = os.path.dirname(file_path)
                if sys.platform == "win32":
                    os.startfile(directory)
                elif sys.platform == "darwin":
                    subprocess.Popen(["open", directory])
                else:
                    subprocess.Popen(["xdg-open", directory])
        except (IndexError, OSError) as e:
            self.status_label.config(text=f"错误: {e}")

if __name__ == "__main__":
    # Use TkinterDnD.Tk() as the root window to enable drag-and-drop
    root = TkinterDnD.Tk()
    # Apply the ttkbootstrap theme to the DND-enabled root window
    style = ttk.Style(theme='litera')
    app = DuplicateFinderApp(root)
    root.mainloop()
