import requests
from bs4 import BeautifulSoup
import logging
from dataclasses import dataclass
from typing import List, Optional
from pathlib import Path
import json
from functools import lru_cache
import time
import os


@dataclass(frozen=True)
class IPData:
    """IP数据结构"""
    service_provider: str
    ip_address: str
    latency: str
    packet_loss: str
    speed: str

    def __str__(self) -> str:
        return (f"服务商：{self.service_provider}，IP：{self.ip_address}，"
                f"延迟：{self.latency}，丢包率：{self.packet_loss}，速度：{self.speed}")

    def to_dict(self) -> dict:
        return {k: v for k, v in self.__dict__.items()}


class CFIPFetcher:
    """CF优选IP获取器"""
    def __init__(self, url: str = 'https://cf.090227.xyz/'):
        self.url = url
        self.session = self._create_session()
        # 使用脚本所在目录
        self.script_dir = Path(__file__).parent.absolute()
        self.cache_file = self.script_dir / 'ip_cache.json'
        self.logger = self._setup_logger()

    def _setup_logger(self) -> logging.Logger:
        """配置日志"""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            handler.setFormatter(logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            ))
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _create_session(self) -> requests.Session:
        """创建请求会话"""
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/91.0.4472.124'
        })
        return session

    @lru_cache(maxsize=32)
    def _get_cached_data(self, cache_key: str) -> Optional[List[IPData]]:
        """获取缓存数据"""
        if not self.cache_file.exists():
            return None
        try:
            cache = json.loads(self.cache_file.read_text(encoding='utf-8'))
            data = cache.get(cache_key)
            if data and time.time() - data['timestamp'] < 3600:
                return [IPData(**ip) for ip in data['ips']]
        except Exception as e:
            self.logger.debug(f"读取缓存失败: {e}")
        return None

    def _save_cache(self, cache_key: str, data: List[IPData]) -> None:
        """保存缓存数据"""
        try:
            cache = {}
            if self.cache_file.exists():
                cache = json.loads(self.cache_file.read_text(encoding='utf-8'))
            cache[cache_key] = {
                'timestamp': time.time(),
                'ips': [ip.to_dict() for ip in data]
            }
            self.cache_file.write_text(
                json.dumps(cache, ensure_ascii=False, indent=2),
                encoding='utf-8'
            )
        except Exception as e:
            self.logger.debug(f"保存缓存失败: {e}")

    def fetch_and_parse(self) -> List[IPData]:
        """获取并解析IP数据"""
        # 尝试从缓存获取
        cached = self._get_cached_data(self.url)
        if cached:
            self.logger.info("使用缓存数据")
            return cached

        try:
            # 获取新数据
            response = self.session.get(self.url, timeout=10)
            response.raise_for_status()
            
            # 解析数据
            soup = BeautifulSoup(response.text, 'lxml')
            results = []
            
            for row in soup.select('div.centered table tr')[:11]:
                cells = row.find_all('td')
                if len(cells) >= 5:
                    ip_data = IPData(
                        service_provider=cells[0].get_text(strip=True),
                        ip_address=cells[1].get_text(strip=True),
                        latency=cells[2].get_text(strip=True),
                        packet_loss=cells[3].get_text(strip=True),
                        speed=cells[4].get_text(strip=True)
                    )
                    results.append(ip_data)

            if results:
                self._save_cache(self.url, results)
                self.logger.info(f"成功获取 {len(results)} 条IP数据")
            return results

        except Exception as e:
            self.logger.error(f"获取数据失败: {e}")
            return []

    def save_results(self, results: List[IPData], output_file: str) -> bool:
        """保存结果"""
        if not results:
            return False

        try:
            # 使用脚本所在目录
            output_path = self.script_dir / output_file
            output_path.write_text(
                json.dumps([ip.to_dict() for ip in results], 
                          ensure_ascii=False, indent=2),
                encoding='utf-8'
            )
            self.logger.info(f"结果已保存到: {output_path}")
            return True
        except Exception as e:
            self.logger.error(f"保存失败: {e}")
            return False

    def run(self, output_file: Optional[str] = None) -> bool:
        """运行获取和保存流程"""
        try:
            if output_file is None:
                output_file = 'results.json'

            results = self.fetch_and_parse()
            return bool(results) and self.save_results(results, output_file)
        finally:
            self.session.close()


def main():
    """主函数"""
    try:
        fetcher = CFIPFetcher()
        success = fetcher.run()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        exit(130)
    except Exception as e:
        print(f"程序执行出错: {e}")
        exit(1)


if __name__ == "__main__":
    main()
