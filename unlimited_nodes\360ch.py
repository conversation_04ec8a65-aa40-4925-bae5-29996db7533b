
import re
import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import sys
import traceback
import ctypes

def save_to_file(data_list):
    """保存URL列表到文件"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(script_dir, 'clash_links.txt')
    
    # 不进行过滤，直接保存所有结果
    clean_urls = []
    for item in data_list:
        clean_url = item.split('\n')[0].strip()
        clean_urls.append(clean_url)
    
    # 写入文件
    with open(file_path, 'w', encoding='utf-8') as f:
        for item in clean_urls:
            f.write(f"{item}\n")
    print(f"数据已保存到 {file_path}，共 {len(clean_urls)} 个URL")

def login(driver, max_retries=3):
    """处理登录流程"""
    for attempt in range(max_retries):
        try:
            print(f"开始登录流程... (尝试 {attempt + 1}/{max_retries})")
            
            # 访问登录页面
            driver.get("https://quake.360.net/quake/login#/")
            print("已进入登录页面")
            
            # 等待用户名输入框出现并确保可交互
            username_input = WebDriverWait(driver, 15).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, 'input[name="userName"][placeholder="手机号/用户名/邮箱"]'))
            )
            username_input.clear()
            username_input.send_keys("13558782739")
            print("已输入用户名")
            
            # 等待密码输入框可交互
            password_input = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, 'input[type="password"][name="password"]'))
            )
            password_input.clear()
            password_input.send_keys("o~tk!&7+p/D?NPyG")
            print("已输入密码")
            
            # 等待同意框可交互
            agree_checkbox = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, 'input[type="checkbox"][name="is_agree"]'))
            )
            if not agree_checkbox.is_selected():
                agree_checkbox.click()
            print("已勾选同意框")
            
            # 等待登录按钮可点击
            login_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '.quc-button-submit.quc-button.quc-button-primary'))
            )
            login_button.click()
            print("已点击登录按钮，等待验证完成...")
            
            # 等待页面跳转完成，使用更长的超时时间
            WebDriverWait(driver, 30).until(
                EC.all_of(
                    EC.url_changes("https://quake.360.net/quake/login#/"),
                    lambda driver: "login" not in driver.current_url
                )
            )
            
            # 等待新页面加载完成
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            print("登录成功，页面已完全加载")
            return True
            
        except Exception as e:
            print(f"登录尝试 {attempt + 1} 失败: {str(e)}")
            if attempt < max_retries - 1:
                print("等待5秒后重试...")
                time.sleep(5)
            else:
                print("已达到最大重试次数")
                return False
    
    return False

def find_ip_in_container(container):
    """从容器中提取IP地址"""
    selectors = [
        ".ip span.copy_btn",
        ".ip span[data-clipboard-text]",
        "span[data-clipboard-text]",
        ".ip"
    ]
    
    for selector in selectors:
        try:
            elements = container.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                ip_element = elements[0]
                ip_text = ip_element.get_attribute("data-clipboard-text") or ip_element.text
                ip_text = ip_text.strip()
                
                if ip_text:
                    # 如果IP包含端口，去除端口部分
                    if ':' in ip_text:
                        ip_text = ip_text.split(':')[0].strip()
                    return ip_text
        except:
            continue
    
    return None

def find_path_in_container(container):
    """从容器中提取路径"""
    # 方法1: 查找包含"网站路径"文本的标签后面的span
    try:
        path_labels = container.find_elements(By.XPATH, ".//span[contains(text(), '网站路径')]")
        if path_labels:
            for label in path_labels:
                try:
                    next_span = label.find_element(By.XPATH, "following-sibling::span")
                    if next_span:
                        path_text = next_span.text.strip()
                        if path_text:
                            return path_text
                except:
                    pass
                
                try:
                    parent = label.find_element(By.XPATH, "..")
                    spans = parent.find_elements(By.TAG_NAME, "span")
                    for i, span in enumerate(spans):
                        if span == label and i+1 < len(spans):
                            path_text = spans[i+1].text.strip()
                            if path_text:
                                return path_text
                except:
                    pass
    except:
        pass
    
    # 方法2: 查找class为ellipse-text的span
    try:
        ellipse_spans = container.find_elements(By.CSS_SELECTOR, "span.ellipse-text")
        for span in ellipse_spans:
            text = span.text.strip()
            if '/sub/' in text:
                return text
    except:
        pass
    
    # 方法3: 查找所有span，寻找包含/sub/的文本
    try:
        all_spans = container.find_elements(By.TAG_NAME, "span")
        for span in all_spans:
            try:
                text = span.text.strip()
                if '/sub/' in text:
                    path_match = re.search(r'(/sub/[^\s]*)', text)
                    if path_match:
                        return path_match.group(1)
            except:
                continue
    except:
        pass
    
    # 方法4: 在整个容器的文本中查找/sub/路径
    try:
        container_text = container.text
        path_matches = re.findall(r'(/sub/[^\s\n]*)', container_text)
        if path_matches:
            return path_matches[0]
    except:
        pass
    
    return None

def extract_urls_from_page(driver, max_retries=3):
    """从页面提取URL"""
    results = []
    
    for attempt in range(max_retries):
        try:
            # 等待结果加载完成
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.item-container"))
            )
            
            # 查找所有item-container元素
            containers = driver.find_elements(By.CSS_SELECTOR, "div.item-container")
            print(f"找到 {len(containers)} 个结果容器")
            
            if not containers:
                raise Exception("未找到任何结果容器")
            
            for container in containers:
                try:
                    # 等待容器变为可交互
                    WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, "div.item-container"))
                    )
                    
                    # 提取IP
                    ip_text = find_ip_in_container(container)
                    if not ip_text:
                        print("未找到IP元素，跳过此容器")
                        continue
                    
                    print(f"找到IP: {ip_text}")
                    
                    # 提取路径
                    path_text = find_path_in_container(container)
                    
                    # 构建URL
                    if path_text:
                        # 确保路径以 / 开头
                        if not path_text.startswith('/'):
                            path_text = '/' + path_text
                        
                        url = f"https://{ip_text}{path_text}"
                        results.append(url)
                        print(f"找到完整链接: {url}")
                    else:
                        # 如果没有找到路径，使用默认的 /sub 路径
                        url = f"https://{ip_text}/sub"
                        results.append(url)
                        print(f"使用默认路径: {url}")
                except Exception as e:
                    print(f"处理容器时出错: {str(e)}")
                    continue
            
            # 如果成功提取了URL，就跳出重试循环
            if results:
                break
            
        except Exception as e:
            print(f"提取URL尝试 {attempt + 1} 失败: {str(e)}")
            if attempt < max_retries - 1:
                print("等待5秒后重试...")
                time.sleep(5)
            else:
                print("已达到最大重试次数")
    
    return results

def main():
    """主函数"""
    try:
        print("开始初始化Chrome浏览器...")
        
        # 设置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument('--start-maximized')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 创建Chrome浏览器实例
        print("正在启动浏览器...")
        driver = webdriver.Chrome(options=chrome_options)
        
        try:
            # 登录
            if not login(driver):
                print("登录失败，程序退出")
                return
            
            # 访问搜索页面并等待加载完成
            print("正在访问搜索页面...")
            driver.get("https://quake.360.net/quake/#/index")
            
            # 等待页面完全加载
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 使用多个选择器尝试查找输入框
            selectors = [
                "//textarea[contains(@placeholder, 'port:')]",
                "textarea.main-screen-top-search-input",
                "//textarea[contains(@class, 'main-screen-top-search-input')]"
            ]
            
            input_element = None
            for selector in selectors:
                try:
                    print(f"尝试使用选择器查找输入框: {selector}")
                    input_element = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH if selector.startswith("//") else By.CSS_SELECTOR, selector))
                    )
                    if input_element:
                        break
                except:
                    continue
            
            if not input_element:
                raise Exception("无法找到搜索输入框")
            
            # 输入搜索文本
            input_element.clear()
            input_element.send_keys(search_text)
            print(f"已输入搜索文本: {search_text}")
            
            # 等待输入完成
            WebDriverWait(driver, 10).until(
                lambda d: input_element.get_attribute('value') == search_text
            )
            
            # 查找并点击搜索按钮
            button_selectors = [
                "span.append-btn.append-result-btn",
                "//span[contains(@class, 'append-btn') and contains(text(), '检索')]",
                "//*[contains(text(), '检索')]"
            ]
            
            search_button = None
            for selector in button_selectors:
                try:
                    print(f"尝试使用选择器查找搜索按钮: {selector}")
                    search_button = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR if not selector.startswith("//") else By.XPATH, selector))
                    )
                    if search_button:
                        break
                except:
                    continue
            
            if not search_button:
                raise Exception("无法找到搜索按钮")
            
            # 点击搜索按钮并等待结果加载
            search_button.click()
            print("已点击搜索按钮，等待结果加载...")
            
            # 等待搜索结果出现
            WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.item-container"))
            )
            
            # 提取URL
            results = extract_urls_from_page(driver)
            
            # 保存结果
            if results:
                save_to_file(results)
                print(f"共找到 {len(results)} 个结果")
            else:
                print("未找到任何结果")
            
            # 保持浏览器打开状态
            print("任务完成，保持浏览器打开状态。按Ctrl+C可终止程序。")
            while True:
                time.sleep(1)
                
        except Exception as e:
            print(f"操作过程中发生错误: {str(e)}")
            import traceback
            print(traceback.format_exc())
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"浏览器操作发生错误: {str(e)}")
    finally:
        print("保持浏览器打开状态，请手动关闭或按Ctrl+C终止程序")

if __name__ == "__main__":
    search_text = 'response:"subscription-userinfo:" AND response:"download=0" AND http_path: "/subs/clash"'
    main()  # 修改这里，调用main()函数而不是find_port_input()函数
