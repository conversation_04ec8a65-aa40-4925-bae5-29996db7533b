"""
配置管理模块
统一管理应用程序的所有配置常量
"""

from typing import Dict, Any


class AppConfig:
    """应用程序配置类"""
    
    # ==================== 网络配置 ====================
    # API相关配置
    API_BASE_URL = "https://av-wiki.net"
    API_SEARCH_URL = "https://av-wiki.net/?s={}&post_type=product"
    
    # 网络超时配置
    TIMEOUT_CONNECT = 5  # 连接超时(秒)
    TIMEOUT_READ = 15    # 读取超时(秒)
    
    # 重试配置
    MAX_RETRIES = 3      # 最大重试次数
    RETRY_DELAY = 1      # 重试延迟(秒)
    TIMEOUT_RETRY_DELAY = 2  # 超时重试延迟(秒)
    
    # 请求头配置
    DEFAULT_HEADERS = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Referer': 'https://av-wiki.net'
    }
    
    # ==================== UI配置 ====================
    # 窗口配置
    WINDOW_WIDTH = 500
    WINDOW_HEIGHT = 400
    WINDOW_TITLE = "演员名称查询"
    
    # 图标配置
    ICON_SIZE = 32
    
    # 组件尺寸配置
    INPUT_HEIGHT = 35
    INPUT_LABEL_WIDTH = 40
    BUTTON_WIDTH = 80
    BUTTON_HEIGHT_NORMAL = 35
    BUTTON_HEIGHT_SMALL = 30
    PROGRESS_BAR_HEIGHT = 3
    RESULT_TEXT_HEIGHT = 80
    
    # 布局配置
    MAIN_LAYOUT_SPACING = 15
    MAIN_LAYOUT_MARGINS = (25, 25, 25, 25)
    INPUT_LAYOUT_MARGINS = (15, 15, 15, 15)
    INPUT_LAYOUT_SPACING = 10
    INPUT_HEADER_SPACING = 8
    RESULT_LAYOUT_MARGINS = (15, 12, 15, 12)
    RESULT_LAYOUT_SPACING = 8
    BUTTONS_LAYOUT_SPACING = 8
    
    # ==================== 颜色配置 ====================
    # 主色调
    PRIMARY_COLOR = "#4CAF50"
    PRIMARY_COLOR_HOVER = "#45a049"
    PRIMARY_COLOR_PRESSED = "#3d8b40"
    
    # 背景色
    BACKGROUND_COLOR = "#f5f5f5"
    CONTAINER_BACKGROUND = "white"
    RESULT_BACKGROUND = "#fafafa"
    
    # 文字颜色
    TEXT_COLOR_PRIMARY = "#2c3e50"
    TEXT_COLOR_SECONDARY = "#333"
    TEXT_COLOR_MUTED = "#666"
    TEXT_COLOR_DISABLED = "#666666"
    
    # 边框颜色
    BORDER_COLOR = "#ddd"
    BORDER_COLOR_DISABLED = "#cccccc"
    
    # ==================== 字体配置 ====================
    FONT_FAMILY = "Microsoft YaHei UI"
    FONT_SIZE_NORMAL = 12
    FONT_SIZE_TITLE = 18
    
    # ==================== 动画配置 ====================
    ANIMATION_DURATION = 100  # 毫秒
    
    # ==================== 进度配置 ====================
    PROGRESS_INITIAL = 10
    PROGRESS_SEARCHING = 30
    PROGRESS_PARSING = 70
    PROGRESS_COMPLETE = 100
    PROGRESS_RESET = 0
    PROGRESS_RETRY_STEP = 20
    
    # ==================== 延迟配置 ====================
    STATUS_DISPLAY_DELAY = 0.5  # 状态显示延迟(秒)
    TOOLTIP_CLEAR_DELAY = 2000   # 提示清除延迟(毫秒)
    
    # ==================== 文件配置 ====================
    HISTORY_FILENAME = "search_history.json"
    HISTORY_MAX_SIZE = 1000  # 历史记录最大条数
    JSON_INDENT = 2
    
    # ==================== 样式相关配置 ====================
    # 边框圆角
    BORDER_RADIUS_SMALL = 3
    BORDER_RADIUS_NORMAL = 4
    BORDER_RADIUS_LARGE = 5
    BORDER_RADIUS_CONTAINER = 8
    
    # 内边距
    PADDING_SMALL = 5
    PADDING_NORMAL = 8
    PADDING_LEFT_RIGHT = 2
    
    # 边框宽度
    BORDER_WIDTH_THIN = 1
    BORDER_WIDTH_NORMAL = 2
    
    # ==================== 消息配置 ====================
    # 状态消息
    MSG_SEARCHING = "正在查询中..."
    MSG_FROM_HISTORY = "从历史记录中获取..."
    MSG_COPIED = "已复制到剪贴板！"
    
    # 错误消息
    MSG_NO_INPUT = "请输入番号！"
    MSG_NO_COPY_CONTENT = "没有可复制的内容！"
    MSG_NOT_FOUND = "未找到相关女优信息，请检查番号是否正确"
    MSG_SERVER_ERROR = "服务器暂时无法访问，请稍后重试"
    MSG_NETWORK_ERROR = "网络请求失败，请检查网络连接 (错误代码: {})"
    MSG_TIMEOUT_ERROR = "网络连接超时，请检查网络状况后重试"
    MSG_CONNECTION_ERROR = "无法连接到服务器，请检查网络连接"
    MSG_UNKNOWN_ERROR = "查询过程中发生未知错误，请稍后重试"
    
    # 重试消息
    MSG_RETRY_TEMPLATE = "重试第 {} 次..."
    MSG_TIMEOUT_RETRY_TEMPLATE = "网络超时，重试第 {} 次..."
    MSG_CONNECTION_RETRY_TEMPLATE = "网络连接失败，重试第 {} 次..."
    MSG_ERROR_RETRY_TEMPLATE = "发生错误，重试第 {} 次..."
    
    # ==================== 选择器配置 ====================
    ACTRESS_LINK_SELECTOR = '/av-actress/'
    INVALID_ACTRESS_NAMES = ["***"]
    MIN_ACTRESS_NAME_LENGTH = 1
    
    # ==================== HTTP状态码配置 ====================
    HTTP_OK = 200
    HTTP_NOT_FOUND = 404
    HTTP_SERVER_ERROR = 500


class ConfigManager:
    """配置管理器"""
    
    @staticmethod
    def get_timeout_tuple() -> tuple:
        """获取超时配置元组"""
        return (AppConfig.TIMEOUT_CONNECT, AppConfig.TIMEOUT_READ)
    
    @staticmethod
    def get_window_size() -> tuple:
        """获取窗口尺寸"""
        return (AppConfig.WINDOW_WIDTH, AppConfig.WINDOW_HEIGHT)
    
    @staticmethod
    def get_button_size_normal() -> tuple:
        """获取普通按钮尺寸"""
        return (AppConfig.BUTTON_WIDTH, AppConfig.BUTTON_HEIGHT_NORMAL)
    
    @staticmethod
    def get_button_size_small() -> tuple:
        """获取小按钮尺寸"""
        return (AppConfig.BUTTON_WIDTH, AppConfig.BUTTON_HEIGHT_SMALL)
    
    @staticmethod
    def get_icon_size() -> tuple:
        """获取图标尺寸"""
        return (AppConfig.ICON_SIZE, AppConfig.ICON_SIZE)
    
    @staticmethod
    def get_main_layout_margins() -> tuple:
        """获取主布局边距"""
        return AppConfig.MAIN_LAYOUT_MARGINS
    
    @staticmethod
    def get_input_layout_margins() -> tuple:
        """获取输入布局边距"""
        return AppConfig.INPUT_LAYOUT_MARGINS
    
    @staticmethod
    def get_result_layout_margins() -> tuple:
        """获取结果布局边距"""
        return AppConfig.RESULT_LAYOUT_MARGINS
    
    @staticmethod
    def format_retry_message(retry_count: int, msg_type: str = "normal") -> str:
        """格式化重试消息"""
        templates = {
            "normal": AppConfig.MSG_RETRY_TEMPLATE,
            "timeout": AppConfig.MSG_TIMEOUT_RETRY_TEMPLATE,
            "connection": AppConfig.MSG_CONNECTION_RETRY_TEMPLATE,
            "error": AppConfig.MSG_ERROR_RETRY_TEMPLATE
        }
        return templates.get(msg_type, AppConfig.MSG_RETRY_TEMPLATE).format(retry_count)
    
    @staticmethod
    def format_network_error(status_code: int) -> str:
        """格式化网络错误消息"""
        return AppConfig.MSG_NETWORK_ERROR.format(status_code)


# 导出配置实例
config = AppConfig()
config_manager = ConfigManager()
