<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打地鼠游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .game-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            text-align: center;
            max-width: 600px;
            width: 90%;
        }

        .game-header {
            margin-bottom: 30px;
        }

        .game-title {
            font-size: 2.5em;
            color: #333;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .game-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
        }

        .stat {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            padding: 15px 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            min-width: 120px;
        }

        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #333;
        }

        .game-board {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #a8edea, #fed6e3);
            border-radius: 20px;
            box-shadow: inset 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .hole {
            width: 120px;
            height: 120px;
            background: #8b4513;
            border-radius: 50%;
            position: relative;
            cursor: pointer;
            box-shadow: inset 0 5px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.2s ease;
            overflow: hidden;
        }

        .hole::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            background: #654321;
            border-radius: 50%;
            box-shadow: inset 0 3px 8px rgba(0, 0, 0, 0.4);
        }

        .mole {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #8B4513, #D2691E);
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, 150%);
            transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            cursor: pointer;
            z-index: 10;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .mole::before {
            content: '🐹';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 2.5em;
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
        }

        .mole.up {
            transform: translate(-50%, -50%);
        }

        .mole.hit {
            animation: hit 0.5s ease-out;
        }

        @keyframes hit {
            0% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.3) rotate(10deg); }
            100% { transform: translate(-50%, 150%) scale(0.8) rotate(-10deg); }
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        .btn {
            padding: 15px 30px;
            font-size: 1.1em;
            font-weight: bold;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-start {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-start:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
            box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
        }

        .btn-reset {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            color: #333;
        }

        .btn-reset:hover {
            background: linear-gradient(135deg, #ff8a8e, #fdbfdf);
            transform: translateY(-2px);
            box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .game-over {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .game-over-content {
            background: white;
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            animation: popup 0.5s ease-out;
        }

        @keyframes popup {
            0% { transform: scale(0.5); opacity: 0; }
            100% { transform: scale(1); opacity: 1; }
        }

        .game-over h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2em;
        }

        .final-score {
            font-size: 1.5em;
            color: #667eea;
            margin-bottom: 30px;
            font-weight: bold;
        }

        .hit-effect {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ff6b6b;
            font-size: 2em;
            font-weight: bold;
            pointer-events: none;
            animation: scoreUp 1s ease-out forwards;
            z-index: 100;
        }

        @keyframes scoreUp {
            0% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
            100% {
                opacity: 0;
                transform: translate(-50%, -150%) scale(1.5);
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1 class="game-title">🔨 打地鼠 🔨</h1>
            <div class="game-stats">
                <div class="stat">
                    <div class="stat-label">得分</div>
                    <div class="stat-value" id="score">0</div>
                </div>
                <div class="stat">
                    <div class="stat-label">时间</div>
                    <div class="stat-value" id="time">30</div>
                </div>
                <div class="stat">
                    <div class="stat-label">命中率</div>
                    <div class="stat-value" id="accuracy">100%</div>
                </div>
            </div>
        </div>

        <div class="game-board" id="gameBoard">
            <div class="hole" data-index="0">
                <div class="mole"></div>
            </div>
            <div class="hole" data-index="1">
                <div class="mole"></div>
            </div>
            <div class="hole" data-index="2">
                <div class="mole"></div>
            </div>
            <div class="hole" data-index="3">
                <div class="mole"></div>
            </div>
            <div class="hole" data-index="4">
                <div class="mole"></div>
            </div>
            <div class="hole" data-index="5">
                <div class="mole"></div>
            </div>
            <div class="hole" data-index="6">
                <div class="mole"></div>
            </div>
            <div class="hole" data-index="7">
                <div class="mole"></div>
            </div>
            <div class="hole" data-index="8">
                <div class="mole"></div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-start" id="startBtn">开始游戏</button>
            <button class="btn btn-reset" id="resetBtn">重置游戏</button>
        </div>
    </div>

    <div class="game-over" id="gameOver">
        <div class="game-over-content">
            <h2>🎉 游戏结束! 🎉</h2>
            <div class="final-score" id="finalScore">最终得分: 0</div>
            <div class="final-accuracy" id="finalAccuracy">命中率: 100%</div>
            <button class="btn btn-start" onclick="restartGame()">再来一局</button>
        </div>
    </div>

    <script>
        class WhackAMoleGame {
            constructor() {
                this.score = 0;
                this.timeLeft = 30;
                this.gameActive = false;
                this.moleTimeout = null;
                this.gameTimer = null;
                this.currentMole = null;
                this.hits = 0;
                this.misses = 0;
                this.moleSpeed = 1500;
                
                this.initElements();
                this.bindEvents();
            }

            initElements() {
                this.scoreEl = document.getElementById('score');
                this.timeEl = document.getElementById('time');
                this.accuracyEl = document.getElementById('accuracy');
                this.startBtn = document.getElementById('startBtn');
                this.resetBtn = document.getElementById('resetBtn');
                this.gameBoard = document.getElementById('gameBoard');
                this.gameOver = document.getElementById('gameOver');
                this.finalScore = document.getElementById('finalScore');
                this.finalAccuracy = document.getElementById('finalAccuracy');
                this.holes = document.querySelectorAll('.hole');
                this.moles = document.querySelectorAll('.mole');
            }

            bindEvents() {
                this.startBtn.addEventListener('click', () => this.startGame());
                this.resetBtn.addEventListener('click', () => this.resetGame());
                
                this.moles.forEach((mole, index) => {
                    mole.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.hitMole(index);
                    });
                });

                this.holes.forEach((hole, index) => {
                    hole.addEventListener('click', () => {
                        if (this.gameActive) {
                            this.miss();
                        }
                    });
                });
            }

            startGame() {
                this.gameActive = true;
                this.startBtn.disabled = true;
                this.startBtn.textContent = '游戏中...';
                
                this.gameTimer = setInterval(() => {
                    this.timeLeft--;
                    this.timeEl.textContent = this.timeLeft;
                    
                    if (this.timeLeft <= 0) {
                        this.endGame();
                    }
                }, 1000);

                this.showRandomMole();
            }

            showRandomMole() {
                if (!this.gameActive) return;

                this.hideCurrentMole();
                
                const randomIndex = Math.floor(Math.random() * 9);
                const mole = this.moles[randomIndex];
                
                mole.classList.add('up');
                this.currentMole = randomIndex;
                
                // 调整地鼠出现的持续时间，随游戏进行逐渐加快
                const duration = Math.max(800, this.moleSpeed - (this.score * 50));
                
                this.moleTimeout = setTimeout(() => {
                    this.hideCurrentMole();
                    this.showRandomMole();
                }, duration);
            }

            hideCurrentMole() {
                this.moles.forEach(mole => {
                    mole.classList.remove('up');
                });
                this.currentMole = null;
            }

            hitMole(index) {
                if (!this.gameActive || this.currentMole !== index) return;
                
                const mole = this.moles[index];
                const hole = this.holes[index];
                
                mole.classList.add('hit');
                mole.classList.remove('up');
                
                this.score += 10;
                this.hits++;
                this.scoreEl.textContent = this.score;
                this.updateAccuracy();
                
                // 显示得分效果
                this.showHitEffect(hole, '+10');
                
                clearTimeout(this.moleTimeout);
                this.currentMole = null;
                
                setTimeout(() => {
                    mole.classList.remove('hit');
                    this.showRandomMole();
                }, 500);
            }

            miss() {
                this.misses++;
                this.updateAccuracy();
            }

            updateAccuracy() {
                const total = this.hits + this.misses;
                const accuracy = total === 0 ? 100 : Math.round((this.hits / total) * 100);
                this.accuracyEl.textContent = accuracy + '%';
            }

            showHitEffect(hole, text) {
                const effect = document.createElement('div');
                effect.className = 'hit-effect';
                effect.textContent = text;
                hole.appendChild(effect);
                
                setTimeout(() => {
                    hole.removeChild(effect);
                }, 1000);
            }

            endGame() {
                this.gameActive = false;
                clearTimeout(this.moleTimeout);
                clearInterval(this.gameTimer);
                
                this.hideCurrentMole();
                
                this.finalScore.textContent = `最终得分: ${this.score}`;
                this.finalAccuracy.textContent = `命中率: ${this.accuracyEl.textContent}`;
                this.gameOver.style.display = 'flex';
                
                this.startBtn.disabled = false;
                this.startBtn.textContent = '开始游戏';
            }

            resetGame() {
                this.gameActive = false;
                clearTimeout(this.moleTimeout);
                clearInterval(this.gameTimer);
                
                this.score = 0;
                this.timeLeft = 30;
                this.hits = 0;
                this.misses = 0;
                this.currentMole = null;
                
                this.scoreEl.textContent = '0';
                this.timeEl.textContent = '30';
                this.accuracyEl.textContent = '100%';
                
                this.hideCurrentMole();
                this.gameOver.style.display = 'none';
                
                this.startBtn.disabled = false;
                this.startBtn.textContent = '开始游戏';
            }
        }

        // 全局重启游戏函数
        function restartGame() {
            game.resetGame();
        }

        // 初始化游戏
        const game = new WhackAMoleGame();
    </script>
</body>
</html>