import os
import re
import sys

# 检查操作系统
if not os.name == 'nt':
    print("Error: This script only supports Windows")
    sys.exit(1)

# 检查Trae安装
trae_dir = os.path.join(os.getenv('APPDATA'), 'Trae')
if not os.path.exists(trae_dir):
    print("Error: Trae installation not found")
    sys.exit(1)

# 找到最新的日志目录
log_dirs = [d for d in os.listdir(os.path.join(trae_dir, 'logs')) if os.path.isdir(os.path.join(trae_dir, 'logs', d))]
if not log_dirs:
    print("Error: No log directories found")
    sys.exit(1)

log_dirs.sort()
newest_log_dir = os.path.join(trae_dir, 'logs', log_dirs[-1])
main_log = os.path.join(newest_log_dir, 'main.log')
if not os.path.exists(main_log):
    print(f"Error: main.log not found in the newest log directory: {log_dirs[-1]}")
    sys.exit(1)

# 直接读取文件内容
with open(main_log, 'r', encoding='utf-8') as file:
    content = file.read()

# 使用更直接的方法提取 ClientID
client_id_match = re.search(r'"userJwt":"{\\"ClientID\\":\\"([^\\"]*)\\"', content)
if client_id_match:
    client_id = client_id_match.group(1)
else:
    print(f"Error: Could not find ClientID in main.log in directory: {log_dirs[-1]}")
    sys.exit(1)

# 寻找最新的Modular子目录来寻找ai_1_stdout.log
modulars_path = os.path.join(newest_log_dir, 'Modular')
if not os.path.exists(modulars_path):
    print(f"Error: Modular directory not found in the latest log directory: {log_dirs[-1]}")
    sys.exit(1)

app_log = os.path.join(modulars_path, 'ai_1_stdout.log')
if not os.path.exists(app_log):
    print(f"Error: ai_1_stdout.log not found in the latest log directory: {log_dirs[-1]}")
    sys.exit(1)

# 提取AppID
with open(app_log, 'r', encoding='utf-8') as file:
    app_content = file.read()

app_id_match = re.search(r'"x-app-id": "([^"]*)"', app_content)
if app_id_match:
    app_id = app_id_match.group(1)
else:
    print("Error: Could not find APP_ID in ai_1_stdout.log")
    sys.exit(1)

# 提取认证信息
storage_file = os.path.join(os.getenv('APPDATA'), 'Trae', 'User', 'globalStorage', 'storage.json')
if not os.path.exists(storage_file):
    print("Error: storage.json file not found")
    sys.exit(1)

with open(storage_file, 'r', encoding='utf-8') as file:
    storage_content = file.read()

auth_match = re.search(r'"iCubeAuthInfo://icube\.cloudide": "({.*?})"', storage_content)
if auth_match:
    json_str = auth_match.group(1).replace('\\"', '"')
    refresh_token_match = re.search(r'refreshToken":"(.*?)",".*?userId":"(\d+)"', json_str)

    if refresh_token_match:
        user_id = refresh_token_match.group(2)
        refresh_token = refresh_token_match.group(1)

        # 生成docker run命令
        docker_command = (
            f'docker run -d '
            f'--name trae2api '
            f'-p 17080:17080 '
            f'-e APP_ID="{app_id}" '
            f'-e CLIENT_ID="{client_id}" '
            f'-e REFRESH_TOKEN="{refresh_token}" '
            f'-e USER_ID="{user_id}" '
            f'-e AUTH_TOKEN="" '
            f'--restart always '
            f'linqiu1199/trae2api:v1.0.6'
        )

        print(docker_command)
    else:
        print("Error: Could not extract refresh token and user ID from storage.json")
        sys.exit(1)
else:
    print("Error: Could not find authentication information in storage.json")
    sys.exit(1)
