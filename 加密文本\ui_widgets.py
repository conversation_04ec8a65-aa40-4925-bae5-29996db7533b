import customtkinter as ctk
from ui_texts import TEXTS
from ui_logic import process_text, copy_result, paste_text, clear_text, clear_input_and_output, clear_all, toggle_theme

# UI常量
DEFAULT_BUTTON_WIDTH = 130
DEFAULT_BUTTON_HEIGHT = 38
DEFAULT_CORNER_RADIUS = 8
INPUT_OUTPUT_HEIGHT = 200
KEY_ENTRY_HEIGHT = 35
CONTROL_FRAME_HEIGHT = 80
STATUS_FRAME_HEIGHT = 40

def create_button(app, parent, text, command, **kwargs):
    font_families = ["Segoe UI Emoji", "Noto Color Emoji", "Apple Color Emoji"]
    font_size = kwargs.pop('font_size', 13)
    font = None
    for fam in font_families:
        try:
            font = ctk.CTkFont(family=fam, size=font_size, weight="bold")
            break
        except:
            continue
    if font is None:
        font = ctk.CTkFont(size=font_size, weight="bold")
    fg_color = kwargs.get('fg_color', None)
    if fg_color == "transparent":
        mode = ctk.get_appearance_mode()
        kwargs['text_color'] = "#222222" if mode == "Light" else "#f0f0f0"
        kwargs['border_color'] = "#888888"
        if 'width' not in kwargs:
            kwargs['width'] = DEFAULT_BUTTON_WIDTH
    else:
        if 'width' not in kwargs:
            kwargs['width'] = DEFAULT_BUTTON_WIDTH
    button_kwargs = {
        'text': text,
        'command': None,
        'height': DEFAULT_BUTTON_HEIGHT,
        'font': font,
        'corner_radius': DEFAULT_CORNER_RADIUS,
        'anchor': 'center',
        **kwargs
    }
    btn = ctk.CTkButton(parent, **button_kwargs)
    if fg_color == "transparent":
        app.transparent_buttons.append(btn)
    def animated_command(*args, _cmd=command, _btn=btn):
        orig_width = _btn.cget('width')
        orig_fg = _btn.cget('fg_color')
        try:
            _btn.configure(width=int(orig_width*0.92))
            _btn.configure(fg_color="#2980b9")
            _btn.after(100, lambda: _btn.configure(width=orig_width, fg_color=orig_fg))
        except:
            pass
        _cmd()
    btn.configure(command=animated_command)
    return btn

def create_input_section(app, parent):
    """创建输入区域"""
    # 输入区域框架
    input_frame = ctk.CTkFrame(parent, corner_radius=12)
    input_frame.pack(fill="both", expand=True, padx=20, pady=(20, 10))

    # 输入标签
    input_label = ctk.CTkLabel(
        input_frame,
        text=TEXTS['input_label'],
        font=ctk.CTkFont(size=16, weight="bold")
    )
    input_label.pack(anchor="w", padx=20, pady=(15, 5))

    # 输入文本框
    app.input_text = ctk.CTkTextbox(
        input_frame,
        height=INPUT_OUTPUT_HEIGHT,
        font=ctk.CTkFont(size=14),
        corner_radius=DEFAULT_CORNER_RADIUS,
        border_width=2
    )
    app.input_text.pack(fill="both", expand=True, padx=20, pady=(0, 10))

    # 密钥区域
    key_frame = ctk.CTkFrame(input_frame, fg_color="transparent")
    key_frame.pack(fill="x", padx=20, pady=(0, 15))

    # 密钥标签
    key_label = ctk.CTkLabel(
        key_frame,
        text=TEXTS['key_label'],
        font=ctk.CTkFont(size=14, weight="bold")
    )
    key_label.pack(side="left", padx=(0, 10))

    # 密钥输入框
    app.key_entry = ctk.CTkEntry(
        key_frame,
        placeholder_text="请输入密钥",
        font=ctk.CTkFont(size=14),
        height=KEY_ENTRY_HEIGHT,
        show="*"
    )
    app.key_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))

    # 显示/隐藏密钥按钮
    app.toggle_key_btn = create_button(
        app, key_frame, TEXTS['show_key'],
        lambda: toggle_key_visibility(app),
        width=50, height=KEY_ENTRY_HEIGHT, fg_color="transparent"
    )
    app.toggle_key_btn.pack(side="right")

    return input_frame

def create_output_section(app, parent):
    """创建输出区域"""
    # 输出区域框架
    output_frame = ctk.CTkFrame(parent, corner_radius=12)
    output_frame.pack(fill="both", expand=True, padx=20, pady=(10, 20))

    # 输出标签
    output_label = ctk.CTkLabel(
        output_frame,
        text=TEXTS['output_label'],
        font=ctk.CTkFont(size=16, weight="bold")
    )
    output_label.pack(anchor="w", padx=20, pady=(15, 5))

    # 输出文本框
    app.output_text = ctk.CTkTextbox(
        output_frame,
        height=INPUT_OUTPUT_HEIGHT,
        font=ctk.CTkFont(size=14),
        corner_radius=DEFAULT_CORNER_RADIUS,
        border_width=2,
        state="disabled"
    )
    app.output_text.pack(fill="both", expand=True, padx=20, pady=(0, 15))

    return output_frame

def create_control_section(app, parent):
    """创建控制按钮区域"""
    # 控制区域框架
    control_frame = ctk.CTkFrame(parent, corner_radius=12, height=CONTROL_FRAME_HEIGHT)
    control_frame.pack(fill="x", padx=20, pady=(0, 10))
    control_frame.pack_propagate(False)

    # 按钮容器
    button_container = ctk.CTkFrame(control_frame, fg_color="transparent")
    button_container.pack(expand=True, fill="both", padx=20, pady=15)

    # 主要操作按钮
    main_buttons_frame = ctk.CTkFrame(button_container, fg_color="transparent")
    main_buttons_frame.pack(side="left", fill="y")

    # 加密按钮
    encrypt_btn = create_button(
        app, main_buttons_frame, TEXTS['encrypt'],
        lambda: process_text(app, 'encrypt'),
        fg_color="#2ecc71", hover_color="#27ae60"
    )
    encrypt_btn.pack(side="left", padx=(0, 10))

    # 解密按钮
    decrypt_btn = create_button(
        app, main_buttons_frame, TEXTS['decrypt'],
        lambda: process_text(app, 'decrypt'),
        fg_color="#e74c3c", hover_color="#c0392b"
    )
    decrypt_btn.pack(side="left", padx=(0, 20))

    # 辅助操作按钮
    aux_buttons_frame = ctk.CTkFrame(button_container, fg_color="transparent")
    aux_buttons_frame.pack(side="left", fill="y")

    # 复制按钮
    copy_btn = create_button(
        app, aux_buttons_frame, TEXTS['copy'],
        lambda: copy_result(app),
        fg_color="transparent"
    )
    copy_btn.pack(side="left", padx=(0, 5))

    # 粘贴按钮
    paste_btn = create_button(
        app, aux_buttons_frame, TEXTS['paste'],
        lambda: paste_text(app),
        fg_color="transparent"
    )
    paste_btn.pack(side="left", padx=(0, 5))

    # 清空按钮（清空输入和输出）
    clear_btn = create_button(
        app, aux_buttons_frame, TEXTS['clear'],
        lambda: clear_input_and_output(app),
        fg_color="transparent"
    )
    clear_btn.pack(side="left", padx=(0, 5))

    # 全部清空按钮
    clear_all_btn = create_button(
        app, aux_buttons_frame, TEXTS['clear_all'],
        lambda: clear_all(app),
        fg_color="transparent"
    )
    clear_all_btn.pack(side="left", padx=(0, 5))

    # 切换主题按钮
    theme_btn = create_button(
        app, aux_buttons_frame, TEXTS['toggle_theme'],
        lambda: toggle_theme(app),
        fg_color="transparent"
    )
    theme_btn.pack(side="left")

    return control_frame

def create_status_bar(app, parent):
    """创建状态栏"""
    # 状态栏框架
    status_frame = ctk.CTkFrame(parent, corner_radius=DEFAULT_CORNER_RADIUS, height=STATUS_FRAME_HEIGHT)
    status_frame.pack(fill="x", padx=20, pady=(0, 20))
    status_frame.pack_propagate(False)

    # 状态标签
    app.status_label = ctk.CTkLabel(
        status_frame,
        text=TEXTS['ready'],
        font=ctk.CTkFont(size=13),
        anchor="w"
    )
    app.status_label.pack(side="left", padx=15, pady=8, fill="x", expand=True)

    # 添加状态更新方法到app
    app._loading = False

    def update_status(text, color="#ffffff", error=False, loading=False):
        app.status_label.configure(text=text, text_color=color)
        app._loading = loading
        if error:
            app.root.after(3000, lambda: app.status_label.configure(
                text=TEXTS['ready'], text_color="#ffffff"
            ))

    app.update_status = update_status

    return status_frame

def toggle_key_visibility(app):
    """切换密钥显示/隐藏"""
    if app.is_key_hidden:
        app.key_entry.configure(show="")
        app.toggle_key_btn.configure(text=TEXTS['hide_key'])
        app.is_key_hidden = False
    else:
        app.key_entry.configure(show="*")
        app.toggle_key_btn.configure(text=TEXTS['show_key'])
        app.is_key_hidden = True