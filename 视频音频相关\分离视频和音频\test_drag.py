#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

def test_file_validation(file_path):
    """测试文件验证功能"""
    print(f"测试文件路径: {repr(file_path)}")
    print(f"文件是否存在: {os.path.exists(file_path)}")
    
    if os.path.exists(file_path):
        file_ext = os.path.splitext(file_path)[1].lower()
        print(f"文件扩展名: {repr(file_ext)}")
        valid_extensions = ['.mp4', '.avi', '.mkv', '.mov', '.flv', '.wmv', '.webm', '.m4v']
        print(f"是否在支持列表中: {file_ext in valid_extensions}")
        print(f"支持的扩展名: {valid_extensions}")
        
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        print(f"文件大小: {file_size} 字节")
        
        return file_ext in valid_extensions
    else:
        print("文件不存在！")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1:
        test_path = sys.argv[1]
        print("=" * 50)
        print("文件验证测试")
        print("=" * 50)
        result = test_file_validation(test_path)
        print(f"验证结果: {'✅ 有效' if result else '❌ 无效'}")
    else:
        print("用法: python test_drag.py <文件路径>")
        print("示例: python test_drag.py \"C:\\path\\to\\video.mp4\"")
