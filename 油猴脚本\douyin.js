// ==UserScript==
// @name         抖音无水印视频下载器
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  在抖音页面添加480P、720P、1080P视频下载按钮
// @match        *://*.douyin.com/*
// @match        *://v.douyin.com/*
// @match        *://www.douyin.com/*
// @grant        GM_xmlhttpRequest
// @grant        GM_download
// @grant        GM_addStyle
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 添加CSS样式
    GM_addStyle(`
        .douyin-downloader {
            position: fixed;
            top: 50%;
            right: 20px;
            z-index: 9999;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            transition: box-shadow 0.3s ease;
        }

        .douyin-downloader.dragging {
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
            opacity: 0.9;
        }

        .douyin-downloader h3 {
            color: white;
            margin: 0 0 10px 0;
            font-size: 14px;
            text-align: center;
            cursor: move;
            padding: 5px;
            border-radius: 5px;
            position: relative;
        }

        .douyin-downloader h3:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .douyin-downloader h3::after {
            content: "⋮⋮";
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            opacity: 0.7;
        }

        .download-btn {
            display: block;
            width: 80px;
            height: 35px;
            margin: 5px 0;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: linear-gradient(45deg, #ee5a24, #ff6b6b);
            transform: scale(1.05);
        }

        .download-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .status-text {
            color: #fff;
            font-size: 10px;
            text-align: center;
            margin-top: 5px;
        }

        .debug-btn {
            background: #333 !important;
            font-size: 10px !important;
            height: 25px !important;
        }

        .debug-btn:hover {
            background: #555 !important;
        }
    `);

    // 调试模式开关
    const DEBUG_MODE = true;

    // 多种视频ID提取正则表达式
    const patterns = [
        /"video":{"play_addr":{"uri":"([a-z0-9]+)"/,
        /"aweme_id":"(\d+)"/,
        /aweme_id['"]\s*:\s*['"](\d+)['"]/,
        /"item_id":"(\d+)"/,
        /item_id['"]\s*:\s*['"](\d+)['"]/
    ];

    // 多个API接口模板
    const apiTemplates = [
        "https://www.iesdouyin.com/aweme/v1/play/?video_id=%s&ratio=%s&line=0",
        "https://www.iesdouyin.com/web/api/v2/aweme/iteminfo/?item_ids=%s",
        "https://www.douyin.com/aweme/v1/web/aweme/detail/?aweme_id=%s"
    ];

    // 请求头配置
    const headers = {
        "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1",
        "Referer": "https://www.douyin.com/",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3",
        "Accept-Encoding": "gzip, deflate, br",
        "DNT": "1",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1"
    };

    // 调试日志函数
    function debugLog(message, data = null) {
        if (DEBUG_MODE) {
            console.log(`[抖音下载器] ${message}`, data || '');
        }
    }

    // 从页面中提取视频ID的多种方法
    function extractVideoIdFromPage() {
        debugLog('开始从页面提取视频ID');

        // 方法1: 从URL中提取
        const url = window.location.href;
        debugLog('当前URL:', url);

        // 提取URL中的视频ID
        const urlPatterns = [
            /\/video\/(\d+)/,
            /\/share\/video\/(\d+)/,
            /aweme_id=(\d+)/,
            /item_id=(\d+)/
        ];

        for (const pattern of urlPatterns) {
            const match = url.match(pattern);
            if (match && match[1]) {
                debugLog('从URL提取到视频ID:', match[1]);
                return match[1];
            }
        }

        // 方法2: 从页面JSON数据中提取
        const scripts = document.querySelectorAll('script');
        for (const script of scripts) {
            const content = script.textContent || script.innerHTML;
            if (content.includes('aweme_id') || content.includes('item_id')) {
                for (const pattern of patterns) {
                    const match = pattern.exec(content);
                    if (match && match[1]) {
                        debugLog('从页面脚本提取到视频ID:', match[1]);
                        return match[1];
                    }
                }
            }
        }

        // 方法3: 从页面元素中提取
        const videoElements = document.querySelectorAll('[data-e2e="video-player"], video, [aweme-id], [item-id]');
        for (const element of videoElements) {
            const awemeId = element.getAttribute('aweme-id') || element.getAttribute('data-aweme-id');
            const itemId = element.getAttribute('item-id') || element.getAttribute('data-item-id');
            if (awemeId) {
                debugLog('从元素属性提取到aweme_id:', awemeId);
                return awemeId;
            }
            if (itemId) {
                debugLog('从元素属性提取到item_id:', itemId);
                return itemId;
            }
        }

        debugLog('未能从页面提取到视频ID');
        return null;
    }

    // 获取视频ID
    async function getVideoId(url) {
        debugLog('开始获取视频ID，URL:', url);

        // 首先尝试从当前页面提取
        const pageVideoId = extractVideoIdFromPage();
        if (pageVideoId) {
            return pageVideoId;
        }

        // 如果页面提取失败，尝试请求URL
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'GET',
                url: url,
                headers: headers,
                onload: function(response) {
                    debugLog('请求响应状态:', response.status);
                    const body = response.responseText;

                    for (const pattern of patterns) {
                        const match = pattern.exec(body);
                        if (match && match[1]) {
                            debugLog('从响应中提取到视频ID:', match[1]);
                            resolve(match[1]);
                            return;
                        }
                    }

                    debugLog('响应内容片段:', body.substring(0, 500));
                    reject(new Error('Video ID not found in URL response'));
                },
                onerror: function(error) {
                    debugLog('请求失败:', error);
                    reject(error);
                }
            });
        });
    }

    // 获取视频下载链接
    async function getVideoUrl(url, quality) {
        try {
            const id = await getVideoId(url);
            debugLog('获取到视频ID:', id);

            // 尝试多种API接口
            for (let i = 0; i < apiTemplates.length; i++) {
                try {
                    const apiUrl = apiTemplates[i];
                    debugLog(`尝试API ${i + 1}:`, apiUrl);

                    let downloadUrl;
                    if (i === 0) {
                        // 第一个API：直接播放链接
                        downloadUrl = apiUrl.replace('%s', id).replace('%s', quality);
                        debugLog('构造的下载链接:', downloadUrl);
                        return downloadUrl;
                    } else {
                        // 其他API：需要先获取视频信息
                        const videoInfo = await getVideoInfo(apiUrl.replace('%s', id));
                        downloadUrl = extractDownloadUrl(videoInfo, quality);
                        if (downloadUrl) {
                            debugLog('从API响应提取到下载链接:', downloadUrl);
                            return downloadUrl;
                        }
                    }
                } catch (error) {
                    debugLog(`API ${i + 1} 失败:`, error.message);
                    continue;
                }
            }

            // 如果所有API都失败，尝试从页面video元素获取
            const videoElement = document.querySelector('video');
            if (videoElement && videoElement.src) {
                debugLog('从页面video元素获取到链接:', videoElement.src);
                return videoElement.src;
            }

            throw new Error('所有API接口都无法获取视频链接');
        } catch (error) {
            debugLog('获取视频链接失败:', error.message);
            throw error;
        }
    }

    // 获取视频信息
    async function getVideoInfo(apiUrl) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'GET',
                url: apiUrl,
                headers: headers,
                onload: function(response) {
                    debugLog('API响应状态:', response.status);
                    if (response.status === 200) {
                        try {
                            const data = JSON.parse(response.responseText);
                            debugLog('API响应数据:', data);
                            resolve(data);
                        } catch (e) {
                            debugLog('JSON解析失败:', e.message);
                            resolve(response.responseText);
                        }
                    } else {
                        reject(new Error(`API请求失败: ${response.status}`));
                    }
                },
                onerror: function(error) {
                    debugLog('API请求错误:', error);
                    reject(error);
                }
            });
        });
    }

    // 从API响应中提取下载链接
    function extractDownloadUrl(data, quality) {
        debugLog('开始提取下载链接，质量:', quality);

        if (typeof data === 'string') {
            // 如果是字符串，尝试解析JSON
            try {
                data = JSON.parse(data);
            } catch (e) {
                debugLog('无法解析响应为JSON');
                return null;
            }
        }

        // 尝试多种路径提取视频链接
        const paths = [
            'item_list[0].video.play_addr.url_list[0]',
            'item_list[0].video.download_addr.url_list[0]',
            'aweme_list[0].video.play_addr.url_list[0]',
            'aweme_list[0].video.download_addr.url_list[0]',
            'data.item_list[0].video.play_addr.url_list[0]',
            'data.aweme_list[0].video.play_addr.url_list[0]'
        ];

        for (const path of paths) {
            try {
                const url = getNestedValue(data, path);
                if (url) {
                    debugLog('找到视频链接:', url);
                    return url;
                }
            } catch (e) {
                continue;
            }
        }

        debugLog('未能从响应中提取到视频链接');
        return null;
    }

    // 获取嵌套对象的值
    function getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            if (key.includes('[') && key.includes(']')) {
                const arrayKey = key.substring(0, key.indexOf('['));
                const index = parseInt(key.substring(key.indexOf('[') + 1, key.indexOf(']')));
                return current && current[arrayKey] && current[arrayKey][index];
            }
            return current && current[key];
        }, obj);
    }

    // 保存控件位置到localStorage
    function savePosition(top) {
        try {
            localStorage.setItem('douyin-downloader-position', top.toString());
            debugLog('位置已保存:', top);
        } catch (e) {
            debugLog('保存位置失败:', e);
        }
    }

    // 从localStorage恢复控件位置
    function restorePosition() {
        try {
            const savedTop = localStorage.getItem('douyin-downloader-position');
            if (savedTop) {
                const top = parseInt(savedTop);
                debugLog('恢复位置:', top);
                return Math.max(0, Math.min(top, window.innerHeight - 200)); // 确保在视窗内
            }
        } catch (e) {
            debugLog('恢复位置失败:', e);
        }
        // 默认位置：垂直居中
        return Math.max(0, (window.innerHeight - 200) / 2);
    }

    // 使控件可拖拽
    function makeDraggable(element) {
        const header = element.querySelector('h3');
        if (!header) return;

        let isDragging = false;
        let startY = 0;
        let startTop = 0;

        header.addEventListener('mousedown', function(e) {
            // 只响应左键点击
            if (e.button !== 0) return;

            isDragging = true;
            startY = e.clientY;
            startTop = parseInt(element.style.top) || 0;

            // 添加拖拽样式
            element.classList.add('dragging');

            // 阻止文本选择
            e.preventDefault();

            debugLog('开始拖拽，起始位置:', startTop);
        });

        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;

            const deltaY = e.clientY - startY;
            let newTop = startTop + deltaY;

            // 边界检查
            const maxTop = window.innerHeight - element.offsetHeight;
            newTop = Math.max(0, Math.min(newTop, maxTop));

            // 更新位置
            element.style.top = newTop + 'px';
        });

        document.addEventListener('mouseup', function() {
            if (!isDragging) return;

            isDragging = false;
            element.classList.remove('dragging');

            // 保存新位置
            const finalTop = parseInt(element.style.top);
            savePosition(finalTop);

            debugLog('拖拽结束，最终位置:', finalTop);
        });

        // 防止拖拽时的默认行为
        header.addEventListener('dragstart', function(e) {
            e.preventDefault();
        });
    }

    // 备用下载方案：创建下载链接
    function fallbackDownload(downloadUrl, filename, statusElement, buttons) {
        debugLog('使用备用下载方案');

        try {
            // 创建临时下载链接
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = filename;
            link.style.display = 'none';

            // 添加到页面并自动点击
            document.body.appendChild(link);
            link.click();

            // 清理
            setTimeout(() => {
                document.body.removeChild(link);
            }, 1000);

            statusElement.textContent = '下载已开始';
            debugLog('备用下载已启动');

            // 3秒后恢复按钮状态
            setTimeout(() => {
                buttons.forEach(btn => btn.disabled = false);
                statusElement.textContent = '选择清晰度下载';
            }, 3000);

        } catch (error) {
            debugLog('备用下载也失败:', error);

            // 最后的备用方案：显示下载链接让用户手动点击
            statusElement.innerHTML = `<a href="${downloadUrl}" download="${filename}" target="_blank" style="color: #fff; text-decoration: underline;">点击下载</a>`;

            setTimeout(() => {
                buttons.forEach(btn => btn.disabled = false);
                statusElement.textContent = '选择清晰度下载';
            }, 10000);
        }
    }

    // 下载视频
    async function downloadVideo(quality) {
        const statusElement = document.querySelector('.status-text');
        const buttons = document.querySelectorAll('.download-btn');

        try {
            // 禁用所有按钮
            buttons.forEach(btn => btn.disabled = true);
            statusElement.textContent = '正在解析...';
            debugLog('开始下载视频，质量:', quality);

            // 获取当前页面URL
            const currentUrl = window.location.href;
            debugLog('当前页面URL:', currentUrl);

            // 获取视频下载链接
            statusElement.textContent = '获取视频链接...';
            const downloadUrl = await getVideoUrl(currentUrl, quality);

            if (!downloadUrl) {
                throw new Error('无法获取视频下载链接');
            }

            statusElement.textContent = '准备下载...';
            debugLog('准备下载，链接:', downloadUrl);

            // 验证下载链接
            if (!downloadUrl.startsWith('http')) {
                throw new Error('无效的下载链接');
            }

            // 使用GM_download下载文件
            const filename = `douyin_video_${quality}_${Date.now()}.mp4`;
            debugLog('开始下载文件:', filename);

            // 尝试使用GM_download自动下载
            try {
                GM_download({
                    url: downloadUrl,
                    name: filename,
                    saveAs: false, // 直接下载到默认位置
                    onload: function() {
                        debugLog('下载成功完成');
                        statusElement.textContent = '下载完成';
                        setTimeout(() => {
                            buttons.forEach(btn => btn.disabled = false);
                            statusElement.textContent = '选择清晰度下载';
                        }, 3000);
                    },
                    onerror: function(error) {
                        debugLog('GM_download失败:', error);
                        // 使用备用下载方案
                        fallbackDownload(downloadUrl, filename, statusElement, buttons);
                    }
                });

                statusElement.textContent = '下载已开始';
                debugLog('GM_download已启动');

            } catch (error) {
                debugLog('GM_download调用失败:', error);
                // 使用备用下载方案
                fallbackDownload(downloadUrl, filename, statusElement, buttons);
            }

        } catch (error) {
            debugLog('下载失败:', error);
            console.error('下载失败:', error);

            // 显示具体错误信息
            let errorMsg = '下载失败';
            if (error.message.includes('Video ID not found')) {
                errorMsg = '无法识别视频';
            } else if (error.message.includes('API请求失败')) {
                errorMsg = 'API请求失败';
            } else if (error.message.includes('无法获取视频链接')) {
                errorMsg = '解析失败';
            } else if (DEBUG_MODE) {
                errorMsg = `失败: ${error.message.substring(0, 20)}`;
            }

            statusElement.textContent = errorMsg;
            buttons.forEach(btn => btn.disabled = false);

            // 5秒后恢复状态
            setTimeout(() => {
                statusElement.textContent = '选择清晰度下载';
            }, 5000);

            // 如果是调试模式，显示详细错误
            if (DEBUG_MODE) {
                alert(`下载失败详情：\n${error.message}\n\n请查看控制台获取更多信息`);
            }
        }
    }

    // 创建下载按钮组
    function createDownloadButtons() {
        // 检查是否已经存在下载器
        if (document.querySelector('.douyin-downloader')) {
            return;
        }

        debugLog('创建下载按钮组');

        const downloader = document.createElement('div');
        downloader.className = 'douyin-downloader';

        // 设置初始位置
        const initialTop = restorePosition();
        downloader.style.top = initialTop + 'px';

        downloader.innerHTML = `
            <h3>下载器 <span style="font-size: 10px; opacity: 0.7;">(可拖拽)</span></h3>
            <button class="download-btn" data-quality="720p">720P</button>
            <button class="download-btn" data-quality="1080p">1080P</button>
            <button class="download-btn debug-btn" style="background: #333; margin-top: 10px;">调试信息</button>
            <div class="status-text">选择清晰度下载</div>
        `;

        // 添加点击事件
        downloader.addEventListener('click', (e) => {
            if (e.target.classList.contains('download-btn')) {
                if (e.target.classList.contains('debug-btn')) {
                    showDebugInfo();
                } else {
                    const quality = e.target.getAttribute('data-quality');
                    downloadVideo(quality);
                }
            }
        });

        document.body.appendChild(downloader);

        // 使控件可拖拽
        makeDraggable(downloader);

        debugLog('下载按钮组已创建并设置为可拖拽');
    }

    // 显示调试信息
    function showDebugInfo() {
        const videoId = extractVideoIdFromPage();
        const currentUrl = window.location.href;
        const videoElement = document.querySelector('video');

        let debugInfo = `=== 抖音下载器调试信息 ===\n\n`;
        debugInfo += `当前URL: ${currentUrl}\n\n`;
        debugInfo += `提取的视频ID: ${videoId || '未找到'}\n\n`;
        debugInfo += `页面video元素: ${videoElement ? '存在' : '不存在'}\n`;
        if (videoElement && videoElement.src) {
            debugInfo += `Video src: ${videoElement.src}\n`;
        }
        debugInfo += `\n调试模式: ${DEBUG_MODE ? '开启' : '关闭'}\n`;
        debugInfo += `\n支持的API接口数量: ${apiTemplates.length}\n`;

        // 检查页面中的相关元素
        const videoPlayers = document.querySelectorAll('[data-e2e="video-player"], video, [aweme-id], [item-id]');
        debugInfo += `\n找到的视频相关元素: ${videoPlayers.length}个\n`;

        alert(debugInfo);

        // 同时输出到控制台
        console.log(debugInfo);
        console.log('页面中的所有script标签内容（查找视频ID）:');
        const scripts = document.querySelectorAll('script');
        scripts.forEach((script, index) => {
            const content = script.textContent || script.innerHTML;
            if (content.includes('aweme_id') || content.includes('item_id')) {
                console.log(`Script ${index}:`, content.substring(0, 1000));
            }
        });
    }

    // 检查是否为视频页面
    function isVideoPage() {
        // 检查URL是否包含视频相关路径
        const url = window.location.href;
        return url.includes('/video/') || url.includes('/share/video/') ||
               document.querySelector('video') !== null ||
               document.querySelector('[data-e2e="video-player"]') !== null;
    }

    // 初始化脚本
    function init() {
        if (isVideoPage()) {
            // 延迟创建按钮，确保页面加载完成
            setTimeout(createDownloadButtons, 1000);
        }
    }

    // 监听页面变化（SPA应用）
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                // 检查是否切换到了视频页面
                if (isVideoPage() && !document.querySelector('.douyin-downloader')) {
                    setTimeout(createDownloadButtons, 500);
                }
            }
        });
    });

    // 开始监听
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
