"""
网络服务模块
封装网络请求逻辑，包括数据获取、解析、重试机制
"""

import time
import threading
from typing import List, Optional, Callable, Dict, Any, Tuple
from dataclasses import dataclass
from enum import Enum

import requests
from bs4 import BeautifulSoup
from PyQt5.QtCore import QThread, pyqtSignal

from app.config import config, config_manager
from app.history_manager import history_manager


class SearchStatus(Enum):
    """搜索状态枚举"""
    IDLE = "idle"
    CHECKING_HISTORY = "checking_history"
    SEARCHING = "searching"
    PARSING = "parsing"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class SearchResult:
    """搜索结果数据类"""
    query: str
    actresses: List[str]
    status: SearchStatus
    message: str
    from_cache: bool = False
    retry_count: int = 0
    elapsed_time: float = 0.0


class NetworkError(Exception):
    """网络错误基类"""
    pass


class TimeoutError(NetworkError):
    """超时错误"""
    pass


class ConnectionError(NetworkError):
    """连接错误"""
    pass


class ParseError(NetworkError):
    """解析错误"""
    pass


class NetworkService:
    """网络服务类"""
    
    def __init__(self):
        self.session = requests.Session()
        self._setup_session()
        self._request_cache: Dict[str, Tuple[str, float]] = {}
        self._cache_timeout = 300  # 缓存5分钟
        self._lock = threading.RLock()
    
    def _setup_session(self):
        """设置会话配置"""
        self.session.headers.update(config.DEFAULT_HEADERS)
        # 设置连接池
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=10,
            pool_maxsize=20,
            max_retries=0  # 我们自己处理重试
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
    
    def search_actress(self, query: str) -> SearchResult:
        """
        搜索演员信息
        
        Args:
            query: 查询关键字
            
        Returns:
            SearchResult: 搜索结果
        """
        start_time = time.time()
        
        # 检查历史记录
        cached_result = history_manager.get_history(query)
        if cached_result:
            return SearchResult(
                query=query,
                actresses=cached_result.split('\n') if cached_result else [],
                status=SearchStatus.COMPLETED,
                message=config.MSG_FROM_HISTORY,
                from_cache=True,
                elapsed_time=time.time() - start_time
            )
        
        # 检查请求缓存
        cached_response = self._get_cached_response(query)
        if cached_response:
            try:
                actresses = self._parse_response(cached_response)
                result_text = '\n'.join(actresses) if actresses else config.MSG_NOT_FOUND
                
                # 保存到历史记录
                history_manager.save_history(query, result_text)
                
                return SearchResult(
                    query=query,
                    actresses=actresses,
                    status=SearchStatus.COMPLETED,
                    message="从缓存获取",
                    from_cache=True,
                    elapsed_time=time.time() - start_time
                )
            except ParseError:
                # 缓存的响应解析失败，清除缓存并重新请求
                self._clear_cache_entry(query)
        
        # 执行网络请求
        return self._perform_network_search(query, start_time)
    
    def _perform_network_search(self, query: str, start_time: float) -> SearchResult:
        """执行网络搜索"""
        retry_count = 0
        last_error = None
        
        while retry_count < config.MAX_RETRIES:
            try:
                # 构建URL
                url = config.API_SEARCH_URL.format(query)
                
                # 发送请求
                response = self.session.get(
                    url,
                    timeout=config_manager.get_timeout_tuple()
                )
                
                # 缓存响应
                self._cache_response(query, response.text)
                
                # 检查状态码
                if response.status_code == config.HTTP_OK:
                    actresses = self._parse_response(response.text)
                    
                    if actresses:
                        result_text = '\n'.join(actresses)
                        # 保存到历史记录
                        history_manager.save_history(query, result_text)
                        
                        return SearchResult(
                            query=query,
                            actresses=actresses,
                            status=SearchStatus.COMPLETED,
                            message="查询成功",
                            retry_count=retry_count,
                            elapsed_time=time.time() - start_time
                        )
                    else:
                        return SearchResult(
                            query=query,
                            actresses=[],
                            status=SearchStatus.FAILED,
                            message=config.MSG_NOT_FOUND,
                            retry_count=retry_count,
                            elapsed_time=time.time() - start_time
                        )
                
                elif response.status_code == config.HTTP_NOT_FOUND:
                    return SearchResult(
                        query=query,
                        actresses=[],
                        status=SearchStatus.FAILED,
                        message=config.MSG_NOT_FOUND,
                        retry_count=retry_count,
                        elapsed_time=time.time() - start_time
                    )
                
                elif response.status_code >= config.HTTP_SERVER_ERROR:
                    raise NetworkError(config.MSG_SERVER_ERROR)
                
                else:
                    raise NetworkError(config_manager.format_network_error(response.status_code))
            
            except requests.exceptions.Timeout as e:
                last_error = TimeoutError(config.MSG_TIMEOUT_ERROR)
                retry_count += 1
                if retry_count < config.MAX_RETRIES:
                    time.sleep(config.TIMEOUT_RETRY_DELAY)
            
            except requests.exceptions.ConnectionError as e:
                last_error = ConnectionError(config.MSG_CONNECTION_ERROR)
                retry_count += 1
                if retry_count < config.MAX_RETRIES:
                    time.sleep(config.TIMEOUT_RETRY_DELAY)
            
            except (NetworkError, ParseError) as e:
                last_error = e
                retry_count += 1
                if retry_count < config.MAX_RETRIES:
                    time.sleep(config.RETRY_DELAY)
            
            except Exception as e:
                last_error = NetworkError(config.MSG_UNKNOWN_ERROR)
                retry_count += 1
                if retry_count < config.MAX_RETRIES:
                    time.sleep(config.RETRY_DELAY)
        
        # 所有重试都失败了
        error_message = str(last_error) if last_error else config.MSG_UNKNOWN_ERROR
        return SearchResult(
            query=query,
            actresses=[],
            status=SearchStatus.FAILED,
            message=error_message,
            retry_count=retry_count,
            elapsed_time=time.time() - start_time
        )
    
    def _parse_response(self, html_content: str) -> List[str]:
        """
        解析HTML响应
        
        Args:
            html_content: HTML内容
            
        Returns:
            List[str]: 演员名字列表
            
        Raises:
            ParseError: 解析错误
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找女优名字链接
            actress_links = soup.find_all('a', href=lambda x: x and config.ACTRESS_LINK_SELECTOR in x)
            
            if not actress_links:
                return []
            
            # 使用 set 进行去重
            actress_names = set()
            for link in actress_links:
                actress_name = link.get_text(strip=True)
                if (actress_name and 
                    actress_name not in config.INVALID_ACTRESS_NAMES and 
                    len(actress_name) > config.MIN_ACTRESS_NAME_LENGTH):
                    actress_names.add(actress_name)
            
            # 按字母顺序排序
            return sorted(list(actress_names))
            
        except Exception as e:
            raise ParseError(f"解析HTML内容失败: {e}")
        finally:
            # 确保BeautifulSoup对象被及时释放
            if 'soup' in locals():
                del soup
    
    def _get_cached_response(self, query: str) -> Optional[str]:
        """获取缓存的响应"""
        with self._lock:
            if query in self._request_cache:
                content, timestamp = self._request_cache[query]
                if time.time() - timestamp < self._cache_timeout:
                    return content
                else:
                    # 缓存过期，删除
                    del self._request_cache[query]
            return None
    
    def _cache_response(self, query: str, content: str):
        """缓存响应"""
        with self._lock:
            self._request_cache[query] = (content, time.time())
            
            # 清理过期缓存
            self._cleanup_expired_cache()
    
    def _clear_cache_entry(self, query: str):
        """清除指定的缓存条目"""
        with self._lock:
            self._request_cache.pop(query, None)
    
    def _cleanup_expired_cache(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, (_, timestamp) in self._request_cache.items()
            if current_time - timestamp >= self._cache_timeout
        ]
        for key in expired_keys:
            del self._request_cache[key]
    
    def clear_cache(self):
        """清空所有缓存"""
        with self._lock:
            self._request_cache.clear()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            return {
                'cache_size': len(self._request_cache),
                'cache_timeout': self._cache_timeout,
                'cache_entries': list(self._request_cache.keys())
            }


class AsyncNetworkWorker(QThread):
    """异步网络工作线程"""
    
    # 信号定义
    finished = pyqtSignal(SearchResult)
    status_changed = pyqtSignal(str)
    progress_changed = pyqtSignal(int)
    
    def __init__(self, query: str, network_service: Optional[NetworkService] = None):
        super().__init__()
        self.query = query
        self.network_service = network_service or NetworkService()
        self._is_cancelled = False
    
    def run(self):
        """执行搜索"""
        if self._is_cancelled:
            return
        
        try:
            # 发出状态信号
            self.status_changed.emit(config.MSG_SEARCHING)
            self.progress_changed.emit(config.PROGRESS_INITIAL)
            
            # 执行搜索
            result = self.network_service.search_actress(self.query)
            
            if not self._is_cancelled:
                # 更新进度
                if result.status == SearchStatus.COMPLETED:
                    self.progress_changed.emit(config.PROGRESS_COMPLETE)
                else:
                    self.progress_changed.emit(config.PROGRESS_RESET)
                
                # 发出完成信号
                self.finished.emit(result)
        
        except Exception as e:
            if not self._is_cancelled:
                error_result = SearchResult(
                    query=self.query,
                    actresses=[],
                    status=SearchStatus.FAILED,
                    message=f"搜索过程中发生错误: {e}"
                )
                self.finished.emit(error_result)
    
    def cancel(self):
        """取消搜索"""
        self._is_cancelled = True


# 创建全局实例
network_service = NetworkService()
