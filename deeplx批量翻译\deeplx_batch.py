import os
import tkinter as tk
from tkinter import filedialog, messagebox
import requests
import json
from requests.packages.urllib3.exceptions import InsecureRequestWarning
import threading
import configparser
import subprocess
import time
import random
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.scrolled import ScrolledText
from config import API

requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

CONFIG_FILE = 'translation_config.ini'

class TranslationAPI:
    def __init__(self):
        self.retry_count = 0
        self.max_retries = 5
        self.base_delay = 3  # 基础延迟时间（秒）
        self.max_delay = 60  # 最大延迟时间（秒）
        self.last_request_time = 0
        self.min_request_interval = 3  # 最小请求间隔（秒）
        self.consecutive_errors = 0
        self.error_threshold = 3  # 连续错误阈值

    def get_headers(self):
        return {
            'Content-Type': 'application/json',
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache"
        }

    def calculate_delay(self):
        # 基础延迟 + 指数退避 + 随机因子
        if self.consecutive_errors > 0:
            delay = min(self.base_delay * (2 ** self.consecutive_errors) + random.uniform(1, 5), self.max_delay)
        else:
            delay = self.base_delay + random.uniform(0, 2)
        return delay

    def wait_if_needed(self):
        # 确保请求间隔不小于最小间隔时间
        elapsed = time.time() - self.last_request_time
        if elapsed < self.min_request_interval:
            time.sleep(self.min_request_interval - elapsed)
        self.last_request_time = time.time()

    def translate(self, text, log_callback=None):
        while self.retry_count < self.max_retries:
            try:
                self.wait_if_needed()
                
                payload = json.dumps({
                    "text": text,
                    "source_lang": "auto",
                    "target_lang": "ZH"
                })
                
                if log_callback:
                    log_callback(f"发送翻译请求...")

                response = requests.post(
                    API['url'],
                    headers=self.get_headers(),
                    data=payload,
                    verify=False,
                    timeout=30
                )

                if response.status_code == 200:
                    data = response.json()
                    translated_text = data.get("data", "")
                    if translated_text:
                        self.retry_count = 0  # 成功后重置重试计数
                        self.consecutive_errors = 0  # 重置连续错误计数
                        return translated_text
                    else:
                        raise Exception(f"API返回数据中没有找到翻译结果: {data}")

                elif response.status_code in [403, 429]:
                    self.consecutive_errors += 1
                    if log_callback:
                        log_callback(f"请求被限制 (状态码: {response.status_code})，等待后重试...")
                    
                    # 如果连续错误超过阈值，增加基础延迟时间
                    if self.consecutive_errors >= self.error_threshold:
                        self.base_delay = min(self.base_delay * 1.5, 10)
                        if log_callback:
                            log_callback(f"检测到频繁错误，增加基础延迟到 {self.base_delay:.1f} 秒")
                    
                    delay = self.calculate_delay()
                    if log_callback:
                        log_callback(f"等待 {delay:.1f} 秒后重试...")
                    time.sleep(delay)
                    self.retry_count += 1
                    continue

                else:
                    raise Exception(f"请求失败: {response.status_code} {response.reason}")

            except Exception as e:
                if log_callback:
                    log_callback(f"翻译出错: {str(e)}")
                self.retry_count += 1
                self.consecutive_errors += 1
                
                if self.retry_count >= self.max_retries:
                    if log_callback:
                        log_callback("达到最大重试次数，翻译失败")
                    return None
                    
                delay = self.calculate_delay()
                if log_callback:
                    log_callback(f"等待 {delay:.1f} 秒后重试...")
                time.sleep(delay)
                continue

        return None

def save_config(input_dir, output_dir):
    config = configparser.ConfigParser()
    config['Paths'] = {
        'input_dir': input_dir,
        'output_dir': output_dir
    }
    with open(CONFIG_FILE, 'w') as configfile:
        config.write(configfile)

def load_config():
    config = configparser.ConfigParser()
    if os.path.exists(CONFIG_FILE):
        config.read(CONFIG_FILE)
        if 'Paths' in config:
            return config['Paths'].get('input_dir', ''), config['Paths'].get('output_dir', '')
    return '', ''

def translate_text(text):
    payload = json.dumps({
        "text": text,
        "source_lang": "auto",
        "target_lang": "ZH"
    })
    headers = {
        'Content-Type': 'application/json',
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                      "AppleWebKit/537.36 (KHTML, like Gecko) "
                      "Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,"
                  "application/xml;q=0.9,image/avif,image/webp,"
                  "image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Accept-Encoding": "gzip, deflate",
        "Connection": "keep-alive",
    }

    try:
        response = requests.post(API['url'], headers=headers, data=payload, verify=False)
        if response.status_code == 200:
            data = response.json()
            translated_text = data.get("data", "")
            if not translated_text:
                print(f"API 返回的数据中没有找到 data: {data}")
            return translated_text
        else:
            messagebox.showerror("错误", f"请求失败: {response.status_code} {response.reason}")
            return None
    except requests.exceptions.RequestException as e:
        messagebox.showerror("错误", f"请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        messagebox.showerror("错误", f"JSON 解码失败: {e}")
        return None

def translate_text_file(input_file_path, output_dir, progress_var, total_lines):
    translated_text = ""
    try:
        with open(input_file_path, 'r', encoding='utf-8') as file:
            # 按行读取文件并分批翻译
            lines = file.readlines()
            chunk_size = 100  # 每50行一块
            chunks = [lines[i:i+chunk_size] for i in range(0, len(lines), chunk_size)]
            total_chunks = len(chunks)
            for i, chunk in enumerate(chunks):
                chunk_text = "".join(chunk)
                translated_chunk = translate_text(chunk_text)
                if translated_chunk:
                    translated_text += translated_chunk
                else:
                    start_line = i * chunk_size + 1
                    end_line = min((i + 1) * chunk_size, len(lines))
                    print(f"翻译失败：第{start_line}行到{end_line}行")
                    return 0
                # 更新进度条
                current_chunk = i + 1
                progress_var.set(f"翻译进度: {current_chunk}/{total_chunks} 块完成")
                progress['value'] = (current_chunk / total_chunks) * 100
                root.update_idletasks()
                time.sleep(1)  # 添加延迟
    except Exception as e:
        print(f"读取文件失败: {e}")
        return 0

    # 获取输入文件名并构造输出文件路径
    input_filename = os.path.basename(input_file_path)
    file_extension = os.path.splitext(input_filename)[1]
    output_file_path = os.path.join(output_dir, f"{os.path.splitext(input_filename)[0]}_translated{file_extension}")

    with open(output_file_path, 'w', encoding='utf-8') as file:
        file.write(translated_text)

    print(f"数据已成功写入到 {output_file_path}")
    return 1

def process_directory(input_dir, output_dir, progress_var, total_files):
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    translated_count = 0
    for filename in os.listdir(input_dir):
        if filename.endswith(('.srt', '.txt')):
            input_file_path = os.path.join(input_dir, filename)
            with open(input_file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()
                total_lines = len(lines)
            translated_count += translate_text_file(input_file_path, output_dir, progress_var, total_lines)
            remaining_files = total_files - translated_count
            progress_var.set(f"文件进度: {translated_count}/{total_files} 文件完成 (剩余: {remaining_files})")
            progress['value'] = (translated_count / total_files) * 100
            root.update_idletasks()
            time.sleep(0.5)  # 每次处理文件后添加延迟

    if translated_count > 0:
        messagebox.showinfo("完成", "翻译完成")
        # 打开翻译好的目录
        open_folder(output_dir)

def open_folder(folder_path):
    try:
        if os.name == 'nt':  # Windows
            os.startfile(folder_path)
        elif os.name == 'posix':  # Linux 或 macOS
            subprocess.Popen(['open', folder_path])
        else:
            messagebox.showwarning("警告", "无法在当前操作系统上打开文件夹")
    except Exception as e:
        messagebox.showerror("错误", f"打开文件夹时出错: {e}")

def select_input_directory():
    directory = filedialog.askdirectory()
    if directory:
        input_path_entry.delete(0, tk.END)
        input_path_entry.insert(0, directory)

def select_output_directory():
    directory = filedialog.askdirectory()
    if directory:
        output_path_entry.delete(0, tk.END)
        output_path_entry.insert(0, directory)

def start_translation():
    input_dir = input_path_entry.get()
    output_dir = output_path_entry.get()

    if not input_dir or not output_dir:
        messagebox.showwarning("警告", "请输入输入目录和输出目录")
        return

    # 计算总文件数
    total_files = sum(1 for filename in os.listdir(input_dir) if filename.endswith(('.srt', '.txt')))
    if total_files == 0:
        messagebox.showwarning("警告", "输入目录中没有可翻译的文件")
        return

    # 保存配置
    save_config(input_dir, output_dir)

    # 修改按钮文本为"翻译中"
    translate_button.config(text="正在翻译", state=tk.DISABLED)

    # 创建进度变量
    progress_var = tk.StringVar()
    progress_var.set("翻译准备中...")
    progress_label = tk.Label(root, textvariable=progress_var)
    progress_label.grid(row=4, column=0, columnspan=3, padx=10, pady=10, sticky=tk.W)

    # 创建进度条
    global progress
    progress = ttk.Progressbar(root, orient="horizontal", length=400, mode="determinate")
    progress.grid(row=5, column=0, columnspan=3, padx=10, pady=5, sticky=tk.W)

    # 启动新线程进行翻译
    translation_thread = threading.Thread(target=process_directory, args=(input_dir, output_dir, progress_var, total_files))
    translation_thread.start()

    # 监听线程结束
    def check_thread():
        if translation_thread.is_alive():
            root.after(100, check_thread)
        else:
            translate_button.config(text="开始翻译", state=tk.NORMAL)
            progress_label.destroy()
            progress.destroy()

    check_thread()

class TranslationApp:
    def __init__(self):
        self.root = ttk.Window(themename="cosmo")
        self.root.title("DeepLX 批量翻译工具")
        self.translation_api = TranslationAPI()  # 创建TranslationAPI实例
        self.setup_ui()
        self.load_config()
        
    def setup_ui(self):
        # 设置窗口大小和位置
        window_width = 800
        window_height = 600
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=BOTH, expand=YES)

        # 路径选择区域
        path_frame = ttk.LabelFrame(main_frame, text="路径设置", padding="10")
        path_frame.pack(fill=X, pady=(0, 10))

        # 输入路径
        input_frame = ttk.Frame(path_frame)
        input_frame.pack(fill=X, pady=(0, 5))
        ttk.Label(input_frame, text="输入路径:").pack(side=LEFT)
        self.input_path_entry = ttk.Entry(input_frame)
        self.input_path_entry.pack(side=LEFT, fill=X, expand=YES, padx=(5, 5))
        ttk.Button(input_frame, text="选择目录", command=self.select_input_directory, style='info.Outline.TButton').pack(side=LEFT)

        # 输出路径
        output_frame = ttk.Frame(path_frame)
        output_frame.pack(fill=X)
        ttk.Label(output_frame, text="输出路径:").pack(side=LEFT)
        self.output_path_entry = ttk.Entry(output_frame)
        self.output_path_entry.pack(side=LEFT, fill=X, expand=YES, padx=(5, 5))
        ttk.Button(output_frame, text="选择目录", command=self.select_output_directory, style='info.Outline.TButton').pack(side=LEFT)

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="翻译日志", padding="10")
        log_frame.pack(fill=BOTH, expand=YES)
        
        self.log_text = ScrolledText(log_frame, height=10)
        self.log_text.pack(fill=BOTH, expand=YES)

        # 进度条
        self.progress_var = ttk.StringVar()
        self.progress_label = ttk.Label(main_frame, textvariable=self.progress_var)
        self.progress_label.pack(fill=X, pady=(10, 5))
        
        self.progress = ttk.Progressbar(main_frame, mode="determinate", style="success.Horizontal.TProgressbar")
        self.progress.pack(fill=X)

        # 控制按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=X, pady=(10, 0))
        
        self.translate_button = ttk.Button(
            button_frame, 
            text="开始翻译", 
            command=self.start_translation,
            style='success.TButton',
            width=20
        )
        self.translate_button.pack(side=LEFT, padx=(0, 10))
        
        ttk.Button(
            button_frame, 
            text="清除日志", 
            command=lambda: self.log_text.delete(1.0, END),
            style='secondary.Outline.TButton',
            width=20
        ).pack(side=LEFT)

    def log(self, message):
        self.log_text.insert(END, f"{message}\n")
        self.log_text.see(END)

    def load_config(self):
        config = configparser.ConfigParser()
        if os.path.exists(CONFIG_FILE):
            config.read(CONFIG_FILE)
            if 'Paths' in config:
                self.input_path_entry.insert(0, config['Paths'].get('input_dir', ''))
                self.output_path_entry.insert(0, config['Paths'].get('output_dir', ''))

    def save_config(self):
        config = configparser.ConfigParser()
        config['Paths'] = {
            'input_dir': self.input_path_entry.get(),
            'output_dir': self.output_path_entry.get()
        }
        with open(CONFIG_FILE, 'w') as configfile:
            config.write(configfile)

    def select_input_directory(self):
        directory = filedialog.askdirectory()
        if directory:
            self.input_path_entry.delete(0, tk.END)
            self.input_path_entry.insert(0, directory)

    def select_output_directory(self):
        directory = filedialog.askdirectory()
        if directory:
            self.output_path_entry.delete(0, tk.END)
            self.output_path_entry.insert(0, directory)

    def translate_text(self, text):
        return self.translation_api.translate(text, self.log)

    def translate_text_file(self, input_file_path, output_dir, total_lines):
        translated_text = ""
        try:
            with open(input_file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()
                chunk_size = 100  # 每100行一块
                chunks = [lines[i:i+chunk_size] for i in range(0, len(lines), chunk_size)]
                total_chunks = len(chunks)
                
                for i, chunk in enumerate(chunks):
                    chunk_text = "".join(chunk)
                    self.log(f"\n开始翻译第 {i+1}/{total_chunks} 块...")
                    translated_chunk = self.translate_text(chunk_text)
                    
                    if translated_chunk:
                        translated_text += translated_chunk
                        self.log(f"✓ 第 {i+1} 块翻译成功")
                    else:
                        start_line = i * chunk_size + 1
                        end_line = min((i + 1) * chunk_size, len(lines))
                        self.log(f"❌ 翻译失败：第{start_line}行到{end_line}行")
                        return 0
                    
                    current_chunk = i + 1
                    self.progress_var.set(f"翻译进度: {current_chunk}/{total_chunks} 块完成")
                    self.progress['value'] = (current_chunk / total_chunks) * 100
                    self.root.update_idletasks()

        except Exception as e:
            self.log(f"读取文件失败: {str(e)}")
            return 0

        input_filename = os.path.basename(input_file_path)
        file_extension = os.path.splitext(input_filename)[1]
        output_file_path = os.path.join(output_dir, f"{os.path.splitext(input_filename)[0]}_translated{file_extension}")

        try:
            with open(output_file_path, 'w', encoding='utf-8') as file:
                file.write(translated_text)
            self.log(f"✓ 已保存翻译结果到: {output_file_path}")
            return 1
        except Exception as e:
            self.log(f"保存文件失败: {str(e)}")
            return 0

    def process_directory(self):
        input_dir = self.input_path_entry.get()
        output_dir = self.output_path_entry.get()

        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        files = [f for f in os.listdir(input_dir) if f.endswith(('.srt', '.txt'))]
        total_files = len(files)
        translated_count = 0

        for filename in files:
            self.log(f"\n开始翻译文件: {filename}")
            input_file_path = os.path.join(input_dir, filename)
            with open(input_file_path, 'r', encoding='utf-8') as file:
                total_lines = len(file.readlines())
            
            translated_count += self.translate_text_file(input_file_path, output_dir, total_lines)
            remaining_files = total_files - translated_count
            self.progress_var.set(f"总进度: {translated_count}/{total_files} 文件完成 (剩余: {remaining_files})")
            self.progress['value'] = (translated_count / total_files) * 100
            self.root.update_idletasks()
            time.sleep(0.5)

        if translated_count > 0:
            self.log("\n✨ 翻译任务完成!")
            messagebox.showinfo("完成", "翻译完成")
            self.open_folder(output_dir)
        else:
            self.log("\n❌ 没有文件被成功翻译")

    def start_translation(self):
        input_dir = self.input_path_entry.get()
        output_dir = self.output_path_entry.get()

        if not input_dir or not output_dir:
            messagebox.showwarning("警告", "请输入输入目录和输出目录")
            return

        total_files = sum(1 for filename in os.listdir(input_dir) if filename.endswith(('.srt', '.txt')))
        if total_files == 0:
            messagebox.showwarning("警告", "输入目录中没有可翻译的文件")
            return

        self.save_config()
        self.translate_button.config(text="正在翻译", state=tk.DISABLED)
        self.log_text.delete(1.0, END)
        self.log("开始批量翻译任务...\n")
        
        threading.Thread(target=self.process_directory_wrapper).start()

    def process_directory_wrapper(self):
        try:
            self.process_directory()
        finally:
            self.translate_button.config(text="开始翻译", state=tk.NORMAL)

    def open_folder(self, folder_path):
        try:
            if os.name == 'nt':  # Windows
                os.startfile(folder_path)
            elif os.name == 'posix':  # Linux 或 macOS
                subprocess.Popen(['open', folder_path])
            else:
                messagebox.showwarning("警告", "无法在当前操作系统上打开文件夹")
        except Exception as e:
            messagebox.showerror("错误", f"打开文件夹时出错: {e}")

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = TranslationApp()
    app.run()
