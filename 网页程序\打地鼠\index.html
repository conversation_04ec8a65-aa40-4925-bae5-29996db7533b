<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>打地鼠游戏</title>
    <style>
        body {
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #7b7ce6 0%, #a6e3e9 100%);
            font-family: '微软雅黑', Arial, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: #fff;
            border-radius: 28px;
            box-shadow: 0 8px 32px rgba(60,60,120,0.18);
            padding: 32px 32px 24px 32px;
            max-width: 480px;
            width: 100%;
            margin: 32px 0;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        h1 {
            margin-top: 30px;
        }
        #game {
            display: grid;
            grid-template-columns: repeat(3, 90px);
            grid-gap: 24px;
            justify-content: center;
            margin: 30px auto 0 auto;
            width: 312px;
            background: linear-gradient(135deg, #e0f7fa 60%, #ffe0e9 100%);
            border-radius: 18px;
            box-shadow: 0 2px 12px #b2dfdb60;
            padding: 24px 50px 24px 50px;
        }
        .hole {
            width: 90px;
            height: 90px;
            background: radial-gradient(circle at 60% 40%, #6d4c41 60%, #3e2723 100%);
            border-radius: 50%;
            border: 4px solid #a1887f;
            box-shadow: 0 4px 16px #79554855 inset, 0 2px 8px #fff8e1;
            position: relative;
            cursor: pointer;
            transition: box-shadow 0.2s, border 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .hole:hover {
            box-shadow: 0 0 0 4px #ffd54f99, 0 4px 16px #79554855 inset;
            border-color: #ffd54f;
        }
        .hole.active {
            background: radial-gradient(circle at 60% 40%, #a1887f 60%, #6d4c41 100%);
        }
        .mole {
            width: 54px;
            height: 54px;
            position: absolute;
            left: 18px;
            top: 18px;
            display: none;
            z-index: 2;
        }
        .hole.active .mole {
            display: block;
            animation: popUp 0.28s cubic-bezier(.4,2,.6,1.2);
        }
        @keyframes popUp {
            0% {
                transform: translateY(40px) scale(0.7);
                opacity: 0;
            }
            60% {
                transform: translateY(-8px) scale(1.08);
                opacity: 1;
            }
            80% {
                transform: translateY(2px) scale(0.98);
            }
            100% {
                transform: translateY(0) scale(1);
                opacity: 1;
            }
        }
        .mole.hit {
            transform: scale(0.7) rotate(-18deg);
            filter: brightness(0.85) drop-shadow(0 0 6px #ffb300);
        }
        #score, #timer {
            font-size: 22px;
            margin: 10px;
        }
        .btn-row {
            display: flex;
            justify-content: center;
            gap: 28px;
            margin-top: 18px;
        }
        .btn {
            padding: 10px 32px;
            font-size: 1.1rem;
            border: none;
            border-radius: 16px;
            box-shadow: 0 2px 8px #bdbdbd40;
            cursor: pointer;
            font-weight: bold;
            transition: background 0.18s, color 0.18s, box-shadow 0.18s;
        }
        .btn-main {
            background: linear-gradient(90deg, #b7b8f7 60%, #a6e3e9 100%);
            color: #fff;
        }
        .btn-main:disabled {
            background: #bdbdbd;
            color: #fff;
            cursor: not-allowed;
        }
        .btn-reset {
            background: linear-gradient(90deg, #ffd6e0 60%, #ffe0e9 100%);
            color: #b48a78;
        }
        .btn-reset:active {
            background: #ffb6b9;
            color: #fff;
        }
        .top-title {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 18px;
            gap: 12px;
        }
        .top-title .hammer {
            font-size: 2.2rem;
            filter: drop-shadow(0 2px 2px #bdbdbd);
        }
        .top-title .title-text {
            font-size: 2.1rem;
            font-weight: bold;
            letter-spacing: 6px;
            color: #b48a78;
            text-shadow: 0 2px 8px #e0cfcf, 0 1px 0 #fff;
        }
        .info-row {
            display: flex;
            justify-content: center;
            gap: 24px;
            margin-bottom: 18px;
        }
        .info-card {
            background: linear-gradient(135deg, #ffe0e9 60%, #e0f7fa 100%);
            border-radius: 18px;
            box-shadow: 0 2px 8px #f8bbd0a0;
            padding: 12px 28px 10px 28px;
            min-width: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .info-label {
            font-size: 1.05rem;
            color: #b48a78;
            margin-bottom: 2px;
        }
        .info-value {
            font-size: 1.6rem;
            font-weight: bold;
            color: #e57373;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="top-title">
            <span class="hammer">🔨</span>
            <span class="title-text">打地鼠</span>
            <span class="hammer" style="transform: scaleX(-1);">🔨</span>
        </div>
        <h1>打地鼠游戏</h1>
        <div class="info-row">
            <div class="info-card">
                <div class="info-label">得分</div>
                <div class="info-value" id="score">0</div>
            </div>
            <div class="info-card">
                <div class="info-label">时间</div>
                <div class="info-value" id="timer">30</div>
            </div>
            <div class="info-card">
                <div class="info-label">命中率</div>
                <div class="info-value" id="accuracy">100%</div>
            </div>
        </div>
        <div class="btn-row">
            <button id="startBtn" class="btn btn-main">开始游戏</button>
            <button id="resetBtn" class="btn btn-reset">重置游戏</button>
        </div>
        <div id="game"></div>
    </div>
    <script>
        const game = document.getElementById('game');
        const scoreEl = document.getElementById('score');
        const timerEl = document.getElementById('timer');
        const startBtn = document.getElementById('startBtn');
        const resetBtn = document.getElementById('resetBtn');
        let score = 0;
        let time = 30;
        let timer = null;
        let moleTimer = null;
        let activeIndex = -1;
        let isPlaying = false;
        let totalClicks = 0;

        // 创建9个洞
        for (let i = 0; i < 9; i++) {
            const hole = document.createElement('div');
            hole.className = 'hole';
            hole.innerHTML = `<div class="mole"> <svg width="54" height="54" viewBox="0 0 54 54" fill="none" xmlns="http://www.w3.org/2000/svg"><ellipse cx="27" cy="36" rx="20" ry="15" fill="#f9b28a"/><ellipse cx="19" cy="19" rx="7" ry="7" fill="#f9b28a"/><ellipse cx="35" cy="19" rx="7" ry="7" fill="#f9b28a"/><ellipse cx="27" cy="28" rx="13" ry="13" fill="#f9b28a"/><ellipse cx="21" cy="18" rx="2.5" ry="3" fill="#fff"/><ellipse cx="33" cy="18" rx="2.5" ry="3" fill="#fff"/><ellipse cx="21" cy="19" rx="1.2" ry="1.5" fill="#a66a4a"/><ellipse cx="33" cy="19" rx="1.2" ry="1.5" fill="#a66a4a"/><ellipse cx="27" cy="32" rx="3.5" ry="2.2" fill="#a66a4a"/><ellipse cx="25.5" cy="32" rx="0.5" ry="0.7" fill="#fff"/><ellipse cx="28.5" cy="32" rx="0.5" ry="0.7" fill="#fff"/><rect x="25" y="36" width="1.2" height="4" rx="0.6" fill="#a66a4a"/><rect x="27.8" y="36" width="1.2" height="4" rx="0.6" fill="#a66a4a"/><ellipse cx="27" cy="38.5" rx="1.2" ry="0.7" fill="#fff"/></svg> </div>`;
            hole.addEventListener('click', function() {
                if (!isPlaying) return;
                totalClicks++;
                if (hole.classList.contains('active')) {
                    score++;
                    scoreEl.textContent = score;
                    hole.classList.remove('active');
                    const mole = hole.querySelector('.mole');
                    mole.classList.add('hit');
                    setTimeout(()=>mole.classList.remove('hit'), 180);
                }
                updateAccuracy();
            });
            game.appendChild(hole);
        }
        const holes = document.querySelectorAll('.hole');

        function showMole() {
            if (activeIndex !== -1) holes[activeIndex].classList.remove('active');
            activeIndex = Math.floor(Math.random() * 9);
            holes[activeIndex].classList.add('active');
        }

        function updateAccuracy() {
            const accuracy = totalClicks === 0 ? 100 : Math.round((score / totalClicks) * 100);
            document.getElementById('accuracy').textContent = accuracy + '%';
        }

        function startGame() {
            if (isPlaying) return;
            isPlaying = true;
            score = 0;
            time = 30;
            totalClicks = 0;
            scoreEl.textContent = score;
            timerEl.textContent = time;
            document.getElementById('accuracy').textContent = '100%';
            startBtn.textContent = '游戏中…';
            startBtn.disabled = true;
            resetBtn.disabled = false;
            showMole();
            moleTimer = setInterval(showMole, 1200);
            timer = setInterval(() => {
                time--;
                timerEl.textContent = time;
                if (time <= 0) {
                    endGame();
                }
            }, 1000);
        }

        function endGame() {
            isPlaying = false;
            clearInterval(timer);
            clearInterval(moleTimer);
            if (activeIndex !== -1) holes[activeIndex].classList.remove('active');
            startBtn.textContent = '开始游戏';
            startBtn.disabled = false;
            resetBtn.disabled = false;
            alert('游戏结束！你的分数是：' + score);
        }

        function resetGame() {
            isPlaying = false;
            clearInterval(timer);
            clearInterval(moleTimer);
            if (activeIndex !== -1) holes[activeIndex].classList.remove('active');
            score = 0;
            time = 30;
            totalClicks = 0;
            scoreEl.textContent = score;
            timerEl.textContent = time;
            document.getElementById('accuracy').textContent = '100%';
            startBtn.textContent = '开始游戏';
            startBtn.disabled = false;
            resetBtn.disabled = true;
        }

        startBtn.textContent = '开始游戏';
        startBtn.disabled = false;
        resetBtn.disabled = true;
        startBtn.addEventListener('click', startGame);
        resetBtn.addEventListener('click', resetGame);
    </script>
</body>
</html>
