# -*- coding: utf-8 -*-
"""
DeepL翻译配置文件
"""

# API配置
API = {
    'url': 'https://api.deeplx.org/YDNExdKKiA16rIdFr4nQGuR1fFECw-kLkSVUKx-hV1M/translate'
}

# 支持的语言
LANGUAGES = {
    'auto': '自动检测',
    'ZH': '中文',
    'EN': '英语',
    'JA': '日语'
}

# UI配置 - 简约商务风格
UI = {
    'title': 'DeepL 翻译助手',
    'width': 1000,
    'height': 680,
    'font': ('Microsoft YaHei UI', 9),          # 基础字体
    'text_font': ('Microsoft YaHei UI', 10),    # 文本区域字体
    'button_font': ('Microsoft YaHei UI', 9),   # 按钮字体
    'title_font': ('Microsoft YaHei UI', 11, 'bold'),   # 标题字体
    'subtitle_font': ('Microsoft YaHei UI', 9),         # 副标题字体
    'padding': 12,

    # 简约商务配色方案
    'bg': '#FFFFFF',           # 主背景色 - 纯白
    'fg': '#2D3748',           # 主文字色 - 深灰
    'card_bg': '#FFFFFF',      # 卡片背景色
    'card_border': '#E2E8F0',  # 卡片边框色
    'section_bg': '#F7FAFC',   # 区域背景色

    # 按钮颜色 - 商务风格
    'primary_button_bg': '#2B6CB0',    # 主要按钮 - 商务蓝
    'primary_button_fg': '#FFFFFF',    # 主要按钮文字
    'primary_button_hover': '#2C5282', # 主要按钮悬停
    'secondary_button_bg': '#F8F9FA',  # 次要按钮 - 浅灰背景
    'secondary_button_fg': '#1A202C',  # 次要按钮文字 - 深黑色
    'secondary_button_hover': '#E2E8F0', # 次要按钮悬停
    'secondary_button_border': '#A0AEC0', # 次要按钮边框 - 深一点
    'accent_button_bg': '#D69E2E',     # 强调按钮 - 金色
    'accent_button_fg': '#FFFFFF',     # 强调按钮文字
    'accent_button_hover': '#B7791F',  # 强调按钮悬停
    'danger_button_bg': '#E53E3E',     # 危险按钮 - 红色
    'danger_button_fg': '#FFFFFF',     # 危险按钮文字

    # 文本区域颜色
    'text_bg': '#FFFFFF',      # 文本区背景
    'text_fg': '#2D3748',      # 文本区文字颜色
    'text_border': '#CBD5E0',  # 文本区边框
    'text_focus_border': '#2B6CB0', # 文本区聚焦边框
    'text_placeholder': '#A0AEC0', # 占位符颜色

    # 其他颜色
    'border': '#E2E8F0',       # 一般边框颜色
    'separator': '#CBD5E0',    # 分隔线颜色
    'highlight_bg': '#EBF8FF', # 高亮背景
    'highlight_fg': '#2B6CB0', # 高亮文字
    'success_color': '#38A169', # 成功色 - 绿色
    'warning_color': '#D69E2E', # 警告色 - 金色
    'error_color': '#E53E3E',   # 错误色 - 红色
    'info_color': '#3182CE',    # 信息色 - 蓝色

    # 组件特定配置
    'text': {
        'height': 15,
        'relief': 'solid',
        'borderwidth': 1,
        'padx': 12,
        'pady': 12,
        'insertwidth': 2,
        'selectbackground': '#EBF8FF',
        'selectforeground': '#2B6CB0',
        'wrap': 'word',
        'spacing1': 1,  # 行间距
        'spacing2': 1,
        'spacing3': 1
    },
    'button': {
        'width': 12,           # 统一按钮宽度
        'height': 32,          # 统一按钮高度（像素）
        'padding': (16, 8),    # 统一内边距
        'relief': 'flat',
        'borderwidth': 1,
        'cursor': 'hand2'
    },
    'primary_button': {
        'width': 12,
        'height': 32,
        'padding': (16, 8),
        'relief': 'flat',
        'borderwidth': 0
    },
    'combo': {
        'width': 16,
        'relief': 'solid',
        'borderwidth': 1,
        'padding': 6
    },
    'swap_button': {
        'width': 2,
        'height': 28,
        'relief': 'solid',
        'borderwidth': 1,
        'padding': (6, 4),
        'text': '⇄',
        'font': ('Microsoft YaHei UI', 10, 'bold')
    },
    'section': {
        'relief': 'flat',
        'borderwidth': 1,
        'padding': 16
    },
    'status_bar': {
        'height': 35,
        'padding': 15,
        'relief': 'flat'
    },
    'window': {
        'min_width': 900,
        'min_height': 650
    },
    'layout': {
        'button_spacing': 12,    # 按钮间距
        'button_group_spacing': 25,  # 按钮组间距
        'section_spacing': 12,  # 区域间距
        'element_spacing': 6    # 元素间距
    }
}

# 应用配置
APP = {
    'version': '1.0.0',
    'author': 'chengbeyond',
    'description': 'DeepL翻译助手 - 支持多语言在线翻译',
    'state_save_interval': 5000,  # 状态保存间隔（毫秒）
    'max_history_records': 100  # 最大历史记录数
}

# 文件路径
FILES = {
    'history': 'translation_history.json',
    'window_state': 'window_state.json'
}
