# !/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2017/6/6 16:24
# <AUTHOR> <PERSON><PERSON>

import smtplib
from email.mime.text import MIMEText
from email.utils import formataddr
from typing import List, Optional, Union


class EmailSender:
    """邮件发送类"""

    def __init__(self, sender: str, password: str, smtp_server: str = "smtp.126.com", smtp_port: int = 25):
        """
        初始化邮件发送器

        Args:
            sender: 发件人邮箱
            password: 邮箱密码或授权码
            smtp_server: SMTP服务器地址
            smtp_port: SMTP服务器端口
        """
        self.sender = sender
        self.password = password
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port

    def send_mail(self,
                  recipients: Union[str, List[str]],
                  subject: str,
                  content: str,
                  sender_name: str = "Sender",
                  recipient_name: str = "Recipient",
                  content_type: str = 'html') -> bool:
        """
        发送邮件

        Args:
            recipients: 收件人邮箱，可以是单个字符串或字符串列表
            subject: 邮件主题
            content: 邮件内容
            sender_name: 发件人显示名称
            recipient_name: 收件人显示名称
            content_type: 内容类型，'html' 或 'plain'

        Returns:
            bool: 发送成功返回True，失败返回False
        """
        try:
            # 转换收件人格式
            if isinstance(recipients, str):
                recipients = [recipients]

            # 创建邮件对象
            msg = MIMEText(content, content_type, 'utf-8')
            msg['From'] = formataddr([sender_name, self.sender])
            msg['To'] = formataddr([recipient_name, recipients[0]])  # 主收件人
            msg['Subject'] = subject

            # 发送邮件
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.login(self.sender, self.password)
                server.sendmail(self.sender, recipients, msg.as_string())

            return True

        except Exception as e:
            print(f"发送邮件失败: {str(e)}")
            return False


def main():
    # 示例用法
    sender = EmailSender(
        sender='<EMAIL>',
        password='ACgimLtiii9fGhAX'
    )

    content = """
    <p>Python 邮件发送测试...</p>
    <p><a href="http://www.baidu.com">百度</a></p>
    """

    success = sender.send_mail(
        recipients='<EMAIL>',
        subject='第一个测试',
        content=content,
        sender_name='ronle',
        recipient_name='cheng'
    )

    print("ok" if success else "failed")


if __name__ == "__main__":
    main()
