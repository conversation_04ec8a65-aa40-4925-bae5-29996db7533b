#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片拼接工具 - PySide6现代化版本
基于ocr_qt.py的设计风格，实现UI美化和BUG修复
"""

import sys
import os
import time
import json
import threading
import math
from pathlib import Path
from typing import Optional, List

from PySide6.QtCore import (Qt, QThread, Signal, Slot, QObject, QTimer,
                           QPropertyAnimation, QEasingCurve, QPointF, QElapsedTimer)
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QFileDialog, QSplitter, QScrollArea, QMessageBox, QListWidget,
    QListWidgetItem, QProgressBar, QDialog, QGraphicsDropShadowEffect,
    QGraphicsOpacityEffect, QComboBox, QSpinBox, Q<PERSON>lider, QCheckBox
)
from PySide6.QtGui import (QPixmap, QPainter, QGuiApplication, QColor, QAction, 
                          QKeySequence, QFont, QIcon)

from PIL import Image
import gc

# 复用ocr_qt.py的现代化样式表
STYLE_SHEET = """
QWidget {
    font-family: 'Microsoft YaHei', 'Segoe UI', 'SF Pro Display', Arial, sans-serif;
    font-size: 9pt;
    color: #2c3e50;
    selection-background-color: #e3f2fd;
}

QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
}

QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1a73e8, stop:1 #1557b0);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 5px;
    font-weight: 500;
    min-width: 60px;
    min-height: 26px;
    font-size: 9pt;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1557b0, stop:1 #0d47a1);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #0d47a1, stop:1 #1557b0);
}

QPushButton:disabled {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e9ecef, stop:1 #dee2e6);
    color: #adb5bd;
    border: 1px solid #dee2e6;
}

QScrollArea {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
}

QListWidget {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 8px;
    font-size: 10pt;
}

QListWidget::item {
    padding: 8px;
    border-radius: 6px;
    margin: 2px;
}

QListWidget::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e3f2fd, stop:1 #bbdefb);
    color: #1565c0;
}

QSplitter::handle {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #dee2e6, stop:0.5 #adb5bd, stop:1 #dee2e6);
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:hover {
    background: #2196f3;
}

QLabel {
    color: #495057;
}

QProgressBar {
    border: none;
    border-radius: 8px;
    background-color: #e9ecef;
    height: 16px;
    text-align: center;
    color: #495057;
    font-weight: 600;
}

QProgressBar::chunk {
    border-radius: 8px;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #4CAF50, stop:0.5 #2196F3, stop:1 #9C27B0);
}

QComboBox {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 4px 8px;
    min-width: 80px;
    min-height: 20px;
    font-size: 9pt;
    color: #495057;
}

QComboBox:hover {
    border: 1px solid #2196f3;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
}

QComboBox:focus {
    border: 2px solid #1a73e8;
    background: white;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #dee2e6;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
}

QComboBox::down-arrow {
    image: none;
    border: 2px solid #6c757d;
    width: 6px;
    height: 6px;
    border-top: none;
    border-left: none;
    margin-top: -2px;
    transform: rotate(45deg);
}

QComboBox QAbstractItemView {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    selection-background-color: #e3f2fd;
    selection-color: #1565c0;
    padding: 4px;
}

QSpinBox {
    background: white;
    border: 2px solid #dee2e6;
    border-radius: 4px;
    padding: 4px 6px;
    min-width: 60px;
    min-height: 22px;
    font-size: 9pt;
    color: #495057;
    font-weight: 500;
}

QSpinBox:hover {
    border: 2px solid #1a73e8;
    background: white;
}

QSpinBox:focus {
    border: 2px solid #1a73e8;
    background: white;
}

QSpinBox:disabled {
    background: #f8f9fa;
    color: #adb5bd;
    border: 2px solid #e9ecef;
}

QSpinBox::up-button, QSpinBox::down-button {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
    border: 1px solid #dee2e6;
    width: 16px;
    height: 12px;
}

QSpinBox::up-button {
    subcontrol-origin: border;
    subcontrol-position: top right;
    border-bottom: none;
    border-top-right-radius: 6px;
}

QSpinBox::down-button {
    subcontrol-origin: border;
    subcontrol-position: bottom right;
    border-top: none;
    border-bottom-right-radius: 6px;
}

QSpinBox::up-button:hover, QSpinBox::down-button:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e3f2fd, stop:1 #bbdefb);
}

QSpinBox::up-arrow {
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 6px solid #6c757d;
}

QSpinBox::down-arrow {
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #6c757d;
}

QSlider::groove:horizontal {
    border: 1px solid #dee2e6;
    height: 6px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1a73e8, stop:1 #1557b0);
    border: 1px solid #1557b0;
    width: 16px;
    height: 16px;
    margin: -6px 0;
    border-radius: 8px;
}

QSlider::handle:horizontal:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1557b0, stop:1 #0d47a1);
}

QSlider::handle:horizontal:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #0d47a1, stop:1 #1557b0);
}

QCheckBox {
    color: #495057;
    font-size: 9pt;
    spacing: 6px;
}

QCheckBox::indicator {
    width: 14px;
    height: 14px;
    border: 2px solid #dee2e6;
    border-radius: 3px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
}

QCheckBox::indicator:hover {
    border: 2px solid #2196f3;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
}

QCheckBox::indicator:checked {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1a73e8, stop:1 #1557b0);
    border: 2px solid #1557b0;
    image: none;
}

/* 移除不支持的content属性，使用image替代 */

"""

class ConfigManager:
    """配置管理器 - 参考像素影子处理工具的简单JSON配置模式"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = Path(__file__).parent / config_file
        self.config = self.load_config()
    
    def load_config(self) -> dict:
        """加载配置文件"""
        # 默认配置
        default_config = {
            "paths": {
                "input_path": "",
                "output_path": ""
            },
            "ui": {
                "theme": "Light",
                "window_width": 1000,
                "window_height": 700
            },
            "processing": {
                "image_quality": 95,
                "save_quality": 85,
                "smart_format": True,
                "force_rgb": False,
                "transparency_threshold": 0.01,
                "alignment": "center",
                "max_image_size": 4096,
                "splice_mode": "vertical",
                "grid_columns": 5,
                "png_palette_optimization": True,
                "png_max_colors": 256,
                "png_quantize_method": "mediancut",
                "png_dither": False
            }
        }

        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)

                # 配置升级：确保所有新配置项都存在
                config_updated = False

                # 检查并添加缺失的配置项
                for section, section_data in default_config.items():
                    if section not in loaded_config:
                        loaded_config[section] = section_data
                        config_updated = True
                    elif isinstance(section_data, dict):
                        for key, value in section_data.items():
                            if key not in loaded_config[section]:
                                loaded_config[section][key] = value
                                config_updated = True

                # 如果配置有更新，保存到文件
                if config_updated:
                    self.save_config(loaded_config)
                    print("配置文件已自动升级，添加了新的配置项")

                return loaded_config
            else:
                # 创建新的配置文件
                self.save_config(default_config)
                return default_config
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return {
                "paths": {"input_path": "", "output_path": ""},
                "ui": {"theme": "Light", "window_width": 1000, "window_height": 700},
                "processing": {
                    "image_quality": 95,
                    "save_quality": 85,
                    "smart_format": True,
                    "force_rgb": False,
                    "transparency_threshold": 0.01,
                    "alignment": "center",
                    "max_image_size": 4096,
                    "splice_mode": "vertical",
                    "grid_columns": 5,
                    "png_palette_optimization": True,
                    "png_max_colors": 256,
                    "png_quantize_method": "mediancut",
                    "png_dither": False
                }
            }
    
    def save_config(self, config: dict = None) -> None:
        """保存配置文件"""
        try:
            config_to_save = config or self.config
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get(self, key_path: str, default=None):
        """获取配置值，支持点分隔的路径"""
        keys = key_path.split('.')
        value = self.config
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value
    
    def set(self, key_path: str, value) -> None:
        """设置配置值，支持点分隔的路径"""
        keys = key_path.split('.')
        config = self.config
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        config[keys[-1]] = value
        self.save_config()

class DragDropArea(QLabel):
    """拖拽区域 - 参考ocr_qt.py的拖拽实现"""
    
    folder_dropped = Signal(str)  # 文件夹拖拽信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setAlignment(Qt.AlignCenter)
        self.setMinimumHeight(200)
        
        # 设置默认样式和提示文本
        self.setStyleSheet("""
            QLabel {
                color: #6c757d;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 2px dashed #dee2e6;
                border-radius: 12px;
                font-family: "Microsoft YaHei", "Segoe UI";
                font-size: 14px;
                padding: 20px;
            }
        """)
        
        self.default_text = (
            "📁 拖拽文件夹到此处\n\n"
            "✨ 支持的操作：\n"
            "• 🖱️ 拖放包含图片的文件夹\n"
            "• 📂 点击\"选择文件夹\"按钮\n\n"
            "🖼️ 支持的图片格式：\n"
            "• PNG、JPG、JPEG、BMP\n"
            "• 自动按文件名排序拼接"
        )
        self.setText(self.default_text)
    
    def dragEnterEvent(self, event):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if len(urls) == 1 and urls[0].isLocalFile():
                file_path = urls[0].toLocalFile()
                if os.path.isdir(file_path):
                    event.acceptProposedAction()
                    # 添加视觉反馈
                    self.setStyleSheet("""
                        QLabel {
                            color: #1a73e8;
                            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #e3f2fd, stop:1 #bbdefb);
                            border: 2px solid #2196f3;
                            border-radius: 12px;
                            font-family: "Microsoft YaHei", "Segoe UI";
                            font-size: 14px;
                            padding: 20px;
                        }
                    """)
                    return
        event.ignore()
    
    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        # 恢复默认样式
        self.setStyleSheet("""
            QLabel {
                color: #6c757d;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 2px dashed #dee2e6;
                border-radius: 12px;
                font-family: "Microsoft YaHei", "Segoe UI";
                font-size: 14px;
                padding: 20px;
            }
        """)
    
    def dropEvent(self, event):
        """拖拽放下事件"""
        urls = event.mimeData().urls()
        if urls and urls[0].isLocalFile():
            folder_path = urls[0].toLocalFile()
            if os.path.isdir(folder_path):
                self.folder_dropped.emit(folder_path)
                event.acceptProposedAction()
        
        # 恢复默认样式
        self.dragLeaveEvent(event)

class ImageSplicerApp(QMainWindow):
    """图片拼接工具主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("图片拼接工具")
        
        # 初始化配置管理器
        self.config_manager = ConfigManager()
        
        # 初始化变量
        self.current_folder = ""
        self.image_files = []
        self.loading_dialog = None
        self.thread = None
        self.worker = None
        
        # 设置窗口
        self._setup_window()
        
        # 创建UI
        self._create_widgets()
        self._create_layouts()
        self._create_connections()
        
        # 应用样式
        self.setStyleSheet(STYLE_SHEET)

        # 加载配置到UI控件
        self._load_config_to_ui()

        # 居中显示
        self.center_window()
    
    def _setup_window(self):
        """设置窗口属性"""
        # 从配置获取窗口大小
        width = self.config_manager.get('ui.window_width', 1000)
        height = self.config_manager.get('ui.window_height', 700)
        
        self.setGeometry(100, 100, width, height)
        self.setMinimumSize(800, 600)
    
    def center_window(self):
        """窗口居中显示"""
        screen = QGuiApplication.primaryScreen().geometry()
        window_rect = self.geometry()

        x = (screen.width() - window_rect.width()) // 2
        y = (screen.height() - window_rect.height()) // 2

        self.move(x, y)

    def _create_widgets(self):
        """创建所有UI组件"""
        # 中央组件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # 左侧拖拽区域
        self.drag_area = DragDropArea()

        # 图片列表
        self.image_list = QListWidget()
        self.image_list.setMaximumHeight(200)

        # 右侧控制按钮
        self.select_folder_btn = QPushButton("📂 选择文件夹")
        self.select_output_btn = QPushButton("💾 选择输出文件")
        self.start_splice_btn = QPushButton("🚀 开始拼接")
        self.clear_btn = QPushButton("🗑️ 清空")

        # 拼接选项控件组
        self.splice_options_label = QLabel("🔧 拼接选项")
        self.splice_options_label.setStyleSheet("font-weight: bold; color: #495057; margin-top: 8px;")

        # 拼接模式选择
        self.splice_mode_label = QLabel("拼接模式:")
        self.splice_mode_combo = QComboBox()
        self.splice_mode_combo.addItems(["🔽 竖向拼接", "➡️ 横向拼接", "🔲 网格拼接"])
        self.splice_mode_combo.setToolTip("选择图片拼接的方向和模式")

        # 网格列数设置
        self.grid_columns_label = QLabel("每行图片:")
        self.grid_columns_spinbox = QSpinBox()
        self.grid_columns_spinbox.setRange(1, 20)
        self.grid_columns_spinbox.setValue(5)
        self.grid_columns_spinbox.setSuffix(" 个")
        self.grid_columns_spinbox.setToolTip("网格模式下每行显示的图片数量")

        # 对齐方式选择
        self.alignment_label = QLabel("对齐方式:")
        self.alignment_combo = QComboBox()
        self.alignment_combo.addItems(["🎯 居中对齐", "⬅️ 左对齐", "➡️ 右对齐"])
        self.alignment_combo.setToolTip("图片在容器中的对齐方式")

        # 保存质量控制
        self.quality_label = QLabel("保存质量:")
        self.quality_slider = QSlider(Qt.Horizontal)
        self.quality_slider.setRange(60, 95)
        self.quality_slider.setValue(85)
        self.quality_value_label = QLabel("85")
        self.quality_value_label.setMinimumWidth(30)
        self.quality_value_label.setAlignment(Qt.AlignCenter)
        self.quality_slider.setToolTip("调整图片保存质量，数值越高质量越好但文件越大")

        # 格式选择选项
        self.smart_format_checkbox = QCheckBox("智能格式选择")
        self.smart_format_checkbox.setToolTip("根据输入图片格式和透明度需求自动选择最佳输出格式")

        self.force_rgb_checkbox = QCheckBox("强制RGB模式")
        self.force_rgb_checkbox.setToolTip("强制使用RGB模式，忽略透明度，可减少文件大小")

        # PNG调色板优化开关
        self.png_palette_checkbox = QCheckBox("启用PNG调色板优化")
        self.png_palette_checkbox.setToolTip("将PNG图像转换为索引色模式，显著减少文件大小")

        # 最大颜色数选择
        self.png_colors_label = QLabel("最大颜色数:")
        self.png_colors_combo = QComboBox()
        self.png_colors_combo.addItems(["64", "128", "256"])
        self.png_colors_combo.setCurrentText("256")
        self.png_colors_combo.setToolTip("调色板最大颜色数，数值越高质量越好但压缩效果越小")

        # 抖动选项
        self.png_dither_checkbox = QCheckBox("启用抖动")
        self.png_dither_checkbox.setToolTip("使用抖动算法改善颜色过渡，可能增加文件大小")

        # 状态标签
        self.status_label = QLabel("📊 就绪")
        self.stats_label = QLabel("💾 等待选择图片...")

        # 设置按钮提示
        self.select_folder_btn.setToolTip("选择包含图片的文件夹")
        self.select_output_btn.setToolTip("选择输出文件路径")
        self.start_splice_btn.setToolTip("开始拼接图片")
        self.clear_btn.setToolTip("清空所有设置")

        # 初始状态
        self.start_splice_btn.setEnabled(False)

    def _create_layouts(self):
        """组织组件布局"""
        # 左侧布局
        left_layout = QVBoxLayout()
        left_layout.addWidget(QLabel("📁 拖拽区域"))
        left_layout.addWidget(self.drag_area, 1)
        left_layout.addWidget(QLabel("🖼️ 图片列表"))
        left_layout.addWidget(self.image_list)

        left_widget = QWidget()
        left_widget.setLayout(left_layout)

        # 右侧控制面板
        right_layout = QVBoxLayout()
        right_layout.setSpacing(8)
        right_layout.setContentsMargins(12, 12, 12, 12)

        # 文件选择按钮组
        file_buttons_layout = QVBoxLayout()
        file_buttons_layout.setSpacing(6)
        file_buttons_layout.addWidget(self.select_folder_btn)
        file_buttons_layout.addWidget(self.select_output_btn)

        # 拼接选项组
        splice_options_layout = QVBoxLayout()
        splice_options_layout.setSpacing(5)
        splice_options_layout.addWidget(self.splice_options_label)

        # 拼接模式行
        splice_mode_layout = QHBoxLayout()
        splice_mode_layout.setSpacing(6)
        splice_mode_layout.addWidget(self.splice_mode_label)
        splice_mode_layout.addWidget(self.splice_mode_combo, 1)
        splice_options_layout.addLayout(splice_mode_layout)

        # 网格列数行
        grid_columns_layout = QHBoxLayout()
        grid_columns_layout.setSpacing(6)
        grid_columns_layout.addWidget(self.grid_columns_label)
        grid_columns_layout.addWidget(self.grid_columns_spinbox, 1)
        splice_options_layout.addLayout(grid_columns_layout)

        # 对齐方式行
        alignment_layout = QHBoxLayout()
        alignment_layout.setSpacing(6)
        alignment_layout.addWidget(self.alignment_label)
        alignment_layout.addWidget(self.alignment_combo, 1)
        splice_options_layout.addLayout(alignment_layout)

        # 保存质量控制组
        save_options_layout = QVBoxLayout()
        save_options_layout.setSpacing(4)

        # 添加保存选项标签
        save_options_label = QLabel("💾 保存选项")
        save_options_label.setStyleSheet("font-weight: 600; color: #495057; margin: 6px 0 3px 0; font-size: 9pt;")
        save_options_layout.addWidget(save_options_label)

        # 质量控制行
        quality_layout = QHBoxLayout()
        quality_layout.setSpacing(6)
        quality_layout.addWidget(self.quality_label)
        quality_layout.addWidget(self.quality_slider, 1)
        quality_layout.addWidget(self.quality_value_label)
        save_options_layout.addLayout(quality_layout)

        # 格式选项行
        format_layout = QVBoxLayout()
        format_layout.setSpacing(3)
        format_layout.addWidget(self.smart_format_checkbox)
        format_layout.addWidget(self.force_rgb_checkbox)
        save_options_layout.addLayout(format_layout)

        # 添加PNG优化分隔符
        png_separator = QLabel("🎨 PNG优化")
        png_separator.setStyleSheet("font-weight: 500; color: #6c757d; margin: 4px 0 2px 0; font-size: 8pt;")
        save_options_layout.addWidget(png_separator)

        # PNG优化选项 - 直接集成到保存选项中
        save_options_layout.addWidget(self.png_palette_checkbox)

        # 颜色数选择 - 紧凑的水平布局
        colors_layout = QHBoxLayout()
        colors_layout.setSpacing(4)
        colors_layout.addWidget(self.png_colors_label)
        colors_layout.addWidget(self.png_colors_combo)
        colors_layout.addStretch()
        save_options_layout.addLayout(colors_layout)

        save_options_layout.addWidget(self.png_dither_checkbox)

        # 操作按钮组
        action_buttons_layout = QHBoxLayout()
        action_buttons_layout.setSpacing(8)
        action_buttons_layout.addWidget(self.start_splice_btn)
        action_buttons_layout.addWidget(self.clear_btn)

        # 状态信息
        status_layout = QVBoxLayout()
        status_layout.setSpacing(5)
        status_layout.addWidget(self.status_label)
        status_layout.addWidget(self.stats_label)

        # 组装右侧布局
        right_layout.addLayout(file_buttons_layout)
        right_layout.addLayout(splice_options_layout)
        right_layout.addLayout(save_options_layout)
        right_layout.addLayout(action_buttons_layout)
        right_layout.addStretch()
        right_layout.addLayout(status_layout)

        right_widget = QWidget()
        right_widget.setLayout(right_layout)
        right_widget.setMaximumWidth(400)  # 进一步增加宽度
        right_widget.setMinimumWidth(350)

        # 主分割器
        self.splitter = QSplitter(Qt.Horizontal)
        self.splitter.addWidget(left_widget)
        self.splitter.addWidget(right_widget)
        self.splitter.setSizes([600, 400])  # 进一步调整比例，给右侧面板更多空间

        # 主布局
        main_layout = QHBoxLayout(self.central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.addWidget(self.splitter)

    def _create_connections(self):
        """连接信号和槽"""
        self.drag_area.folder_dropped.connect(self.on_folder_selected)
        self.select_folder_btn.clicked.connect(self.select_input_folder)
        self.select_output_btn.clicked.connect(self.select_output_file)
        self.start_splice_btn.clicked.connect(self.start_splicing)
        self.clear_btn.clicked.connect(self.clear_all)

        # 拼接选项信号连接
        self.splice_mode_combo.currentIndexChanged.connect(self.on_splice_mode_changed)
        self.grid_columns_spinbox.valueChanged.connect(self.on_grid_columns_changed)
        self.alignment_combo.currentIndexChanged.connect(self.on_alignment_changed)

        # 保存选项信号连接
        self.quality_slider.valueChanged.connect(self.on_quality_changed)
        self.smart_format_checkbox.toggled.connect(self.on_smart_format_changed)
        self.force_rgb_checkbox.toggled.connect(self.on_force_rgb_changed)

        # PNG优化选项信号连接
        self.png_palette_checkbox.toggled.connect(self.on_png_palette_changed)
        self.png_colors_combo.currentTextChanged.connect(self.on_png_colors_changed)
        self.png_dither_checkbox.toggled.connect(self.on_png_dither_changed)

    def on_folder_selected(self, folder_path: str):
        """处理文件夹选择"""
        self.current_folder = folder_path
        self.config_manager.set('paths.input_path', folder_path)

        # 扫描图片文件
        self.scan_images()

        # 更新拖拽区域显示
        self.drag_area.setText(f"📁 已选择文件夹:\n{os.path.basename(folder_path)}\n\n找到 {len(self.image_files)} 个图片文件")

    def select_input_folder(self):
        """选择输入文件夹"""
        folder = QFileDialog.getExistingDirectory(
            self,
            "选择包含图片的文件夹",
            self.config_manager.get('paths.input_path', '')
        )
        if folder:
            self.on_folder_selected(folder)

    def select_output_file(self):
        """选择输出文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "选择输出文件",
            self.config_manager.get('paths.output_path', ''),
            "PNG files (*.png);;JPEG files (*.jpg);;All files (*.*)"
        )
        if file_path:
            self.config_manager.set('paths.output_path', file_path)
            self.check_ready_state()

    def scan_images(self):
        """扫描文件夹中的图片文件"""
        if not self.current_folder:
            return

        # 支持的图片格式
        image_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.gif', '.webp'}

        self.image_files = []
        valid_files = []
        invalid_files = []

        try:
            # 第一步：收集所有可能的图片文件
            potential_files = []
            for file_path in Path(self.current_folder).iterdir():
                if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                    potential_files.append(file_path)

            # 第二步：验证每个文件是否为有效图片
            for file_path in potential_files:
                try:
                    # 尝试打开图片验证其有效性
                    with Image.open(file_path) as test_img:
                        # 验证图片基本信息
                        if test_img.size[0] > 0 and test_img.size[1] > 0:
                            valid_files.append(file_path)
                        else:
                            invalid_files.append(file_path.name)
                except Exception as e:
                    # 记录无效文件
                    invalid_files.append(f"{file_path.name} ({str(e)})")
                    continue

            self.image_files = valid_files

            # 按文件名排序
            self.image_files.sort(key=lambda x: x.name.lower())

            # 更新图片列表显示
            self.update_image_list()

            # 更新状态
            status_text = f"💾 找到 {len(self.image_files)} 个有效图片文件"
            if invalid_files:
                status_text += f"，跳过 {len(invalid_files)} 个无效文件"
            self.stats_label.setText(status_text)

            # 如果有无效文件，在控制台输出详细信息
            if invalid_files:
                print(f"跳过的无效文件: {', '.join(invalid_files[:5])}")
                if len(invalid_files) > 5:
                    print(f"... 还有 {len(invalid_files) - 5} 个无效文件")

            # 检查是否可以开始拼接
            self.check_ready_state()

        except Exception as e:
            QMessageBox.warning(self, "错误", f"扫描文件夹时出错：{str(e)}")

    def update_image_list(self):
        """更新图片列表显示"""
        self.image_list.clear()

        for img_file in self.image_files:
            item = QListWidgetItem(f"📷 {img_file.name}")
            item.setToolTip(str(img_file))
            self.image_list.addItem(item)

    def check_ready_state(self):
        """检查是否可以开始拼接"""
        has_images = len(self.image_files) > 0
        has_output = bool(self.config_manager.get('paths.output_path'))

        self.start_splice_btn.setEnabled(has_images and has_output)

        if has_images and has_output:
            self.status_label.setText("✅ 准备就绪")
        elif not has_images:
            self.status_label.setText("⚠️ 请选择包含图片的文件夹")
        elif not has_output:
            self.status_label.setText("⚠️ 请选择输出文件路径")

    def clear_all(self):
        """清空所有设置"""
        self.current_folder = ""
        self.image_files = []

        self.image_list.clear()
        self.drag_area.setText(self.drag_area.default_text)

        self.status_label.setText("📊 已清空")
        self.stats_label.setText("💾 等待选择图片...")

        self.start_splice_btn.setEnabled(False)

    def start_splicing(self):
        """开始拼接图片"""
        if not self.image_files:
            QMessageBox.warning(self, "错误", "请先选择包含图片的文件夹")
            return

        output_path = self.config_manager.get('paths.output_path')
        if not output_path:
            QMessageBox.warning(self, "错误", "请先选择输出文件路径")
            return

        # 显示进度对话框
        self.loading_dialog = QLoadingDialog(
            self,
            message=f"🖼️ 正在拼接 {len(self.image_files)} 个图片...\n\n请稍候，这可能需要一些时间",
            show_progress=True
        )
        self.loading_dialog.show()

        # 禁用按钮
        self.start_splice_btn.setEnabled(False)
        self.select_folder_btn.setEnabled(False)
        self.select_output_btn.setEnabled(False)

        # 获取拼接参数
        splice_mode_index = self.splice_mode_combo.currentIndex()
        splice_modes = ['vertical', 'horizontal', 'grid']
        splice_mode = splice_modes[splice_mode_index]

        grid_columns = self.grid_columns_spinbox.value()

        alignment_index = self.alignment_combo.currentIndex()
        alignments = ['center', 'left', 'right']
        alignment = alignments[alignment_index]

        # 启动工作线程
        self.thread = QThread()
        self.worker = SpliceWorker(
            self.image_files,
            output_path,
            self.config_manager,
            splice_mode=splice_mode,
            grid_columns=grid_columns,
            alignment=alignment
        )
        self.worker.moveToThread(self.thread)

        # 连接信号
        self.thread.started.connect(self.worker.run)
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.status_updated.connect(self.update_status)
        self.worker.finished.connect(self.on_splicing_finished)

        self.thread.start()

    def update_progress(self, value: int, message: str):
        """更新进度"""
        if self.loading_dialog:
            self.loading_dialog.set_progress(value)
            self.loading_dialog.update_message(message)

    def update_status(self, message: str):
        """更新状态"""
        self.stats_label.setText(message)

    def on_splicing_finished(self, success: bool, message: str):
        """拼接完成处理"""
        # 关闭进度对话框
        if self.loading_dialog:
            self.loading_dialog.close()
            self.loading_dialog = None

        # 清理线程
        if self.thread:
            self.thread.quit()
            self.thread.wait()
            self.thread = None
            self.worker = None

        # 恢复按钮状态
        self.select_folder_btn.setEnabled(True)
        self.select_output_btn.setEnabled(True)
        self.check_ready_state()

        # 显示结果
        if success:
            QMessageBox.information(self, "成功", f"✅ {message}")
            self.status_label.setText("✅ 拼接完成")
        else:
            QMessageBox.critical(self, "错误", f"❌ {message}")
            self.status_label.setText("❌ 拼接失败")

    def _load_config_to_ui(self):
        """从配置文件加载设置到UI控件"""
        # 加载拼接模式
        splice_mode = self.config_manager.get('processing.splice_mode', 'vertical')
        mode_index = {'vertical': 0, 'horizontal': 1, 'grid': 2}.get(splice_mode, 0)
        self.splice_mode_combo.setCurrentIndex(mode_index)

        # 加载网格列数
        grid_columns = self.config_manager.get('processing.grid_columns', 5)
        self.grid_columns_spinbox.setValue(grid_columns)

        # 加载对齐方式
        alignment = self.config_manager.get('processing.alignment', 'center')
        alignment_index = {'center': 0, 'left': 1, 'right': 2}.get(alignment, 0)
        self.alignment_combo.setCurrentIndex(alignment_index)

        # 网格列数控件应该随时可用，不依赖于拼接模式
        self.grid_columns_spinbox.setEnabled(True)
        self.grid_columns_label.setEnabled(True)

        # 加载保存质量
        save_quality = self.config_manager.get('processing.save_quality', 85)
        self.quality_slider.setValue(save_quality)
        self.quality_value_label.setText(str(save_quality))

        # 加载格式选项
        smart_format = self.config_manager.get('processing.smart_format', True)
        self.smart_format_checkbox.setChecked(smart_format)

        force_rgb = self.config_manager.get('processing.force_rgb', False)
        self.force_rgb_checkbox.setChecked(force_rgb)

        # 加载PNG优化设置
        png_palette_enabled = self.config_manager.get('processing.png_palette_optimization', True)
        self.png_palette_checkbox.setChecked(png_palette_enabled)

        png_max_colors = self.config_manager.get('processing.png_max_colors', 256)
        self.png_colors_combo.setCurrentText(str(png_max_colors))

        png_dither = self.config_manager.get('processing.png_dither', False)
        self.png_dither_checkbox.setChecked(png_dither)

        # 根据PNG优化开关设置相关控件的启用状态
        self.png_colors_combo.setEnabled(png_palette_enabled)
        self.png_colors_label.setEnabled(png_palette_enabled)
        self.png_dither_checkbox.setEnabled(png_palette_enabled)

    def on_splice_mode_changed(self, index):
        """拼接模式变化处理"""
        modes = ['vertical', 'horizontal', 'grid']
        selected_mode = modes[index]

        # 保存到配置
        self.config_manager.set('processing.splice_mode', selected_mode)

        # 网格列数控件保持随时可用，不受拼接模式影响
        # 移除了原来的启用/禁用逻辑，因为用户希望随时可以设置每行图片数

        # 更新提示信息
        if selected_mode == 'vertical':
            self.splice_mode_combo.setToolTip("竖向拼接：图片从上到下排列")
        elif selected_mode == 'horizontal':
            self.splice_mode_combo.setToolTip("横向拼接：图片从左到右排列")
        else:  # grid
            self.splice_mode_combo.setToolTip("网格拼接：图片按网格排列，可设置每行图片数量")

    def on_grid_columns_changed(self, value):
        """网格列数变化处理"""
        self.config_manager.set('processing.grid_columns', value)

        # 更新提示信息
        self.grid_columns_spinbox.setToolTip(f"网格模式下每行显示 {value} 个图片")

    def on_alignment_changed(self, index):
        """对齐方式变化处理"""
        alignments = ['center', 'left', 'right']
        selected_alignment = alignments[index]

        # 保存到配置
        self.config_manager.set('processing.alignment', selected_alignment)

        # 更新提示信息
        alignment_tips = {
            'center': "居中对齐：图片在容器中居中显示",
            'left': "左对齐：图片在容器中左对齐显示",
            'right': "右对齐：图片在容器中右对齐显示"
        }
        self.alignment_combo.setToolTip(alignment_tips[selected_alignment])

    def on_quality_changed(self, value):
        """保存质量变化处理"""
        self.quality_value_label.setText(str(value))
        self.config_manager.set('processing.save_quality', value)

        # 更新提示信息
        self.quality_slider.setToolTip(f"当前质量: {value}% - 数值越高质量越好但文件越大")

    def on_smart_format_changed(self, checked):
        """智能格式选择变化处理"""
        self.config_manager.set('processing.smart_format', checked)

        # 更新提示信息
        if checked:
            self.smart_format_checkbox.setToolTip("已启用智能格式选择 - 根据输入图片和透明度需求自动选择最佳格式")
        else:
            self.smart_format_checkbox.setToolTip("已禁用智能格式选择 - 使用用户指定的输出格式")

    def on_force_rgb_changed(self, checked):
        """强制RGB模式变化处理"""
        self.config_manager.set('processing.force_rgb', checked)

        # 更新提示信息
        if checked:
            self.force_rgb_checkbox.setToolTip("已启用强制RGB模式 - 忽略透明度，可显著减少文件大小")
        else:
            self.force_rgb_checkbox.setToolTip("已禁用强制RGB模式 - 保持透明度支持")

    def on_png_palette_changed(self, checked):
        """PNG调色板优化开关变化处理"""
        self.config_manager.set('processing.png_palette_optimization', checked)

        # 更新相关控件的启用状态
        self.png_colors_combo.setEnabled(checked)
        self.png_colors_label.setEnabled(checked)
        self.png_dither_checkbox.setEnabled(checked)

        # 更新工具提示
        if checked:
            self.png_palette_checkbox.setToolTip("已启用PNG调色板优化 - 将显著减少PNG文件大小")
        else:
            self.png_palette_checkbox.setToolTip("已禁用PNG调色板优化 - 保持原始颜色模式")

    def on_png_colors_changed(self, text):
        """PNG最大颜色数变化处理"""
        try:
            colors = int(text)
            self.config_manager.set('processing.png_max_colors', colors)
            self.png_colors_combo.setToolTip(f"当前最大颜色数: {colors} - 数值越高质量越好但压缩效果越小")
        except ValueError:
            pass

    def on_png_dither_changed(self, checked):
        """PNG抖动选项变化处理"""
        self.config_manager.set('processing.png_dither', checked)

        if checked:
            self.png_dither_checkbox.setToolTip("已启用抖动 - 改善颜色过渡但可能增加文件大小")
        else:
            self.png_dither_checkbox.setToolTip("已禁用抖动 - 更好的压缩效果")


# 复用ocr_qt.py的QLoadingDialog类
class QLoadingDialog(QDialog):
    """现代化加载对话框 - 直接复用ocr_qt.py的实现"""
    def __init__(self, parent=None, message="正在加载...", show_progress=False):
        super().__init__(parent)
        self.setWindowTitle("请稍候")
        self.setModal(True)
        self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # 创建主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 创建内容容器
        self.container = QWidget(self)
        self.container.setObjectName("container")
        container_layout = QVBoxLayout(self.container)
        container_layout.setContentsMargins(40, 40, 40, 40)
        container_layout.setSpacing(25)

        # 添加加载动画
        self.spinner = QLabel()
        self.spinner.setFixedSize(64, 64)
        self.spinner.setObjectName("spinner")
        container_layout.addWidget(self.spinner, 0, Qt.AlignCenter)

        # 添加加载提示文本
        self.label = QLabel(message)
        self.label.setAlignment(Qt.AlignCenter)
        self.label.setObjectName("message")
        self.label.setWordWrap(True)
        container_layout.addWidget(self.label)

        # 可选的进度条
        self.progress_bar = None
        if show_progress:
            self.progress_bar = QProgressBar()
            self.progress_bar.setObjectName("progressBar")
            self.progress_bar.setRange(0, 100)
            container_layout.addWidget(self.progress_bar)

        layout.addWidget(self.container)
        self.setFixedSize(450, 250 if show_progress else 220)

        # 现代化样式
        self.setStyleSheet("""
            QDialog {
                background-color: rgba(0, 0, 0, 180);
            }
            QWidget#container {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-radius: 15px;
                border: 1px solid rgba(255, 255, 255, 0.8);
            }
            QLabel#message {
                font-family: "Microsoft YaHei", "Segoe UI";
                font-size: 15px;
                font-weight: 500;
                color: #2c3e50;
                padding: 10px;
                background: transparent;
            }
            QLabel#spinner {
                background: transparent;
            }
            QProgressBar#progressBar {
                border: none;
                border-radius: 8px;
                background-color: #e9ecef;
                height: 8px;
                text-align: center;
            }
            QProgressBar#progressBar::chunk {
                border-radius: 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #2196F3);
            }
        """)

        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 5)
        self.container.setGraphicsEffect(shadow)

        self.start_animation()

    def start_animation(self):
        """启动现代化加载动画"""
        self.rotation = 0
        self.animation_timer = QTimer(self)
        self.animation_timer.timeout.connect(self.update_rotation)
        self.animation_timer.start(16)  # 60 FPS

    def update_rotation(self):
        """更新旋转角度"""
        self.rotation = (self.rotation + 6) % 360

        # 创建现代化的加载图标
        size = 64
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)

        try:
            painter = QPainter()
            if not painter.begin(pixmap):
                return

            painter.setRenderHint(QPainter.Antialiasing)

            center = QPointF(size/2, size/2)
            radius = size/2 - 8

            # 绘制多个圆点组成的加载动画
            for i in range(8):
                angle = i * 45 + self.rotation
                x = center.x() + radius * 0.7 * math.cos(math.radians(angle))
                y = center.y() + radius * 0.7 * math.sin(math.radians(angle))

                # 计算透明度（创建拖尾效果）
                alpha = max(0.2, 1.0 - (i * 0.12))

                # 设置颜色
                color = QColor("#1a73e8")
                color.setAlphaF(alpha)
                painter.setBrush(color)
                painter.setPen(Qt.NoPen)

                # 绘制圆点
                dot_size = 6 - i * 0.3
                painter.drawEllipse(QPointF(x, y), dot_size, dot_size)

            painter.end()
            self.spinner.setPixmap(pixmap)
        except Exception as e:
            print(f"绘制加载动画时出错: {e}")

    def update_message(self, message):
        """更新加载消息"""
        self.label.setText(message)

    def set_progress(self, value):
        """设置进度值"""
        if self.progress_bar:
            if value < 0:
                self.progress_bar.setRange(0, 0)  # 无限进度
            else:
                self.progress_bar.setRange(0, 100)
                self.progress_bar.setValue(value)

    def showEvent(self, event):
        """重写showEvent以确保对话框在屏幕中央显示"""
        super().showEvent(event)

        # 获取屏幕几何信息
        screen = QGuiApplication.primaryScreen().geometry()

        # 如果有父窗口，相对于父窗口居中
        if self.parent():
            parent_rect = self.parent().geometry()
            x = parent_rect.x() + (parent_rect.width() - self.width()) // 2
            y = parent_rect.y() + (parent_rect.height() - self.height()) // 2

            # 确保对话框不会超出屏幕边界
            x = max(0, min(x, screen.width() - self.width()))
            y = max(0, min(y, screen.height() - self.height()))
        else:
            # 没有父窗口时，在屏幕中央显示
            x = (screen.width() - self.width()) // 2
            y = (screen.height() - self.height()) // 2

        self.move(x, y)

    def closeEvent(self, event):
        """重写closeEvent以停止动画"""
        if hasattr(self, 'animation_timer'):
            self.animation_timer.stop()
        super().closeEvent(event)


class ImageSaveOptimizer:
    """图片保存优化器 - 智能格式分析和保存参数优化"""

    def __init__(self, config_manager):
        """初始化优化器

        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager

    def analyze_input_formats(self, image_files):
        """分析输入图片格式分布

        Args:
            image_files: 图片文件路径列表

        Returns:
            dict: 格式统计信息 {'jpg': count, 'png': count, 'other': count}
        """
        format_stats = {'jpg': 0, 'png': 0, 'other': 0}

        try:
            for file in image_files:
                ext = file.suffix.lower()
                if ext in ['.jpg', '.jpeg']:
                    format_stats['jpg'] += 1
                elif ext == '.png':
                    format_stats['png'] += 1
                else:
                    format_stats['other'] += 1
        except Exception as e:
            print(f"分析输入格式时出错: {e}")

        return format_stats

    def analyze_image_colors(self, image):
        """分析图像颜色数量

        Args:
            image: PIL Image对象

        Returns:
            dict: {'unique_colors': int, 'has_transparency': bool, 'mode': str}
        """
        try:
            # 获取图像模式和尺寸
            mode = image.mode
            width, height = image.size

            # 对于大图片，使用采样分析以提高性能
            if width * height > 1000000:  # 大于100万像素
                # 缩小图片进行颜色分析
                sample_size = 500
                ratio = min(sample_size / width, sample_size / height)
                sample_image = image.resize((int(width * ratio), int(height * ratio)), Image.Resampling.LANCZOS)
                colors = sample_image.getcolors(maxcolors=65536)
            else:
                # 小图片直接分析
                colors = image.getcolors(maxcolors=65536)

            if colors is None:
                # 颜色数超过maxcolors限制
                unique_colors = 65536
            else:
                unique_colors = len(colors)

            # 检测透明度
            has_transparency = mode in ('RGBA', 'LA') or 'transparency' in image.info

            return {
                'unique_colors': unique_colors,
                'has_transparency': has_transparency,
                'mode': mode
            }
        except Exception as e:
            print(f"颜色分析失败: {e}")
            return {'unique_colors': 65536, 'has_transparency': False, 'mode': image.mode}

    def optimize_png_palette(self, image):
        """PNG调色板优化

        Args:
            image: PIL Image对象

        Returns:
            PIL Image对象: 优化后的图像，如果不适合优化则返回原图
        """
        try:
            # 获取配置
            max_colors = self.config_manager.get('processing.png_max_colors', 256)
            enable_optimization = self.config_manager.get('processing.png_palette_optimization', True)
            enable_dither = self.config_manager.get('processing.png_dither', False)

            if not enable_optimization:
                return image

            # 分析图像颜色
            color_info = self.analyze_image_colors(image)

            # 如果颜色数已经很少，不需要优化
            if color_info['unique_colors'] <= max_colors:
                return image

            # 处理透明度
            if color_info['has_transparency'] and image.mode == 'RGBA':
                # 保持透明度的调色板优化
                # 先转换为P模式，保留透明度信息
                alpha = image.split()[-1]
                rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                rgb_image.paste(image, mask=alpha)

                # 对RGB部分进行调色板优化
                if enable_dither:
                    quantized = rgb_image.quantize(colors=max_colors-1, method=Image.Quantize.MEDIANCUT, dither=Image.Dither.FLOYDSTEINBERG)
                else:
                    quantized = rgb_image.quantize(colors=max_colors-1, method=Image.Quantize.MEDIANCUT)

                # 转换为RGBA并恢复透明度
                result = quantized.convert('RGBA')
                result.putalpha(alpha)

                return result
            else:
                # 无透明度的调色板优化
                if image.mode != 'RGB':
                    image = image.convert('RGB')

                if enable_dither:
                    return image.quantize(colors=max_colors, method=Image.Quantize.MEDIANCUT, dither=Image.Dither.FLOYDSTEINBERG)
                else:
                    return image.quantize(colors=max_colors, method=Image.Quantize.MEDIANCUT)

        except Exception as e:
            print(f"PNG调色板优化失败: {e}")
            return image

    def optimize_transparency_detection(self, images_info):
        """优化的透明度检测算法

        实现更精确的透明度检测，平衡准确性和性能：
        1. 快速检查图片模式，跳过不支持透明度的格式
        2. 对RGBA图片进行采样检测，避免全像素扫描
        3. 使用可配置阈值，只有真正需要时才启用透明度
        4. 支持多种透明度类型（RGBA、LA、调色板透明）

        Args:
            images_info: 图片信息列表，每个元素包含 'image' 键

        Returns:
            bool: 是否需要透明度支持
        """
        threshold = self.config_manager.get('processing.transparency_threshold', 0.01)

        # 性能优化：如果阈值为0，则任何透明像素都会触发透明度需求
        strict_mode = threshold <= 0.0

        try:
            for img_info in images_info:
                img = img_info['image']

                # 快速检查：跳过不支持透明度的模式
                if img.mode not in ('RGBA', 'LA', 'P'):
                    continue

                # 处理RGBA模式
                if img.mode == 'RGBA':
                    # 获取alpha通道
                    alpha = img.split()[-1]

                    # 性能优化：使用getextrema()快速检查是否有透明像素
                    alpha_min, alpha_max = alpha.getextrema()

                    # 如果alpha通道全为255，则无透明度
                    if alpha_min == alpha_max == 255:
                        continue

                    # 如果在严格模式下，任何非255的alpha值都触发透明度
                    if strict_mode and alpha_min < 255:
                        return True

                    # 性能优化：对于大图片，使用采样检测而非全像素扫描
                    total_pixels = img.width * img.height
                    if total_pixels > 1000000:  # 大于100万像素时使用采样
                        # 采样检测：每隔一定间隔检查像素
                        sample_step = max(1, total_pixels // 10000)  # 采样约1万个像素
                        alpha_data = alpha.getdata()

                        transparent_count = 0
                        sample_count = 0

                        for i in range(0, total_pixels, sample_step):
                            if alpha_data[i] < 255:
                                transparent_count += 1
                            sample_count += 1

                            # 早期退出：如果采样中透明度比例已经超过阈值
                            if sample_count > 100 and transparent_count / sample_count > threshold:
                                return True

                        # 计算采样的透明度比例
                        if sample_count > 0:
                            transparency_ratio = transparent_count / sample_count
                            if transparency_ratio > threshold:
                                return True
                    else:
                        # 小图片：使用完整像素检测
                        alpha_array = list(alpha.getdata())
                        transparent_pixels = sum(1 for a in alpha_array if a < 255)
                        transparency_ratio = transparent_pixels / total_pixels

                        if transparency_ratio > threshold:
                            return True

                # 处理LA模式（灰度+alpha）
                elif img.mode == 'LA':
                    # LA模式总是有alpha通道，检查是否有实际透明度
                    if strict_mode:
                        return True

                    # 获取alpha通道（LA模式的第二个通道）
                    _, alpha = img.split()
                    alpha_min, alpha_max = alpha.getextrema()

                    if alpha_min < 255:
                        # 有透明像素，进一步检查比例
                        alpha_array = list(alpha.getdata())
                        total_pixels = len(alpha_array)
                        transparent_pixels = sum(1 for a in alpha_array if a < 255)
                        transparency_ratio = transparent_pixels / total_pixels

                        if transparency_ratio > threshold:
                            return True

                # 处理调色板模式的透明度
                elif img.mode == 'P' and 'transparency' in img.info:
                    # 调色板透明度：检查透明色索引的使用情况
                    if strict_mode:
                        return True

                    transparent_index = img.info['transparency']
                    if isinstance(transparent_index, int):
                        # 单一透明色索引
                        img_array = list(img.getdata())
                        total_pixels = len(img_array)
                        transparent_pixels = sum(1 for pixel in img_array if pixel == transparent_index)
                        transparency_ratio = transparent_pixels / total_pixels

                        if transparency_ratio > threshold:
                            return True
                    else:
                        # 多透明色或其他透明度信息
                        return True

        except Exception as e:
            print(f"透明度检测时出错: {e}")
            # 出错时采用保守策略：检查是否有可能的透明度支持
            try:
                for img_info in images_info:
                    img = img_info['image']
                    # 简单检查：有alpha通道或透明度信息就认为需要透明度
                    if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
                        return True
            except:
                # 如果连简单检查都失败，采用最保守策略
                return True

        return False

    def get_optimal_save_params(self, file_ext, quality_level, needs_transparency):
        """获取优化的保存参数

        Args:
            file_ext: 文件扩展名（如 '.jpg', '.png'）
            quality_level: 质量级别 (60-95)
            needs_transparency: 是否需要透明度支持

        Returns:
            dict: 保存参数字典
        """
        try:
            if file_ext in ['.jpg', '.jpeg']:
                # JPEG优化参数：启用优化，禁用渐进式以提高兼容性
                return {
                    'quality': quality_level,
                    'optimize': True,
                    'progressive': False
                }
            elif file_ext == '.png':
                # PNG优化参数：启用优化，标记需要调色板优化
                params = {'optimize': True}

                # 检查是否启用调色板优化
                if self.config_manager.get('processing.png_palette_optimization', True):
                    params['palette_optimize'] = True

                return params
            else:
                # 其他格式使用基础优化
                return {
                    'optimize': True
                }
        except Exception as e:
            print(f"获取保存参数时出错: {e}")
            # 返回安全的默认参数
            if file_ext in ['.jpg', '.jpeg']:
                return {'quality': 85, 'optimize': True}
            else:
                return {'optimize': True}

    def suggest_optimal_format(self, format_stats, needs_transparency):
        """智能格式建议

        Args:
            format_stats: 格式统计信息
            needs_transparency: 是否需要透明度支持

        Returns:
            dict: 建议信息 {'suggested_format': str, 'reason': str}
        """
        try:
            total_files = sum(format_stats.values())
            if total_files == 0:
                return {'suggested_format': 'png', 'reason': '默认建议'}

            jpg_ratio = format_stats['jpg'] / total_files
            png_ratio = format_stats['png'] / total_files

            if needs_transparency:
                return {
                    'suggested_format': 'png',
                    'reason': '需要透明度支持，建议使用PNG格式'
                }
            elif jpg_ratio > 0.7:
                return {
                    'suggested_format': 'jpg',
                    'reason': f'输入图片主要为JPEG格式({jpg_ratio:.0%})，建议使用JPEG输出'
                }
            elif png_ratio > 0.7:
                return {
                    'suggested_format': 'png',
                    'reason': f'输入图片主要为PNG格式({png_ratio:.0%})，建议使用PNG输出'
                }
            else:
                return {
                    'suggested_format': 'jpg',
                    'reason': '混合格式，建议使用JPEG以获得更小的文件大小'
                }
        except Exception as e:
            print(f"生成格式建议时出错: {e}")
            return {'suggested_format': 'jpg', 'reason': '默认建议'}


class SpliceWorker(QObject):
    """图片拼接工作线程 - 参考ocr_qt.py的OcrWorker实现模式"""

    progress_updated = Signal(int, str)  # 进度值，状态消息
    status_updated = Signal(str)         # 状态更新
    finished = Signal(bool, str)         # 成功/失败，消息

    def __init__(self, image_files: List[Path], output_path: str, config_manager, splice_mode: str = 'vertical', grid_columns: int = 5, alignment: str = 'center'):
        super().__init__()
        self.image_files = image_files
        self.output_path = output_path
        self.config_manager = config_manager
        self.splice_mode = splice_mode
        self.grid_columns = grid_columns
        self.alignment = alignment
        self.max_image_size = 4096  # 最大图片尺寸限制
        self.max_canvas_pixels = 500_000_000  # 最大画布像素数（约500M像素）
        self.save_timer = None  # 保存进度计时器
        self.save_start_time = None  # 保存开始时间
        self.needs_transparency = False  # 是否需要透明度支持
        self.original_total_size = 0  # 原图总大小

        # 初始化保存优化器
        self.save_optimizer = ImageSaveOptimizer(config_manager)

    def _check_canvas_size(self, width: int, height: int) -> bool:
        """检查画布尺寸是否合理"""
        total_pixels = width * height
        if total_pixels > self.max_canvas_pixels:
            return False
        return True

    def _estimate_memory_usage(self, width: int, height: int) -> float:
        """估算内存使用量（MB）"""
        # RGBA模式，每像素4字节
        bytes_needed = width * height * 4
        mb_needed = bytes_needed / (1024 * 1024)
        return mb_needed

    def _estimate_file_size(self, image: Image.Image) -> float:
        """估算保存后的文件大小（MB）"""
        width, height = image.size
        total_pixels = width * height

        # 根据图片复杂度估算压缩后大小
        # 这是一个粗略估算，实际大小会根据图片内容变化
        if image.mode == 'RGBA':
            # PNG格式，压缩比通常在3-8倍
            estimated_bytes = total_pixels * 4 / 5  # 假设5倍压缩
        else:
            # JPEG格式，压缩比通常在10-20倍
            estimated_bytes = total_pixels * 3 / 15  # 假设15倍压缩

        return estimated_bytes / (1024 * 1024)

    def _start_save_monitoring(self, estimated_time: float):
        """开始保存进度监控"""
        self.save_start_time = time.time()
        self.save_timer = QTimer()
        self.save_timer.timeout.connect(lambda: self._update_save_progress(estimated_time))
        self.save_timer.start(1000)  # 每秒更新一次

    def _update_save_progress(self, estimated_time: float):
        """更新保存进度"""
        if self.save_start_time:
            elapsed = time.time() - self.save_start_time
            progress_percent = min(99, 97 + (elapsed / estimated_time) * 2)  # 97-99%之间
            self.progress_updated.emit(int(progress_percent), f"正在保存文件... ({elapsed:.0f}秒)")

    def _stop_save_monitoring(self):
        """停止保存进度监控"""
        if self.save_timer:
            self.save_timer.stop()
            self.save_timer = None
        self.save_start_time = None

    def _detect_transparency_needs(self, images_info) -> bool:
        """检测是否需要透明度支持"""
        for img_info in images_info:
            img = img_info['image']
            # 检查是否有alpha通道且包含透明像素
            if img.mode in ('RGBA', 'LA') or 'transparency' in img.info:
                if img.mode == 'RGBA':
                    # 检查alpha通道是否有非255的值
                    alpha = img.split()[-1]
                    if alpha.getextrema()[0] < 255:  # 有透明像素
                        return True
                else:
                    return True
        return False

    def _get_optimal_mode(self, needs_transparency: bool) -> str:
        """获取最佳颜色模式"""
        return 'RGBA' if needs_transparency else 'RGB'

    def _calculate_original_size(self, images_info) -> float:
        """计算原图总大小（MB）"""
        total_size = 0
        for img_info in images_info:
            try:
                file_size = img_info['path'].stat().st_size
                total_size += file_size
            except:
                pass
        return total_size / (1024 * 1024)

    @Slot()
    def run(self):
        """执行图片拼接任务"""
        try:
            # 第一阶段：加载图片并计算尺寸
            self.status_updated.emit("📏 正在加载图片...")
            self.progress_updated.emit(5, "加载图片文件...")

            images_info = []

            for i, img_file in enumerate(self.image_files):
                try:
                    # 更新进度 - 开始加载当前图片
                    progress = 5 + i / len(self.image_files) * 35
                    self.progress_updated.emit(int(progress), f"加载图片 {i+1}/{len(self.image_files)}: {img_file.name}")

                    # 直接加载图片，不使用with语句避免过早关闭
                    img = Image.open(img_file)

                    # 验证图片有效性
                    if img.size[0] <= 0 or img.size[1] <= 0:
                        print(f"跳过无效尺寸图片 {img_file.name}: {img.size}")
                        img.close()
                        continue

                    # 调整图片大小（如果太大）
                    if max(img.size) > self.max_image_size:
                        ratio = self.max_image_size / max(img.size)
                        new_size = tuple(int(dim * ratio) for dim in img.size)
                        print(f"调整图片尺寸 {img_file.name}: {img.size} -> {new_size}")
                        img = img.resize(new_size, Image.Resampling.LANCZOS)

                    # 暂时保持原始模式，稍后统一转换

                    images_info.append({
                        'path': img_file,
                        'image': img,  # 直接使用图片对象，不创建副本
                        'width': img.width,
                        'height': img.height
                    })

                    # 更新进度 - 完成当前图片加载
                    progress = 5 + (i + 1) / len(self.image_files) * 35
                    self.progress_updated.emit(int(progress), f"已加载 {i+1}/{len(self.image_files)} 个图片")

                except Exception as e:
                    print(f"跳过无效图片 {img_file.name}: {e}")
                    # 确保释放可能已打开的图片资源
                    try:
                        if 'img' in locals():
                            img.close()
                    except:
                        pass
                    continue

            if not images_info:
                self.finished.emit(False, "没有找到有效的图片文件")
                return

            self.progress_updated.emit(40, f"成功加载 {len(images_info)} 个图片")

            # 使用优化器分析输入格式
            format_stats = self.save_optimizer.analyze_input_formats(self.image_files)

            # 使用优化器检测透明度
            self.needs_transparency = self.save_optimizer.optimize_transparency_detection(images_info)

            # 获取用户配置
            force_rgb = self.config_manager.get('processing.force_rgb', False)

            # 如果强制RGB模式，覆盖透明度检测
            if force_rgb:
                self.needs_transparency = False

            optimal_mode = self._get_optimal_mode(self.needs_transparency)

            # 计算原图总大小
            self.original_total_size = self._calculate_original_size(images_info)

            self.progress_updated.emit(42, f"分析图片格式... (透明度: {'需要' if self.needs_transparency else '不需要'})")

            # 统一转换图片模式
            for img_info in images_info:
                if img_info['image'].mode != optimal_mode:
                    if optimal_mode == 'RGB' and img_info['image'].mode == 'RGBA':
                        # RGBA转RGB，使用白色背景
                        rgb_img = Image.new('RGB', img_info['image'].size, (255, 255, 255))
                        rgb_img.paste(img_info['image'], mask=img_info['image'].split()[-1] if len(img_info['image'].split()) == 4 else None)
                        img_info['image'].close()
                        img_info['image'] = rgb_img
                    else:
                        img_info['image'] = img_info['image'].convert(optimal_mode)

            # 第二阶段：根据拼接模式执行拼接
            self.status_updated.emit("🎨 正在拼接图片...")
            self.progress_updated.emit(45, f"使用{self.splice_mode}模式拼接... (模式: {optimal_mode})")

            if self.splice_mode == 'vertical':
                result = self._vertical_splice(images_info)
            elif self.splice_mode == 'horizontal':
                result = self._horizontal_splice(images_info)
            elif self.splice_mode == 'grid':
                result = self._grid_splice(images_info, self.grid_columns)
            else:
                # 默认使用竖向拼接
                result = self._vertical_splice(images_info)

            # 第四阶段：保存结果
            self.status_updated.emit("💾 正在保存结果...")
            self.progress_updated.emit(90, "准备保存...")

            # 确保输出目录存在
            output_dir = Path(self.output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            self.progress_updated.emit(92, "创建输出目录...")

            # 保存图片
            self.progress_updated.emit(94, "开始保存拼接结果...")

            # 估算文件大小和保存时间
            estimated_size_mb = self._estimate_file_size(result)
            estimated_time = max(2, estimated_size_mb * 0.5)  # 估算保存时间（秒）

            try:
                # 根据文件扩展名确定保存格式
                file_ext = Path(self.output_path).suffix.lower()

                # 获取用户配置的质量设置
                quality_level = self.config_manager.get('processing.save_quality', 85)

                # 获取优化的保存参数
                save_params = self.save_optimizer.get_optimal_save_params(file_ext, quality_level, self.needs_transparency)

                self.progress_updated.emit(95, f"准备保存 {file_ext.upper()} 格式 (质量: {quality_level}%, 预计大小: {estimated_size_mb:.1f}MB, 预计时间: {estimated_time:.0f}秒)")

                if file_ext in ['.jpg', '.jpeg']:
                    # JPEG不支持透明度，转换为RGB
                    if result.mode == 'RGBA':
                        self.progress_updated.emit(96, "转换RGBA到RGB格式...")
                        # 创建白色背景
                        rgb_result = Image.new('RGB', result.size, (255, 255, 255))

                        # 安全地处理alpha通道
                        try:
                            # 检查是否有alpha通道
                            if len(result.split()) == 4:
                                rgb_result.paste(result, mask=result.split()[3])  # 使用alpha通道作为mask
                            else:
                                rgb_result.paste(result)
                        except Exception as alpha_error:
                            print(f"Alpha通道处理失败，使用简单粘贴: {alpha_error}")
                            # 如果alpha处理失败，直接转换
                            rgb_result = result.convert('RGB')

                        self.progress_updated.emit(97, f"正在保存JPEG文件... (质量: {quality_level}%, 预计需要 {estimated_time:.0f} 秒)")
                        # 开始保存监控
                        self._start_save_monitoring(estimated_time)
                        # 使用优化的保存参数
                        rgb_result.save(self.output_path, 'JPEG', **save_params)
                        # 停止保存监控
                        self._stop_save_monitoring()
                        rgb_result.close()
                    else:
                        self.progress_updated.emit(97, f"正在保存JPEG文件... (质量: {quality_level}%, 预计需要 {estimated_time:.0f} 秒)")
                        # 开始保存监控
                        self._start_save_monitoring(estimated_time)
                        # 使用优化的保存参数
                        result.save(self.output_path, 'JPEG', **save_params)
                        # 停止保存监控
                        self._stop_save_monitoring()
                else:
                    # PNG或其他格式
                    # 在保存PNG之前添加调色板优化
                    if file_ext == '.png' and save_params.get('palette_optimize', False):
                        self.progress_updated.emit(96, "正在进行PNG调色板优化...")
                        result = self.save_optimizer.optimize_png_palette(result)
                        # 移除标识，避免传递给save方法
                        save_params.pop('palette_optimize', None)

                    self.progress_updated.emit(97, f"正在保存{file_ext.upper()}文件... (预计需要 {estimated_time:.0f} 秒)")
                    # 开始保存监控
                    self._start_save_monitoring(estimated_time)
                    # 使用优化的保存参数
                    if file_ext == '.png':
                        result.save(self.output_path, 'PNG', **save_params)
                    else:
                        result.save(self.output_path, **save_params)
                    # 停止保存监控
                    self._stop_save_monitoring()

                self.progress_updated.emit(98, "文件保存成功，正在清理资源...")

                # 获取保存时间信息
                save_time = time.time() - self.save_start_time if self.save_start_time else 0

                # 关闭结果图片
                result.close()

                # 关闭所有输入图片
                for img_info in images_info:
                    if 'image' in img_info and img_info['image']:
                        try:
                            img_info['image'].close()
                        except:
                            pass

                # 强制垃圾回收
                gc.collect()

                self.progress_updated.emit(99, "清理完成，准备结束...")

                # 获取文件大小信息和优化建议
                try:
                    file_size = Path(self.output_path).stat().st_size
                    size_mb = file_size / (1024 * 1024)
                    file_ext = Path(self.output_path).suffix.lower()

                    # 计算压缩比
                    if self.original_total_size > 0:
                        compression_ratio = size_mb / self.original_total_size
                        size_info = f"📊 文件大小信息:\n  输出文件: {size_mb:.1f} MB\n  原图总计: {self.original_total_size:.1f} MB\n  大小比例: {compression_ratio:.1f}x"
                    else:
                        size_info = f"📊 输出文件大小: {size_mb:.1f} MB"

                    if save_time > 0:
                        size_info += f"\n  保存耗时: {save_time:.1f} 秒"

                    # 添加优化效果信息
                    optimization_info = ""
                    if hasattr(self, 'save_optimizer'):
                        quality_used = self.config_manager.get('processing.save_quality', 85)
                        smart_format = self.config_manager.get('processing.smart_format', True)
                        force_rgb = self.config_manager.get('processing.force_rgb', False)

                        optimization_info = f"\n\n🔧 优化设置:\n  保存质量: {quality_used}%\n  透明度支持: {'需要' if self.needs_transparency else '不需要'}"
                        if force_rgb:
                            optimization_info += "\n  强制RGB模式: 已启用"
                        if smart_format:
                            optimization_info += "\n  智能格式选择: 已启用"

                        # 在现有的optimization_info部分添加PNG优化信息
                        if file_ext == '.png' and hasattr(self, 'save_optimizer'):
                            png_palette_enabled = self.config_manager.get('processing.png_palette_optimization', True)
                            if png_palette_enabled:
                                png_max_colors = self.config_manager.get('processing.png_max_colors', 256)
                                png_dither = self.config_manager.get('processing.png_dither', False)

                                optimization_info += f"\n  PNG调色板优化: 已启用"
                                optimization_info += f"\n  最大颜色数: {png_max_colors}"
                                if png_dither:
                                    optimization_info += f"\n  抖动算法: 已启用"

                                # 如果文件大小显著减少，显示优化效果
                                if compression_ratio > 2:
                                    optimization_info += f"\n  🎨 调色板优化效果: 显著减少文件大小"
                            else:
                                optimization_info += f"\n  PNG调色板优化: 已禁用"

                    # 生成智能格式建议
                    suggestion = ""
                    if hasattr(self, 'save_optimizer') and self.config_manager.get('processing.smart_format', True):
                        # 分析输入格式分布
                        format_stats = self.save_optimizer.analyze_input_formats(self.image_files)
                        format_suggestion = self.save_optimizer.suggest_optimal_format(format_stats, self.needs_transparency)

                        current_format = 'jpg' if file_ext in ['.jpg', '.jpeg'] else 'png'
                        suggested_format = format_suggestion['suggested_format']

                        if current_format != suggested_format:
                            suggestion = f"\n\n💡 格式建议:\n  {format_suggestion['reason']}"
                            if suggested_format == 'jpg' and file_ext == '.png' and not self.needs_transparency:
                                if size_mb > self.original_total_size * 1.5:
                                    suggestion += f"\n  使用JPEG格式可能减少约{((size_mb / self.original_total_size - 1) * 50):.0f}%的文件大小"
                        elif file_ext in ['.jpg', '.jpeg'] and self.needs_transparency:
                            suggestion = f"\n\n⚠️ 格式提醒:\n  JPEG不支持透明度，透明部分已转为白色背景\n  如需保持透明度，建议使用PNG格式"

                    # 在suggestion部分添加PNG优化建议
                    if file_ext == '.png' and hasattr(self, 'save_optimizer'):
                        png_palette_enabled = self.config_manager.get('processing.png_palette_optimization', True)
                        if not png_palette_enabled and size_mb > self.original_total_size:
                            suggestion += f"\n\n💡 PNG优化建议:\n  启用PNG调色板优化可能进一步减少文件大小"
                        elif png_palette_enabled and compression_ratio < 1.5:
                            png_max_colors = self.config_manager.get('processing.png_max_colors', 256)
                            if png_max_colors > 128:
                                suggestion += f"\n\n💡 PNG优化建议:\n  尝试减少最大颜色数(当前{png_max_colors})以获得更好的压缩效果"

                    # 添加性能提示
                    performance_tip = ""
                    if compression_ratio > 3:
                        performance_tip = f"\n\n✨ 优化效果: 文件大小显著减少，节省了{((1 - 1/compression_ratio) * 100):.0f}%的存储空间"
                    elif compression_ratio < 0.5:
                        performance_tip = f"\n\n📈 质量保证: 保持了高质量输出，文件大小合理"

                    # 组合完整的大小信息
                    complete_size_info = size_info + optimization_info + suggestion + performance_tip

                except Exception as e:
                    print(f"生成反馈信息时出错: {e}")
                    complete_size_info = f"📊 输出文件: {Path(self.output_path).name}"

                self.progress_updated.emit(100, "拼接完成！")

                success_msg = f"🎉 图片拼接完成！\n\n📋 拼接信息:\n  图片数量: {len(images_info)} 个\n  输出文件: {Path(self.output_path).name}\n  拼接模式: {self.splice_mode}\n\n{complete_size_info}"
                self.finished.emit(True, success_msg)

            except Exception as save_error:
                # 保存失败时的详细错误处理
                error_details = f"保存文件时出错: {str(save_error)}\n输出路径: {self.output_path}\n文件格式: {file_ext}"
                print(f"保存错误详情: {error_details}")
                self.finished.emit(False, error_details)
                return

        except Exception as e:
            # 清理资源
            try:
                if 'result' in locals():
                    result.close()
                if 'images_info' in locals():
                    for img_info in images_info:
                        if 'image' in img_info and img_info['image']:
                            img_info['image'].close()
                gc.collect()
            except:
                pass

            self.finished.emit(False, f"拼接过程中出现错误：{str(e)}")

    def _vertical_splice(self, images_info):
        """竖向拼接算法"""
        print(f"开始竖向拼接，图片数量: {len(images_info)}")

        # 计算总高度和最大宽度
        total_height = sum(img['height'] for img in images_info)
        max_width = max(img['width'] for img in images_info)

        print(f"画布尺寸: {max_width} x {total_height}")

        # 检查画布尺寸和内存使用
        if not self._check_canvas_size(max_width, total_height):
            memory_needed = self._estimate_memory_usage(max_width, total_height)
            raise Exception(f"画布尺寸过大: {max_width} x {total_height}\n预计需要内存: {memory_needed:.1f} MB\n请减少图片数量或尺寸")

        memory_needed = self._estimate_memory_usage(max_width, total_height)
        self.progress_updated.emit(48, f"创建画布: {max_width} x {total_height} (预计内存: {memory_needed:.1f} MB)")

        # 创建结果图片
        try:
            if self.needs_transparency:
                result = Image.new('RGBA', (max_width, total_height), (0, 0, 0, 0))
            else:
                result = Image.new('RGB', (max_width, total_height), (255, 255, 255))
        except Exception as e:
            raise Exception(f"创建画布失败，尺寸可能过大: {max_width} x {total_height}, 错误: {e}")

        current_height = 0
        for i, img_info in enumerate(images_info):
            img = img_info['image']

            # 计算水平对齐位置
            x_offset = self._calculate_horizontal_alignment(img_info, max_width)

            # 粘贴图片
            try:
                if self.needs_transparency and img.mode == 'RGBA':
                    result.paste(img, (x_offset, current_height), img)
                else:
                    result.paste(img, (x_offset, current_height))
                current_height += img.height
            except Exception as e:
                print(f"粘贴图片失败 {img_info['path'].name}: {e}")
                continue

            # 更新进度 - 更细致的进度报告
            progress = 50 + (i + 1) / len(images_info) * 40
            self.progress_updated.emit(int(progress), f"竖向拼接 {i+1}/{len(images_info)}: {img_info['path'].name}")

        return result

    def _horizontal_splice(self, images_info):
        """横向拼接算法"""
        print(f"开始横向拼接，图片数量: {len(images_info)}")

        # 计算总宽度和最大高度
        total_width = sum(img['width'] for img in images_info)
        max_height = max(img['height'] for img in images_info)

        print(f"画布尺寸: {total_width} x {max_height}")

        # 检查画布尺寸和内存使用
        if not self._check_canvas_size(total_width, max_height):
            memory_needed = self._estimate_memory_usage(total_width, max_height)
            raise Exception(f"画布尺寸过大: {total_width} x {max_height}\n预计需要内存: {memory_needed:.1f} MB\n请减少图片数量或尺寸")

        memory_needed = self._estimate_memory_usage(total_width, max_height)
        self.progress_updated.emit(48, f"创建画布: {total_width} x {max_height} (预计内存: {memory_needed:.1f} MB)")

        # 创建结果图片
        try:
            if self.needs_transparency:
                result = Image.new('RGBA', (total_width, max_height), (0, 0, 0, 0))
            else:
                result = Image.new('RGB', (total_width, max_height), (255, 255, 255))
        except Exception as e:
            raise Exception(f"创建画布失败，尺寸可能过大: {total_width} x {max_height}, 错误: {e}")

        current_width = 0
        for i, img_info in enumerate(images_info):
            img = img_info['image']

            # 计算垂直对齐位置
            y_offset = self._calculate_vertical_alignment(img_info, max_height)

            # 粘贴图片
            try:
                if self.needs_transparency and img.mode == 'RGBA':
                    result.paste(img, (current_width, y_offset), img)
                else:
                    result.paste(img, (current_width, y_offset))
                current_width += img.width
            except Exception as e:
                print(f"粘贴图片失败 {img_info['path'].name}: {e}")
                continue

            # 更新进度
            progress = 50 + (i + 1) / len(images_info) * 40
            self.progress_updated.emit(int(progress), f"横向拼接 {i+1}/{len(images_info)}: {img_info['path'].name}")

        return result

    def _grid_splice(self, images_info, columns):
        """网格拼接算法"""
        import math

        print(f"开始网格拼接，图片数量: {len(images_info)}, 列数: {columns}")

        # 计算网格尺寸
        rows = math.ceil(len(images_info) / columns)
        cell_width = max(img['width'] for img in images_info)
        cell_height = max(img['height'] for img in images_info)

        # 创建结果图片
        total_width = cell_width * columns
        total_height = cell_height * rows

        print(f"网格画布尺寸: {total_width} x {total_height} ({rows}行 x {columns}列)")

        # 检查画布尺寸和内存使用
        if not self._check_canvas_size(total_width, total_height):
            memory_needed = self._estimate_memory_usage(total_width, total_height)
            raise Exception(f"网格画布尺寸过大: {total_width} x {total_height}\n预计需要内存: {memory_needed:.1f} MB\n请减少图片数量或尺寸")

        memory_needed = self._estimate_memory_usage(total_width, total_height)
        self.progress_updated.emit(48, f"创建网格画布: {rows}行 x {columns}列 (预计内存: {memory_needed:.1f} MB)")

        try:
            if self.needs_transparency:
                result = Image.new('RGBA', (total_width, total_height), (0, 0, 0, 0))
            else:
                result = Image.new('RGB', (total_width, total_height), (255, 255, 255))
        except Exception as e:
            raise Exception(f"创建网格画布失败，尺寸可能过大: {total_width} x {total_height}, 错误: {e}")

        for i, img_info in enumerate(images_info):
            img = img_info['image']

            # 计算网格位置
            row = i // columns
            col = i % columns

            # 计算单元格内的对齐位置
            x_offset = col * cell_width + self._calculate_horizontal_alignment(img_info, cell_width)
            y_offset = row * cell_height + self._calculate_vertical_alignment(img_info, cell_height)

            # 粘贴图片
            try:
                if self.needs_transparency and img.mode == 'RGBA':
                    result.paste(img, (x_offset, y_offset), img)
                else:
                    result.paste(img, (x_offset, y_offset))
            except Exception as e:
                print(f"粘贴图片失败 {img_info['path'].name}: {e}")
                continue

            # 更新进度
            progress = 50 + (i + 1) / len(images_info) * 40
            self.progress_updated.emit(int(progress), f"网格拼接 {i+1}/{len(images_info)} (第{row+1}行第{col+1}列): {img_info['path'].name}")

        return result

    def _calculate_horizontal_alignment(self, img_info, container_width):
        """计算水平对齐偏移"""
        img_width = img_info['width']
        if self.alignment == 'center':
            return (container_width - img_width) // 2
        elif self.alignment == 'left':
            return 0
        elif self.alignment == 'right':
            return container_width - img_width
        else:
            return (container_width - img_width) // 2  # 默认居中

    def _calculate_vertical_alignment(self, img_info, container_height):
        """计算垂直对齐偏移"""
        img_height = img_info['height']
        if self.alignment == 'center':
            return (container_height - img_height) // 2
        elif self.alignment == 'left':  # 在垂直对齐中，left对应top
            return 0
        elif self.alignment == 'right':  # 在垂直对齐中，right对应bottom
            return container_height - img_height
        else:
            return (container_height - img_height) // 2  # 默认居中


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("图片拼接工具")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("MyTools")

    # 创建主窗口
    window = ImageSplicerApp()
    window.show()

    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
