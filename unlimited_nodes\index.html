
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地订阅转换工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        :root {
            --primary: #3a86ff;
            --secondary: #8338ec;
            --success: #06d6a0;
            --light-bg: #f8f9fa;
            --card-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            padding: 1.5rem 0;
        }
        
        .container {
            max-width: 800px;
        }
        
        .app-header {
            text-align: center;
            margin-bottom: 1.5rem;
        }
        
        .app-title {
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.25rem;
            font-size: 1.75rem;
        }
        
        .app-subtitle {
            color: #6c757d;
            font-size: 0.95rem;
            font-weight: 400;
        }
        
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: var(--card-shadow);
            margin-bottom: 1.25rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            font-weight: 600;
            padding: 0.75rem 1.25rem;
            border-bottom: none;
            border-radius: 8px 8px 0 0 !important;
            font-size: 0.95rem;
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        .form-label {
            font-weight: 500;
            font-size: 0.9rem;
            margin-bottom: 0.35rem;
        }
        
        .form-control {
            border-radius: 6px;
            font-size: 0.9rem;
            padding: 0.5rem 0.75rem;
            border: 1px solid #dee2e6;
        }
        
        .form-control:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(58, 134, 255, 0.25);
        }
        
        .btn {
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: linear-gradient(to right, var(--primary), var(--secondary));
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .alert {
            border-radius: 6px;
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
        }
        
        .result-link {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 0.75rem;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 0.85rem;
            word-break: break-all;
            margin-bottom: 1rem;
            border: 1px solid #eaeaea;
        }
        
        .step {
            display: flex;
            margin-bottom: 1rem;
            align-items: center;
        }
        
        .step-number {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 0.75rem;
            flex-shrink: 0;
        }
        
        .step-content {
            flex-grow: 1;
        }
        
        code {
            background-color: #f1f3f5;
            color: #e83e8c;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="app-header">
            <h1 class="app-title">本地订阅转换工具</h1>
            <p class="app-subtitle">安全、高效地转换您的节点订阅<a style="margin-left: 5px;" href="/free">别名</a><a style="margin-left: 5px;" href="/anhao">暗号</a></p>
        </div>
        
        <div class="card mb-4">
            <div class="card-header d-flex align-items-center">
                <i class="bi bi-info-circle me-2"></i>
                <span>在线订阅转换的问题</span>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>可能导致<code>节点被盗</code></li>
                    <li>转换节点的数量有限制</li>
                    <li>直链平台不好找</li>
                    <li>依赖后端平台稳定性</li>
                </ul>
                <div class="alert alert-success mt-3 mb-0">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <strong>本地订阅转换可解决在线订阅转换存在的问题</strong>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header d-flex align-items-center">
                <i class="bi bi-gear-fill me-2"></i>
                <span>转换工具</span>
            </div>
            <div class="card-body">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        先下载<a href="https://github.com/tindy2013/subconverter/releases" target="_blank" class="fw-bold">订阅转换工具</a>，并运行
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <label for="filepath" class="form-label">节点文件路径：</label>
                        <textarea class="form-control" id="filepath" rows="6" placeholder="支持单个节点、多个节点、直链和本地文件路径"></textarea>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <label for="subtype" class="form-label">将格式转换为：</label>
                        <select class="form-select" id="subtype">
                            <option value="clash">Clash</option>
                            <option value="mixed">Base64通用</option>
                        </select>
                    </div>
                </div>
                
                <div class="d-grid gap-2 mt-4">
                    <button type="button" class="btn btn-primary" onclick="create()">
                        <i class="bi bi-link-45deg me-2"></i>生成链接
                    </button>
                </div>
                
                <div class="mt-4">
                    <label class="form-label">生成的链接：</label>
                    <div class="result-link">
                        <a id="kd" style="pointer-events: none; text-decoration: none;">请先生成链接！</a>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <a href="https://acl4ssr-sub.github.io/" target="_blank" class="btn btn-outline-secondary">
                        <i class="bi bi-globe me-2"></i>在线订阅转换
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script>
    function create() {
        var filepath = document.getElementById('filepath').value;
        var subtype = document.getElementById('subtype').value;
        var kd = document.getElementById('kd');

        if (filepath === "") {
            alert("本地节点文件路径不能为空！");
            return false;
        }

        var url = `http://localhost:25500/sub?target=${subtype}&url=${encodeURIComponent(filepath.replace(/\n/g, "|"))}&insert=false`;

        kd.href = url;
        kd.style = '';
        kd.innerText = url;
        kd.target = '_blank';
    }
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
