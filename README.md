# myTools
自用小工具，不定期更新。

视频音频工具需要用户自己去下载 ffmpeg 并设置好环境变量，方可正常运行程序

ffmpeg 下载地址

https://github.com/BtbN/FFmpeg-Builds/releases

## 工具目录

*   `AugmentAPIKey`: 生成 OAuth 授权 URL，处理 API Key 获取。
*   `DuplicateFileFinder`: 基于文件哈希和感知哈希的重复文件查找工具，带图形界面。
*   `baidu翻译`: 百度翻译 API 封装。
*   `converter`: 多功能转换工具，含自定义 Tkinter UI，涉及汇率转换。
*   `deeplx批量翻译`: 基于 DeepLX API 的批量翻译工具，含重试和延迟机制。
*   `deeplx翻译`: 单次翻译工具，带动画效果的图形界面。
*   `dmitriRender-retime`: 需要管理员权限运行的脚本，可能涉及渲染时间调整。
*   `docker`: 基于 Flask 的小型 Web 服务，抓取特定网站数据。
*   `excel_to_json`: Flask Web 应用，实现 Excel/CSV 文件转换为 JSON。
*   `godot服务器`: 支持 CORS 的简单 HTTP 服务器，用于本地托管文件。
*   `ocr识别`: 基于 PySide6 的 OCR 图形界面应用。
*   `pixel_calculation`: Tkinter 像素计算器工具。
*   `relinggo`: 单词和用户名生成脚本，可能用于自动化注册。
*   `unlimited_nodes`: 使用 Selenium 抓取网络节点数据的脚本。
*   `优选IP`: 抓取并缓存 Cloudflare 优选 IP 的工具。
*   `像素影子处理`: Tkinter 图形界面工具，用于处理像素阴影效果。
*   `加密文本`: 基于密码的文本加密解密工具，使用 `cryptography` 库。
*   `发送邮件`: SMTP 邮件发送封装。
*   `拼接图片`: Tkinter 图形界面工具，实现图片竖向拼接。
*   `提取GIF`: 基于 ttkbootstrap 的 GUI 工具，从 GIF 文件提取帧。
*   `油猴脚本`: 多个浏览器油猴脚本，如抖音无水印视频下载器、智能图片批量下载器等。
*   `网络修复`: AutoIt 脚本，用于清理 DNS 缓存和修复网络。
*   `网络修复加强版`: 使用 PyQt5 构建的网络代理修复和管理工具。
*   `网页程序`: 包含多个网页小游戏和应用，如五子棋、俄罗斯方块等。
*   `获取trae`: 从 Trae 应用的日志文件中提取 ClientID 等信息。
*   `获取内网外网IP`: 使用 Tkinter 构建的图形界面工具，用于查询内外网 IP。
*   `获取女优名字`: 使用 PyQt5 构建的桌面应用，用于查询演员名称。
*   `获取模型`: 使用 PyQt5 构建的图形界面工具，从特定网站抓取模型信息。
*   `获取网页内容`: 包含多个脚本，用于从 API 或网页获取数据。
*   `视频音频相关`: 包含一系列基于 ffmpeg 的视频音频处理工具。
*   `转像素图`: 使用 Tkinter 构建的图形界面工具，将图片转换为像素艺术风格。
*   `重命名工具`: 使用 CustomTkinter 构建的图形界面工具，用于批量重命名文件。