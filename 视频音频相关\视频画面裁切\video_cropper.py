import sys
import cv2
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton,
                           QVBoxLayout, QHBoxLayout, QWidget, QLabel,
                           QFileDialog, QProgressDialog, QSlider,
                           QStyle, QFrame, QSpacerItem, QSizePolicy)
from PyQt5.QtGui import QImage, QPixmap, QPainter, QPen, QFont, QIcon, QColor, QPainterPath, QDragEnterEvent, QDropEvent
from PyQt5.QtCore import Qt, QTimer, QRect, QRectF, QPoint, QSize, QSettings, QTime, QThread, pyqtSignal, QUrl
import os
import tempfile
import subprocess
import platform
import time
import re
import json
import gc
import weakref
from contextlib import contextmanager
from typing import Optional, Dict, Tuple, Any

# 常量定义区
CROP_HANDLE_RADIUS = 8  # 拖拽点半径，适当减小更美观
CROP_HANDLE_HIT_RADIUS = 20  # 手柄判定半径，提升易用性
TIMER_INTERVAL_MS = 33   # 定时器间隔（毫秒）
MIN_CROP_SIZE = 20       # 最小裁剪框尺寸

# 性能优化常量
MAX_CACHE_SIZE = 10      # 最大缓存帧数
FRAME_SKIP_THRESHOLD = 5 # 帧跳过阈值
UPDATE_THROTTLE_MS = 16  # UI更新节流间隔（约60FPS）

# 内存管理常量
GC_INTERVAL_FRAMES = 100 # 垃圾回收间隔帧数

# 支持的视频格式
SUPPORTED_VIDEO_FORMATS = {
    '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm',
    '.m4v', '.3gp', '.ts', '.mts', '.m2ts', '.vob', '.ogv'
}

class PerformanceMonitor:
    """性能监控器"""
    def __init__(self):
        self.frame_times = []
        self.cache_hits = 0
        self.cache_misses = 0
        self.last_gc_time = time.time()

    def record_frame_time(self, frame_time: float):
        """记录帧处理时间"""
        self.frame_times.append(frame_time)
        if len(self.frame_times) > 100:  # 只保留最近100帧的数据
            self.frame_times.pop(0)

    def record_cache_hit(self):
        """记录缓存命中"""
        self.cache_hits += 1

    def record_cache_miss(self):
        """记录缓存未命中"""
        self.cache_misses += 1

    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        if not self.frame_times:
            return {}

        avg_frame_time = sum(self.frame_times) / len(self.frame_times)
        fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0
        cache_hit_rate = self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0

        return {
            'avg_frame_time': avg_frame_time,
            'fps': fps,
            'cache_hit_rate': cache_hit_rate,
            'total_frames': len(self.frame_times)
        }

# 全局样式表
STYLE_SHEET = """
QMainWindow {
    background-color: #2b2b2b;
}

QLabel {
    color: #ffffff;
    font-size: 12px;
}

QPushButton {
    background-color: #3a3a3a;
    color: #ffffff;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 12px;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #4a4a4a;
}

QPushButton:pressed {
    background-color: #2a2a2a;
}

QSlider::groove:horizontal {
    border: 1px solid #999999;
    height: 8px;
    background: #4a4a4a;
    margin: 2px 0;
    border-radius: 4px;
}

QSlider::handle:horizontal {
    background: #cccccc;
    border: 1px solid #5c5c5c;
    width: 18px;
    margin: -2px 0;
    border-radius: 9px;
}

QSlider::handle:horizontal:hover {
    background: #ffffff;
}

QFrame#controlFrame {
    background-color: #1e1e1e;
    border-top: 1px solid #3a3a3a;
    padding: 10px;
}

QLabel#timeLabel {
    color: #cccccc;
    font-size: 12px;
    padding: 0 10px;
}
"""

class ImageCache:
    """图像缓存管理器，优化内存使用和性能"""
    def __init__(self, max_size: int = MAX_CACHE_SIZE):
        self.max_size = max_size
        self.cache: Dict[int, Tuple[QPixmap, bytes]] = {}
        self.access_order = []

    def get(self, frame_hash: int) -> Optional[QPixmap]:
        """获取缓存的图像"""
        if frame_hash in self.cache:
            # 更新访问顺序
            self.access_order.remove(frame_hash)
            self.access_order.append(frame_hash)
            return self.cache[frame_hash][0]
        return None

    def put(self, frame_hash: int, pixmap: QPixmap, data: bytes):
        """添加图像到缓存"""
        if len(self.cache) >= self.max_size:
            # 移除最久未使用的项
            oldest = self.access_order.pop(0)
            del self.cache[oldest]

        self.cache[frame_hash] = (pixmap, data)
        self.access_order.append(frame_hash)

    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_order.clear()
        gc.collect()

class VideoLabel(QLabel):
    """自定义视频显示控件，支持裁剪框绘制与交互（限定在display_rect内）"""
    __slots__ = ['crop_rect', 'drag_start', 'dragging', 'dragging_edge',
                 'dragging_border', 'display_rect', '_cached_paint_data',
                 '_last_crop_rect', '_image_cache']

    def __init__(self):
        super().__init__()
        self.crop_rect = None  # 注意：此rect为display_rect内的相对坐标
        self.drag_start = None
        self.dragging = False
        self.dragging_edge = None
        self.dragging_border = None  # 当前拖动的边框
        self.display_rect = QRect(0, 0, 0, 0)
        self._cached_paint_data = None  # 缓存绘制数据
        self._last_crop_rect = None     # 上次的裁剪框，用于检测变化
        self._image_cache = ImageCache()

        self.setMouseTracking(True)
        self.setFocusPolicy(Qt.StrongFocus)
        self.setFixedSize(600, 450)  # 调整为原来的2/3大小
        self.setStyleSheet("""
            QLabel {
                background-color: #1a1a1a;
                border: 1px solid #3a3a3a;
                border-radius: 4px;
            }
        """)
        self.setAutoFillBackground(True)

    def set_display_rect(self, rect):
        """设置显示区域，同时更新裁剪框"""
        if self.display_rect != rect:
            self.display_rect = rect
            self._invalidate_cache()
            if not self.crop_rect:
                self.reset_crop_rect()
            else:
                self.update()

    def reset_crop_rect(self):
        """重置裁剪框为显示区域的中心位置的合适大小"""
        if self.display_rect.isValid():
            # 计算显示区域的中心点
            center_x = self.display_rect.width() / 2
            center_y = self.display_rect.height() / 2

            # 设置裁剪框为显示区域的70%大小
            crop_width = int(self.display_rect.width() * 0.7)
            crop_height = int(self.display_rect.height() * 0.7)

            # 确保裁剪框居中
            left = int(center_x - crop_width / 2)
            top = int(center_y - crop_height / 2)

            self.crop_rect = QRect(left, top, crop_width, crop_height)
            self.limit_crop_rect()
            self._invalidate_cache()
            self.update()

    def _invalidate_cache(self):
        """使缓存失效"""
        self._cached_paint_data = None
        self._last_crop_rect = None

    def limit_crop_rect(self):
        """限制裁剪框在显示区域内，并保持最小尺寸"""
        if not self.crop_rect or not self.display_rect.isValid():
            return
            
        r = self.crop_rect
        d = self.display_rect
        
        # 确保不超出显示区域
        x = max(0, min(r.left(), d.width() - MIN_CROP_SIZE))
        y = max(0, min(r.top(), d.height() - MIN_CROP_SIZE))
        w = max(MIN_CROP_SIZE, min(r.width(), d.width() - x))
        h = max(MIN_CROP_SIZE, min(r.height(), d.height() - y))
        
        # 更新裁剪框
        self.crop_rect = QRect(x, y, w, h)

    def get_border_at(self, pos):
        """检测位置是否在边框上"""
        if not self.crop_rect:
            return None
        
        # 转换为display_rect内的相对坐标
        local_pos = pos - self.display_rect.topLeft()
        
        # 边框检测区域宽度
        border_width = 10
        
        # 创建边框矩形
        rect = self.crop_rect
        top = QRect(rect.left(), rect.top(), rect.width(), border_width)
        bottom = QRect(rect.left(), rect.bottom() - border_width, rect.width(), border_width)
        left = QRect(rect.left(), rect.top(), border_width, rect.height())
        right = QRect(rect.right() - border_width, rect.top(), border_width, rect.height())
        
        # 检查位置
        if top.contains(local_pos):
            return "top"
        elif bottom.contains(local_pos):
            return "bottom"
        elif left.contains(local_pos):
            return "left"
        elif right.contains(local_pos):
            return "right"
        return None

    def mousePressEvent(self, event):
        if not self.display_rect.isValid():
            return super().mousePressEvent(event)
        pos = event.pos()
        # 只响应display_rect区域内的鼠标事件
        if not self.display_rect.contains(pos):
            return super().mousePressEvent(event)
        local_pos = pos - self.display_rect.topLeft()  # 转为display_rect内坐标
        if event.button() == Qt.LeftButton and self.crop_rect:
            # 检查是否点击在调整大小的点上
            points = [
                self.crop_rect.topLeft(),
                self.crop_rect.topRight(),
                self.crop_rect.bottomLeft(),
                self.crop_rect.bottomRight()
            ]
            for i, point in enumerate(points):
                if (point - local_pos).manhattanLength() < CROP_HANDLE_HIT_RADIUS:
                    self.dragging_edge = i
                    self.drag_start = local_pos
                    self.dragging = True
                    return
                    
            # 检查是否点击在边框上
            border = self.get_border_at(pos)
            if border:
                self.dragging_border = border
                self.drag_start = local_pos
                self.dragging = True
                return
                
            # 检查是否点击在裁剪框内
            if self.crop_rect.contains(local_pos):
                self.dragging_edge = None
                self.dragging_border = None
                self.drag_start = local_pos - self.crop_rect.topLeft()
                self.dragging = True
                return
                
        # 新建裁剪框
        self.dragging = True
        self.dragging_edge = None
        self.dragging_border = None
        self.drag_start = local_pos
        self.crop_rect = QRect(local_pos, local_pos)
        self.repaint()
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        if not self.display_rect.isValid():
            return super().mouseMoveEvent(event)
        pos = event.pos()
        local_pos = pos - self.display_rect.topLeft()
        cursor_set = False
        if self.crop_rect:
            # 四个角
            points = [
                self.crop_rect.topLeft(),
                self.crop_rect.topRight(),
                self.crop_rect.bottomLeft(),
                self.crop_rect.bottomRight()
            ]
            # 对应手势
            cursors = [
                Qt.SizeFDiagCursor,  # 左上
                Qt.SizeBDiagCursor,  # 右上
                Qt.SizeBDiagCursor,  # 左下
                Qt.SizeFDiagCursor   # 右下
            ]
            for i, point in enumerate(points):
                if (point - local_pos).manhattanLength() < CROP_HANDLE_HIT_RADIUS:
                    self.setCursor(cursors[i])
                    cursor_set = True
                    break
                    
            # 检查边框
            if not cursor_set:
                border = self.get_border_at(pos)
                if border:
                    if border in ["top", "bottom"]:
                        self.setCursor(Qt.SizeVerCursor)
                    else:
                        self.setCursor(Qt.SizeHorCursor)
                    cursor_set = True
                elif self.crop_rect.contains(local_pos):
                    self.setCursor(Qt.SizeAllCursor)
                    cursor_set = True
                    
        if not cursor_set:
            self.setCursor(Qt.ArrowCursor)
            
        if not self.dragging:
            return super().mouseMoveEvent(event)
            
        # 只允许在display_rect内拖动
        local_pos.setX(max(0, min(local_pos.x(), self.display_rect.width()-1)))
        local_pos.setY(max(0, min(local_pos.y(), self.display_rect.height()-1)))
        
        if self.crop_rect:
            if self.dragging_edge is None and self.dragging_border is None:
                # 拖动裁剪框
                if self.drag_start is not None:
                    new_top_left = local_pos - self.drag_start
                    # 限制裁剪框不超出display_rect
                    new_top_left.setX(max(0, min(new_top_left.x(), self.display_rect.width() - self.crop_rect.width())))
                    new_top_left.setY(max(0, min(new_top_left.y(), self.display_rect.height() - self.crop_rect.height())))
                    self.crop_rect.moveTopLeft(new_top_left)
            elif self.dragging_edge is not None:
                # 调整裁剪框大小（角点）
                if self.dragging_edge == 0:  # 左上角
                    self.crop_rect.setTopLeft(local_pos)
                elif self.dragging_edge == 1:  # 右上角
                    self.crop_rect.setTopRight(local_pos)
                elif self.dragging_edge == 2:  # 左下角
                    self.crop_rect.setBottomLeft(local_pos)
                elif self.dragging_edge == 3:  # 右下角
                    self.crop_rect.setBottomRight(local_pos)
            elif self.dragging_border:
                # 调整裁剪框大小（边框）
                if self.dragging_border == "top":
                    self.crop_rect.setTop(local_pos.y())
                elif self.dragging_border == "bottom":
                    self.crop_rect.setBottom(local_pos.y())
                elif self.dragging_border == "left":
                    self.crop_rect.setLeft(local_pos.x())
                elif self.dragging_border == "right":
                    self.crop_rect.setRight(local_pos.x())
                    
            # 限制宽高不为负
            if self.crop_rect.width() < 0:
                left = self.crop_rect.left()
                self.crop_rect.setLeft(self.crop_rect.right())
                self.crop_rect.setRight(left)
            if self.crop_rect.height() < 0:
                top = self.crop_rect.top()
                self.crop_rect.setTop(self.crop_rect.bottom())
                self.crop_rect.setBottom(top)
            self.limit_crop_rect()
        self.repaint()
        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.dragging = False
            self.dragging_edge = None
            self.dragging_border = None
        super().mouseReleaseEvent(event)

    def leaveEvent(self, event):
        self.setCursor(Qt.ArrowCursor)
        super().leaveEvent(event)

    def paintEvent(self, event):
        super().paintEvent(event)
        if not self.display_rect.isValid():
            return

        # 检查是否需要重新计算绘制数据
        crop_changed = self._last_crop_rect != self.crop_rect
        if crop_changed or not self._cached_paint_data:
            self._update_paint_cache()
            self._last_crop_rect = QRect(self.crop_rect) if self.crop_rect else None

        # 使用缓存的数据进行绘制
        if self._cached_paint_data:
            self._draw_cached_content()

    def _update_paint_cache(self):
        """更新绘制缓存数据"""
        if not self.crop_rect or not self.crop_rect.isValid():
            self._cached_paint_data = None
            return

        # 计算实际显示位置
        draw_rect = QRect(
            self.display_rect.topLeft() + self.crop_rect.topLeft(),
            self.crop_rect.size()
        )

        # 计算三等分点（一次性计算）
        x1 = draw_rect.left() + draw_rect.width() / 3
        x2 = draw_rect.left() + (draw_rect.width() * 2) / 3
        y1 = draw_rect.top() + draw_rect.height() / 3
        y2 = draw_rect.top() + (draw_rect.height() * 2) / 3

        # 计算调整点
        points = [
            draw_rect.topLeft(),
            draw_rect.topRight(),
            draw_rect.bottomLeft(),
            draw_rect.bottomRight()
        ]

        # 计算尺寸信息
        size_text = ""
        main_window = self.window()
        if hasattr(main_window, 'cap') and main_window.cap:
            video_w = int(main_window.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            video_h = int(main_window.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            if self.display_rect.width() > 0 and self.display_rect.height() > 0:
                scale_w = video_w / self.display_rect.width()
                scale_h = video_h / self.display_rect.height()
                crop_w = int(self.crop_rect.width() * scale_w)
                crop_h = int(self.crop_rect.height() * scale_h)
                size_text = f'{crop_w}×{crop_h}'

        # 缓存所有计算结果
        self._cached_paint_data = {
            'draw_rect': draw_rect,
            'grid_lines': [(x1, y1), (x2, y2)],
            'handle_points': points,
            'size_text': size_text
        }

    def _draw_cached_content(self):
        """使用缓存数据绘制内容"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        data = self._cached_paint_data
        draw_rect = data['draw_rect']

        # 绘制外部区域的半透明遮罩
        path = QPainterPath()
        path.addRect(QRectF(self.display_rect))
        path.addRect(QRectF(draw_rect))
        painter.fillPath(path, QColor(0, 0, 0, 120))

        # 绘制裁剪框边框
        pen = QPen(QColor("#00A8FF"), 1.5, Qt.SolidLine)
        painter.setPen(pen)
        painter.drawRect(draw_rect)

        # 绘制九宫格辅助线
        pen.setStyle(Qt.DashLine)
        pen.setColor(QColor(255, 255, 255, 150))
        pen.setWidth(1)
        pen.setDashPattern([3, 3])
        painter.setPen(pen)

        x1, y1 = data['grid_lines'][0]
        x2, y2 = data['grid_lines'][1]

        # 绘制网格线
        painter.drawLine(int(x1), draw_rect.top(), int(x1), draw_rect.bottom())
        painter.drawLine(int(x2), draw_rect.top(), int(x2), draw_rect.bottom())
        painter.drawLine(draw_rect.left(), int(y1), draw_rect.right(), int(y1))
        painter.drawLine(draw_rect.left(), int(y2), draw_rect.right(), int(y2))

        # 绘制调整点
        painter.setPen(Qt.NoPen)
        handle_color = QColor("#00A8FF")
        handle_color.setAlpha(200)
        painter.setBrush(handle_color)
        handle_size = CROP_HANDLE_RADIUS - 3

        for point in data['handle_points']:
            painter.drawEllipse(point, handle_size, handle_size)

        # 显示尺寸信息
        if data['size_text']:
            text_rect = QRect(draw_rect.topLeft() + QPoint(5, 5), QSize(120, 25))
            painter.fillRect(text_rect, QColor(0, 0, 0, 120))
            painter.setPen(QColor(255, 255, 255, 220))
            painter.setFont(QFont("Arial", 9))
            painter.drawText(text_rect, Qt.AlignCenter, data['size_text'])

        painter.end()

def get_ffmpeg_command(base_cmd='ffmpeg'):
    """获取可用的ffmpeg命令，处理Windows兼容性"""
    if platform.system() == 'Windows':
        # Windows下尝试多种可能的命令
        commands = [base_cmd, f'{base_cmd}.exe']
    else:
        commands = [base_cmd]

    for cmd in commands:
        try:
            # 测试命令是否可用
            result = subprocess.run([cmd, '-version'],
                                  stdout=subprocess.PIPE,
                                  stderr=subprocess.PIPE,
                                  timeout=5)
            if result.returncode == 0:
                return cmd
        except (FileNotFoundError, subprocess.TimeoutExpired):
            continue

        try:
            # 尝试使用shell=True
            result = subprocess.run(f'{cmd} -version',
                                  shell=True,
                                  stdout=subprocess.PIPE,
                                  stderr=subprocess.PIPE,
                                  timeout=5)
            if result.returncode == 0:
                return cmd
        except (FileNotFoundError, subprocess.TimeoutExpired):
            continue

    return None

def run_ffmpeg_command(cmd_list, **kwargs):
    """运行ffmpeg命令，自动处理Windows兼容性"""
    if not cmd_list:
        raise ValueError("命令列表不能为空")

    # 获取可用的ffmpeg命令
    base_cmd = cmd_list[0]
    available_cmd = get_ffmpeg_command(base_cmd)

    if not available_cmd:
        raise FileNotFoundError(f"找不到可用的{base_cmd}命令")

    # 替换命令
    cmd_list[0] = available_cmd

    # 设置Windows特定参数和编码
    if platform.system() == 'Windows':
        kwargs.setdefault('creationflags', subprocess.CREATE_NO_WINDOW)

    # 设置编码参数，避免GBK错误
    kwargs.setdefault('encoding', 'utf-8')
    kwargs.setdefault('errors', 'ignore')

    # 执行命令
    return subprocess.run(cmd_list, **kwargs)

@contextmanager
def managed_subprocess(*args, **kwargs):
    """上下文管理器，确保subprocess正确清理"""
    process = None
    try:
        # 如果第一个参数是命令列表，尝试使用改进的ffmpeg命令执行
        if args and isinstance(args[0], list) and args[0] and args[0][0] in ['ffmpeg', 'ffprobe']:
            cmd_list = args[0].copy()
            available_cmd = get_ffmpeg_command(cmd_list[0])
            if available_cmd:
                cmd_list[0] = available_cmd
                if platform.system() == 'Windows':
                    kwargs.setdefault('creationflags', subprocess.CREATE_NO_WINDOW)
                # 设置编码参数，避免GBK错误
                kwargs.setdefault('encoding', 'utf-8')
                kwargs.setdefault('errors', 'ignore')
                process = subprocess.Popen(cmd_list, *args[1:], **kwargs)
            else:
                raise FileNotFoundError(f"找不到可用的{cmd_list[0]}命令")
        else:
            process = subprocess.Popen(*args, **kwargs)
        yield process
    finally:
        if process:
            try:
                if process.poll() is None:  # 进程仍在运行
                    process.terminate()
                    # 等待进程终止，最多等待5秒
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()  # 强制终止
                        process.wait()
            except Exception as e:
                print(f"清理subprocess时出错: {e}")

class VideoSaveThread(QThread):
    """视频保存线程"""
    progress = pyqtSignal(int, str)  # 进度信号
    finished = pyqtSignal(bool, str)  # 完成信号，参数为(是否成功, 错误信息)

    def __init__(self, video_path, output_path, crop_params):
        super().__init__()
        self.video_path = str(video_path)
        self.output_path = str(output_path)
        self.crop_params = crop_params
        self.is_canceled = False
        self._current_process = None
        
    def get_video_info(self):
        """获取完整的视频信息"""
        try:
            # 获取视频流详细信息
            cmd = [
                'ffprobe',
                '-v', 'error',
                '-select_streams', 'v:0',  # 只选择视频流
                '-show_entries', 'stream=nb_frames,r_frame_rate,codec_name,bit_rate,color_space,color_transfer,color_primaries,pix_fmt,profile,level',
                '-show_entries', 'format=duration',  # 同时获取格式信息
                '-of', 'json',  # 使用JSON格式输出
                str(self.video_path)  # 确保路径是字符串类型
            ]

            # 使用上下文管理器确保进程正确清理
            with managed_subprocess(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE) as process:
                try:
                    stdout, _ = process.communicate(timeout=30)
                    if process.returncode == 0:
                        info = json.loads(stdout)
                        stream_info = info.get('streams', [{}])[0]
                        format_info = info.get('format', {})

                        return {
                            'nb_frames': int(stream_info.get('nb_frames', 0)),
                            'frame_rate': stream_info.get('r_frame_rate'),
                            'codec_name': stream_info.get('codec_name'),
                            'bit_rate': stream_info.get('bit_rate'),
                            'color_space': stream_info.get('color_space'),
                            'color_transfer': stream_info.get('color_transfer'),
                            'color_primaries': stream_info.get('color_primaries'),
                            'pix_fmt': stream_info.get('pix_fmt'),
                            'profile': stream_info.get('profile'),
                            'level': stream_info.get('level'),
                            'duration': float(format_info.get('duration', 0))
                        }
                except subprocess.TimeoutExpired:
                    print("获取视频信息超时")

        except Exception as e:
            print(f"获取视频信息失败: {e}")

        # 返回默认值
        return {
            'nb_frames': 0,
            'frame_rate': None,
            'codec_name': None,
            'bit_rate': None,
            'color_space': None,
            'color_transfer': None,
            'color_primaries': None,
            'pix_fmt': None,
            'profile': None,
            'level': None,
            'duration': 0
        }
        
    def run(self):
        try:
            # 1. 获取视频信息
            video_info = self.get_video_info()
            total_frames = video_info['nb_frames']
            
            if total_frames == 0:
                # 如果无法获取帧数，尝试使用时长和帧率计算
                try:
                    cmd = [
                        'ffprobe',
                        '-v', 'error',
                        '-show_entries', 'format=duration',
                        '-of', 'default=noprint_wrappers=1:nokey=1',
                        str(self.video_path)  # 确保路径是字符串类型
                    ]
                    result = run_ffmpeg_command(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)
                    duration = float(result.stdout.strip())
                    
                    # 从帧率计算总帧数
                    if video_info['frame_rate']:
                        if '/' in video_info['frame_rate']:
                            num, den = map(int, video_info['frame_rate'].split('/'))
                            fps = num / den
                        else:
                            fps = float(video_info['frame_rate'])
                        total_frames = int(duration * fps)
                except:
                    total_frames = 0
                    
            # 2. 构建ffmpeg命令
            crop_filter = 'crop={}:{}:{}:{}'.format(
                str(self.crop_params["w"]),
                str(self.crop_params["h"]),
                str(self.crop_params["x"]),
                str(self.crop_params["y"])
            )

            # 基础命令
            ffmpeg_cmd = [
                'ffmpeg',
                '-y',  # 覆盖已存在的文件
                '-i', str(self.video_path),
                '-filter:v', crop_filter
            ]
            
            # 使用简化的编码设置，提高兼容性
            try:
                # 尝试使用原编码器
                if video_info['codec_name'] and video_info['codec_name'] in ['h264', 'libx264']:
                    ffmpeg_cmd.extend(['-c:v', 'libx264'])
                else:
                    # 默认使用h264
                    ffmpeg_cmd.extend(['-c:v', 'libx264'])

                # 简化的编码设置
                ffmpeg_cmd.extend([
                    '-preset', 'fast',  # 使用快速预设
                    '-crf', '23',  # 恒定质量模式
                    '-pix_fmt', 'yuv420p',  # 兼容的像素格式
                    '-c:a', 'copy',  # 直接复制音频流
                    '-movflags', '+faststart',  # 优化MP4文件结构
                    str(self.output_path)
                ])

            except Exception as e:
                print(f"编码参数设置出错，使用默认设置: {e}")
                # 最简化的设置
                ffmpeg_cmd = [
                    'ffmpeg',
                    '-y',
                    '-i', str(self.video_path),
                    '-filter:v', crop_filter,
                    '-c:v', 'libx264',
                    '-preset', 'fast',
                    '-crf', '23',
                    '-c:a', 'copy',
                    str(self.output_path)
                ]

            # 3. 打印调试信息
            print(f"执行FFmpeg命令: {' '.join(ffmpeg_cmd)}")
            print(f"输入文件: {self.video_path}")
            print(f"输出文件: {self.output_path}")
            print(f"裁剪参数: {self.crop_params}")

            # 验证裁剪参数
            if (self.crop_params["w"] <= 0 or self.crop_params["h"] <= 0 or
                self.crop_params["x"] < 0 or self.crop_params["y"] < 0):
                self.finished.emit(False, f"无效的裁剪参数: {self.crop_params}")
                return

            # 4. 执行ffmpeg命令
            with managed_subprocess(
                ffmpeg_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                encoding='utf-8',
                errors='ignore',
                creationflags=subprocess.CREATE_NO_WINDOW if platform.system() == 'Windows' else 0
            ) as process:
                self._current_process = process

                # 4. 监控进度
                frame_pattern = re.compile(r'frame=\s*(\d+)')
                current_frame = 0

                while True:
                    if self.is_canceled:
                        if os.path.exists(self.output_path):
                            try:
                                os.remove(self.output_path)
                            except:
                                pass
                        self.finished.emit(False, "操作已取消")
                        return

                    if process.poll() is not None:
                        break

                    # 读取stderr获取frame信息
                    try:
                        line = process.stderr.readline()
                        if line:
                            # 现在line已经是字符串了，不需要额外解码
                            match = frame_pattern.search(line)
                            if match:
                                current_frame = int(match.group(1))
                                if total_frames > 0:
                                    progress = min(99, int((current_frame / total_frames) * 100))
                                    self.progress.emit(progress, f"正在处理视频...{progress}% ({current_frame}/{total_frames}帧)")
                                else:
                                    # 如果无法获取总帧数，只显示当前帧数
                                    self.progress.emit(50, f"正在处理视频...（已处理{current_frame}帧）")
                        else:
                            time.sleep(0.1)  # 避免过于频繁的检查
                    except Exception as e:
                        print(f"读取进度信息时出错: {e}")
                        break

                if process.returncode != 0:
                    try:
                        # 读取完整的错误信息
                        if process.stderr:
                            remaining_stderr = process.stderr.read()
                            if remaining_stderr:
                                error = remaining_stderr
                            else:
                                error = "FFmpeg进程异常退出"
                        else:
                            error = "无法获取错误信息"

                        # 现在error已经是字符串了，不需要额外解码

                        # 提取关键错误信息
                        error_lines = error.split('\n')
                        key_errors = []
                        for line in error_lines:
                            if any(keyword in line.lower() for keyword in ['error', 'failed', 'invalid', 'not found']):
                                key_errors.append(line.strip())

                        if key_errors:
                            error_summary = '\n'.join(key_errors[-3:])  # 最后3个关键错误
                        else:
                            error_summary = error[-500:] if len(error) > 500 else error  # 最后500字符

                    except Exception as e:
                        error_summary = f"读取错误信息失败: {str(e)}"

                    # 添加调试信息
                    debug_info = f"\n\n调试信息:\n"
                    debug_info += f"返回代码: {process.returncode}\n"
                    debug_info += f"输入文件: {self.video_path}\n"
                    debug_info += f"输出文件: {self.output_path}\n"
                    debug_info += f"裁剪参数: {self.crop_params}\n"

                    if os.path.exists(self.output_path):
                        try:
                            os.remove(self.output_path)
                        except:
                            pass
                    self.finished.emit(False, f"FFmpeg处理失败:\n{error_summary}{debug_info}")
                    return

            # 检查输出文件是否存在且大小不为0
            if not os.path.exists(self.output_path) or os.path.getsize(self.output_path) == 0:
                self.finished.emit(False, "输出文件无效")
                return

            self.progress.emit(100, "处理完成")
            self.finished.emit(True, "")
            
        except Exception as e:
            if os.path.exists(self.output_path):
                try:
                    os.remove(self.output_path)
                except:
                    pass
            self.finished.emit(False, str(e))

    def cancel(self):
        """取消当前操作"""
        self.is_canceled = True
        if self._current_process and self._current_process.poll() is None:
            try:
                self._current_process.terminate()
            except Exception as e:
                print(f"终止进程时出错: {e}")

class VideoCropper(QMainWindow):
    """主窗口，负责视频加载、播放、裁剪与保存"""
    def __init__(self):
        super().__init__()
        self.settings = QSettings('MyCompany', 'VideoCropper')
        self.last_dir = self.settings.value('last_dir', os.getcwd())
        self.video_path = None
        self.cap = None
        self.current_frame = None
        self.is_paused = True  # 默认暂停状态
        self.total_frames = 0
        self.current_frame_no = 0

        # 性能优化相关
        self._frame_cache = {}
        self._last_frame_hash = None
        self._frame_counter = 0
        self._last_update_time = 0
        self._performance_monitor = PerformanceMonitor()

        self.setup_ui()
        self.setup_video_player()
        self.setup_drag_drop()

    def setup_ui(self):
        self.setWindowTitle('视频裁剪工具')
        self.setFixedSize(800, 650)  # 调整主窗口大小
        self.setStyleSheet(STYLE_SHEET)

        # 创建主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # 创建视频显示标签
        self.video_label = VideoLabel()
        self.video_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.video_label, 1, Qt.AlignCenter)

        # 创建顶部工具栏
        toolbar = QHBoxLayout()
        toolbar.setContentsMargins(0, 0, 0, 0)
        
        # 添加文件操作按钮
        load_button = QPushButton('打开视频')
        load_button.setIcon(self.style().standardIcon(QStyle.SP_DialogOpenButton))
        load_button.setFixedHeight(30)
        load_button.clicked.connect(self.load_video)
        toolbar.addWidget(load_button)

        save_button = QPushButton('保存裁剪')
        save_button.setIcon(self.style().standardIcon(QStyle.SP_DialogSaveButton))
        save_button.setFixedHeight(30)
        save_button.clicked.connect(self.save_cropped_video)
        toolbar.addWidget(save_button)

        reset_button = QPushButton('重置裁剪框')
        reset_button.setIcon(self.style().standardIcon(QStyle.SP_DialogResetButton))
        reset_button.setFixedHeight(30)
        reset_button.clicked.connect(self.video_label.reset_crop_rect)
        toolbar.addWidget(reset_button)

        toolbar.addStretch()
        main_layout.addLayout(toolbar)

        # 创建底部控制栏
        control_frame = QFrame()
        control_frame.setObjectName("controlFrame")
        control_frame.setFixedHeight(80)  # 固定控制栏高度
        control_layout = QVBoxLayout(control_frame)
        control_layout.setContentsMargins(10, 5, 10, 5)
        
        # 进度条和时间显示
        slider_layout = QHBoxLayout()
        slider_layout.setContentsMargins(0, 0, 0, 0)
        
        self.time_label = QLabel("00:00 / 00:00")
        self.time_label.setObjectName("timeLabel")
        self.time_label.setFixedWidth(100)
        
        self.progress_slider = QSlider(Qt.Horizontal)
        self.progress_slider.setFixedHeight(20)
        self.progress_slider.setMinimum(0)
        self.progress_slider.setMaximum(1000)
        self.progress_slider.valueChanged.connect(self.slider_value_changed)
        
        slider_layout.addWidget(self.time_label)
        slider_layout.addWidget(self.progress_slider)
        control_layout.addLayout(slider_layout)

        # 播放控制按钮
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 5, 0, 0)
        
        self.play_pause_button = QPushButton()
        self.play_pause_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))
        self.play_pause_button.setFixedSize(30, 30)
        self.play_pause_button.clicked.connect(self.toggle_pause)
        
        button_layout.addStretch()
        button_layout.addWidget(self.play_pause_button)
        button_layout.addStretch()
        
        control_layout.addLayout(button_layout)
        main_layout.addWidget(control_frame)

    def setup_video_player(self):
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_frame)
        # 不自动启动定时器，等待视频加载后再启动
        self.timer.setInterval(TIMER_INTERVAL_MS)

    def setup_drag_drop(self):
        """设置拖拽功能"""
        self.setAcceptDrops(True)

        # 为video_label添加拖拽提示
        self.video_label.setStyleSheet(self.video_label.styleSheet() + """
            QLabel {
                border: 2px dashed #555555;
            }
            QLabel:hover {
                border: 2px dashed #00A8FF;
            }
        """)

        # 添加拖拽提示文本
        if not hasattr(self.video_label, 'drag_hint_added'):
            original_text = self.video_label.text()
            hint_text = "拖拽视频文件到此处\n或点击'打开视频'按钮"
            if not original_text:
                self.video_label.setText(hint_text)
                self.video_label.setAlignment(Qt.AlignCenter)
                self.video_label.drag_hint_added = True

    def update_time_label(self):
        if not self.cap:
            return
        current_time = int(self.current_frame_no / self.cap.get(cv2.CAP_PROP_FPS))
        total_time = int(self.total_frames / self.cap.get(cv2.CAP_PROP_FPS))
        current_str = time.strftime('%M:%S', time.gmtime(current_time))
        total_str = time.strftime('%M:%S', time.gmtime(total_time))
        self.time_label.setText(f"{current_str} / {total_str}")

    def slider_value_changed(self, value):
        if not self.cap:
            return
        frame_no = int((value / 1000.0) * self.total_frames)
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_no)
        self.current_frame_no = frame_no
        self.update_time_label()

    def toggle_pause(self):
        """暂停/播放切换"""
        if not self.cap:
            return
        self.is_paused = not self.is_paused
        icon = QStyle.SP_MediaPause if not self.is_paused else QStyle.SP_MediaPlay
        self.play_pause_button.setIcon(self.style().standardIcon(icon))

    def load_video(self):
        """通过文件对话框选择视频文件"""
        options = QFileDialog.Options()
        # 构建支持的格式字符串
        formats = " ".join([f"*{ext}" for ext in SUPPORTED_VIDEO_FORMATS])
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "选择视频文件",
            self.last_dir,
            f"视频文件 ({formats});;所有文件 (*)",
            options=options
        )
        if file_name:
            self.load_video_file(file_name)

    def update_frame(self):
        if not self.cap or self.is_paused:
            return
        ret, frame = self.cap.read()
        if ret:
            self.current_frame = frame
            self.current_frame_no = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
            
            # 更新进度条
            if self.total_frames > 0:
                slider_value = int((self.current_frame_no / self.total_frames) * 1000)
                self.progress_slider.setValue(slider_value)
            
            # 更新时间标签
            self.update_time_label()
            
            # 更新显示
            self.update_frame_display(frame)
        else:
            # 视频播放完毕，重置到开始位置并暂停
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            self.current_frame_no = 0
            self.is_paused = True
            self.play_pause_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))

    def update_frame_display(self, frame):
        """更新视频帧显示（优化版本）"""
        if frame is None:
            return

        start_time = time.time()

        # 计算帧的哈希值用于缓存
        frame_hash = hash(frame.tobytes())

        # 检查缓存
        cached_pixmap = self.video_label._image_cache.get(frame_hash)
        if cached_pixmap:
            self.video_label.setPixmap(cached_pixmap)
            self._performance_monitor.record_cache_hit()
            self._performance_monitor.record_frame_time(time.time() - start_time)
            return

        self._performance_monitor.record_cache_miss()

        # 节流更新 - 限制更新频率
        current_time = time.time() * 1000  # 转换为毫秒
        if current_time - self._last_update_time < UPDATE_THROTTLE_MS:
            return
        self._last_update_time = current_time

        # 转换颜色空间
        rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        # 创建QImage - 确保数据生命周期正确
        h, w, ch = rgb_image.shape
        bytes_per_line = ch * w

        # 复制数据以确保生命周期安全
        rgb_data = rgb_image.copy()
        qt_image = QImage(rgb_data.data, w, h, bytes_per_line, QImage.Format_RGB888)

        # 获取标签的实际大小
        label_size = self.video_label.size()

        # 计算缩放比例以保持原始宽高比
        video_ratio = w / h
        label_ratio = label_size.width() / label_size.height()

        # 计算目标尺寸（保持宽高比）
        if video_ratio > label_ratio:
            # 视频更宽，以宽度为基准
            target_width = label_size.width()
            target_height = int(target_width / video_ratio)
        else:
            # 视频更高，以高度为基准
            target_height = label_size.height()
            target_width = int(target_height * video_ratio)

        # 创建pixmap并缩放
        pixmap = QPixmap.fromImage(qt_image)
        scaled_pixmap = pixmap.scaled(
            target_width,
            target_height,
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )

        # 缓存处理后的pixmap
        self.video_label._image_cache.put(frame_hash, scaled_pixmap, rgb_data.tobytes())

        # 计算视频在标签中的实际位置（居中显示）
        x_offset = (label_size.width() - target_width) // 2
        y_offset = (label_size.height() - target_height) // 2

        # 设置显示区域
        display_rect = QRect(x_offset, y_offset, target_width, target_height)

        # 更新显示
        self.video_label.setPixmap(scaled_pixmap)
        self.video_label.set_display_rect(display_rect)

        # 记录性能数据
        self._performance_monitor.record_frame_time(time.time() - start_time)

        # 定期清理内存
        self._frame_counter += 1
        if self._frame_counter % GC_INTERVAL_FRAMES == 0:
            gc.collect()

            # 输出性能统计（调试用）
            stats = self._performance_monitor.get_stats()
            if stats:
                print(f"性能统计 - 平均FPS: {stats['fps']:.1f}, 缓存命中率: {stats['cache_hit_rate']:.2%}")

    def closeEvent(self, event):
        """窗口关闭时释放视频资源"""
        try:
            # 停止定时器
            if hasattr(self, 'timer'):
                self.timer.stop()

            # 释放视频资源
            if self.cap:
                self.cap.release()
                self.cap = None

            # 清理缓存
            if hasattr(self.video_label, '_image_cache'):
                self.video_label._image_cache.clear()

            # 清理帧缓存
            self._frame_cache.clear()

            # 强制垃圾回收
            gc.collect()

        except Exception as e:
            print(f"关闭窗口时出错: {e}")
        finally:
            event.accept()

    def save_cropped_video(self):
        if not self.cap or not self.video_label.crop_rect:
            return
        rect = self.video_label.crop_rect
        if rect.width() < MIN_CROP_SIZE or rect.height() < MIN_CROP_SIZE:
            self.show_message("裁剪区域太小！")
            return

        # 自动生成输出文件名
        input_path = self.video_path
        input_dir = os.path.dirname(input_path)
        input_name = os.path.splitext(os.path.basename(input_path))[0]
        input_ext = os.path.splitext(input_path)[1]
        
        # 生成时间戳后缀
        timestamp = time.strftime("_%Y%m%d_%H%M%S")
        output_name = f"{input_name}_crop{timestamp}{input_ext}"
        file_name = os.path.join(input_dir, output_name)

        if not self.check_ffmpeg():
            # 提供更详细的错误信息和解决方案
            error_msg = self.get_ffmpeg_error_message()
            self.show_message(error_msg)
            return

        # 计算裁剪参数
        video_w = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        video_h = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        display_rect = self.video_label.display_rect
        rect = self.video_label.crop_rect

        crop_x = int(rect.x() * (video_w / display_rect.width()))
        crop_y = int(rect.y() * (video_h / display_rect.height()))
        crop_w = int(rect.width() * (video_w / display_rect.width()))
        crop_h = int(rect.height() * (video_h / display_rect.height()))

        # 保证裁剪区域不超出原视频边界
        crop_x = max(0, crop_x)
        crop_y = max(0, crop_y)
        crop_w = min(video_w - crop_x, crop_w)
        crop_h = min(video_h - crop_y, crop_h)

        # 创建进度对话框
        self.progress = QProgressDialog("正在保存裁剪视频...", "取消", 0, 100, self)
        self.progress.setWindowTitle("请稍候")
        self.progress.setWindowModality(Qt.WindowModal)
        self.progress.setMinimumDuration(0)
        self.progress.setAutoClose(False)
        self.progress.setAutoReset(False)
        
        # 设置进度对话框样式
        self.progress.setStyleSheet("""
            QProgressDialog {
                background-color: #2b2b2b;
            }
            QProgressDialog QLabel {
                color: #ffffff;
                font-size: 12px;
            }
            QProgressBar {
                border: 1px solid #3a3a3a;
                border-radius: 3px;
                background-color: #1a1a1a;
                text-align: center;
                color: #ffffff;
            }
            QProgressBar::chunk {
                background-color: #00A8FF;
                border-radius: 2px;
            }
            QPushButton {
                background-color: #3a3a3a;
                color: #ffffff;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #4a4a4a;
            }
            QPushButton:pressed {
                background-color: #2a2a2a;
            }
        """)

        # 创建并启动保存线程
        self.save_thread = VideoSaveThread(
            self.video_path,
            file_name,
            {
                "x": crop_x,
                "y": crop_y,
                "w": crop_w,
                "h": crop_h,
                "orig_w": video_w,
                "orig_h": video_h
            }
        )
        
        # 连接信号
        self.save_thread.progress.connect(self.update_progress)
        self.save_thread.finished.connect(self.save_finished)
        self.progress.canceled.connect(self.save_thread.cancel)
        
        # 启动线程
        self.save_thread.start()
        self.progress.show()

    def update_progress(self, value, message):
        """更新进度对话框"""
        if self.progress:
            self.progress.setValue(value)
            self.progress.setLabelText(message)

    def save_finished(self, success, error_message):
        """处理保存完成事件"""
        if self.progress:
            self.progress.close()
        
        if success:
            self.show_message("保存成功！")
            # 自动打开保存文件所在的文件夹
            folder = os.path.dirname(self.save_thread.output_path)
            if platform.system() == 'Windows':
                os.startfile(folder)
            elif platform.system() == 'Darwin':
                subprocess.Popen(['open', folder])
            else:
                subprocess.Popen(['xdg-open', folder])
        else:
            self.show_message(f"保存失败: {error_message}")
        
        # 清理线程
        self.save_thread.deleteLater()
        self.save_thread = None

    def check_ffmpeg(self):
        """检测ffmpeg命令是否可用"""
        # 尝试多种方式检测ffmpeg
        ffmpeg_commands = ['ffmpeg', 'ffmpeg.exe']

        for cmd in ffmpeg_commands:
            try:
                # 方法1：直接调用
                result = subprocess.run([cmd, '-version'],
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      timeout=10)
                if result.returncode == 0:
                    return True
            except (FileNotFoundError, subprocess.TimeoutExpired):
                pass

            try:
                # 方法2：使用shell=True（Windows兼容）
                result = subprocess.run(f'{cmd} -version',
                                      shell=True,
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      timeout=10)
                if result.returncode == 0:
                    return True
            except (FileNotFoundError, subprocess.TimeoutExpired):
                pass

        # 方法3：尝试查找ffmpeg路径
        try:
            if platform.system() == 'Windows':
                result = subprocess.run('where ffmpeg',
                                      shell=True,
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      timeout=10)
            else:
                result = subprocess.run('which ffmpeg',
                                      shell=True,
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      timeout=10)

            if result.returncode == 0 and result.stdout.strip():
                return True
        except Exception:
            pass

        return False

    def get_ffmpeg_error_message(self):
        """获取详细的ffmpeg错误信息和解决方案"""
        error_msg = "❌ 未检测到FFmpeg\n\n"

        # 检查系统类型并提供相应的解决方案
        if platform.system() == 'Windows':
            error_msg += "🔧 Windows系统解决方案：\n"
            error_msg += "1. 下载FFmpeg：https://ffmpeg.org/download.html\n"
            error_msg += "2. 解压到文件夹（如：C:\\ffmpeg）\n"
            error_msg += "3. 将bin目录添加到系统PATH环境变量\n"
            error_msg += "4. 重启应用程序\n\n"

            # 尝试检测可能的安装位置
            possible_paths = [
                "C:\\ffmpeg\\bin\\ffmpeg.exe",
                "C:\\Program Files\\ffmpeg\\bin\\ffmpeg.exe",
                "C:\\Program Files (x86)\\ffmpeg\\bin\\ffmpeg.exe"
            ]

            found_paths = []
            for path in possible_paths:
                if os.path.exists(path):
                    found_paths.append(path)

            if found_paths:
                error_msg += "💡 发现可能的FFmpeg安装：\n"
                for path in found_paths:
                    error_msg += f"   {path}\n"
                error_msg += "请检查PATH环境变量是否包含bin目录\n\n"

        elif platform.system() == 'Darwin':  # macOS
            error_msg += "🔧 macOS系统解决方案：\n"
            error_msg += "使用Homebrew安装：brew install ffmpeg\n"
            error_msg += "或从官网下载：https://ffmpeg.org/download.html\n\n"

        else:  # Linux
            error_msg += "🔧 Linux系统解决方案：\n"
            error_msg += "Ubuntu/Debian: sudo apt install ffmpeg\n"
            error_msg += "CentOS/RHEL: sudo yum install ffmpeg\n"
            error_msg += "或从官网下载：https://ffmpeg.org/download.html\n\n"

        # 添加环境变量检查信息
        try:
            path_env = os.environ.get('PATH', '')
            if 'ffmpeg' in path_env.lower():
                error_msg += "✅ PATH中包含ffmpeg相关路径\n"
            else:
                error_msg += "⚠️ PATH中未找到ffmpeg相关路径\n"
        except:
            pass

        error_msg += "\n📝 如果已安装FFmpeg，请确保：\n"
        error_msg += "• 命令行中可以运行 'ffmpeg -version'\n"
        error_msg += "• 重启了应用程序\n"
        error_msg += "• PATH环境变量配置正确"

        return error_msg

    def show_message(self, msg):
        """弹窗提示信息"""
        from PyQt5.QtWidgets import QMessageBox
        message_box = QMessageBox(self)
        message_box.setWindowTitle("提示")
        message_box.setText(msg)
        message_box.setIcon(QMessageBox.Information)
        
        # 设置样式
        message_box.setStyleSheet("""
            QMessageBox {
                background-color: #2b2b2b;
            }
            QMessageBox QLabel {
                color: #ffffff;
                min-width: 200px;
            }
            QPushButton {
                background-color: #3a3a3a;
                color: #ffffff;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
                font-size: 12px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #4a4a4a;
            }
            QPushButton:pressed {
                background-color: #2a2a2a;
            }
        """)
        
        message_box.exec_()

    def dragEnterEvent(self, event):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查是否包含支持的视频文件
            for url in event.mimeData().urls():
                if url.isLocalFile():
                    file_path = url.toLocalFile()
                    file_ext = os.path.splitext(file_path)[1].lower()
                    if file_ext in SUPPORTED_VIDEO_FORMATS:
                        event.acceptProposedAction()
                        # 添加视觉反馈
                        self.video_label.setStyleSheet(self.video_label.styleSheet().replace(
                            "border: 2px dashed #555555;",
                            "border: 3px dashed #00A8FF; background-color: rgba(0, 168, 255, 0.1);"
                        ))
                        return
        event.ignore()

    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        # 恢复原始样式
        self.video_label.setStyleSheet(self.video_label.styleSheet().replace(
            "border: 3px dashed #00A8FF; background-color: rgba(0, 168, 255, 0.1);",
            "border: 2px dashed #555555;"
        ))
        event.accept()

    def dropEvent(self, event):
        """拖拽放下事件"""
        if event.mimeData().hasUrls():
            for url in event.mimeData().urls():
                if url.isLocalFile():
                    file_path = url.toLocalFile()
                    file_ext = os.path.splitext(file_path)[1].lower()
                    if file_ext in SUPPORTED_VIDEO_FORMATS:
                        # 恢复原始样式
                        self.dragLeaveEvent(event)
                        # 加载视频文件
                        self.load_video_file(file_path)
                        event.acceptProposedAction()
                        return
        event.ignore()

    def load_video_file(self, file_path):
        """加载视频文件（从拖拽或文件对话框）"""
        try:
            file_path = str(file_path)
            self.last_dir = os.path.dirname(file_path)
            self.settings.setValue('last_dir', self.last_dir)

            if self.cap:
                self.cap.release()

            self.video_path = file_path
            self.cap = cv2.VideoCapture(self.video_path)

            if not self.cap.isOpened():
                self.show_message(f"无法打开视频文件：{os.path.basename(file_path)}")
                return

            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.current_frame_no = 0

            # 确保默认为暂停状态
            self.is_paused = True
            self.play_pause_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))

            # 读取第一帧并显示
            ret, frame = self.cap.read()
            if ret:
                self.current_frame = frame
                self.update_frame_display(frame)
                self.video_label.reset_crop_rect()

                # 更新进度条和时间显示
                self.progress_slider.setValue(0)
                self.update_time_label()

                # 清除拖拽提示
                if hasattr(self.video_label, 'drag_hint_added'):
                    self.video_label.setText("")
                    delattr(self.video_label, 'drag_hint_added')

                self.show_message(f"成功加载视频：{os.path.basename(file_path)}")
            else:
                self.show_message("无法读取视频帧")
                return

            # 启动定时器，但视频处于暂停状态
            self.timer.start()

        except Exception as e:
            self.show_message(f"加载视频时出错：{str(e)}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用Fusion风格
    window = VideoCropper()
    # 设置应用图标
    window.setWindowIcon(window.style().standardIcon(QStyle.SP_DialogOpenButton))
    # 居中显示主窗口
    qr = window.frameGeometry()
    cp = QApplication.desktop().screen().rect().center()
    qr.moveCenter(cp)
    window.move(qr.topLeft())
    window.show()
    sys.exit(app.exec_()) 