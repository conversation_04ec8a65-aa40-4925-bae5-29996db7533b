<!doctype html>
<html lang="zh-CN">
<head>
    <title>Excel to JSON Converter</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        :root {
            --primary: #3a86ff;
            --secondary: #8338ec;
            --success: #06d6a0;
            --light-bg: #f8f9fa;
            --card-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            padding: 1.5rem 0;
        }
        
        .container {
            max-width: 800px;
        }
        
        .app-header {
            text-align: center;
            margin-bottom: 1.5rem;
        }
        
        .app-title {
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.25rem;
            font-size: 1.75rem;
        }
        
        .app-subtitle {
            color: #6c757d;
            font-size: 0.95rem;
            font-weight: 400;
        }
        
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: var(--card-shadow);
            margin-bottom: 1.25rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            font-weight: 600;
            padding: 0.75rem 1.25rem;
            border-bottom: none;
            border-radius: 8px 8px 0 0 !important;
            font-size: 0.95rem;
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        .form-label {
            font-weight: 500;
            font-size: 0.9rem;
            margin-bottom: 0.35rem;
        }
        
        .form-control {
            border-radius: 6px;
            font-size: 0.9rem;
            padding: 0.5rem 0.75rem;
            border: 1px solid #dee2e6;
        }
        
        .form-control:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(58, 134, 255, 0.25);
        }
        
        .form-text {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .btn {
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: linear-gradient(to right, var(--primary), var(--secondary));
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .btn-success {
            background-color: var(--success);
            border: none;
        }
        
        .btn-success:hover {
            background-color: #05c090;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .file-upload {
            position: relative;
            display: flex;
            align-items: center;
            border: 1px dashed #dee2e6;
            border-radius: 6px;
            padding: 0.75rem;
            background-color: #f8f9fa;
            transition: all 0.2s;
            cursor: pointer;
        }
        
        .file-upload:hover {
            border-color: var(--primary);
            background-color: #f0f7ff;
        }
        
        .file-upload input {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            opacity: 0;
            cursor: pointer;
        }
        
        .file-upload-icon {
            color: var(--primary);
            font-size: 1.25rem;
            margin-right: 0.75rem;
        }
        
        .file-name {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 0.9rem;
        }
        
        .json-preview {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 0.75rem;
            max-height: 300px;
            overflow: auto;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 0.85rem;
            white-space: pre-wrap;
            margin-bottom: 1rem;
            border: 1px solid #eaeaea;
        }
        
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 0.15rem solid currentColor;
            border-right-color: transparent;
            border-radius: 50%;
            animation: spinner 0.75s linear infinite;
            margin-left: 0.5rem;
        }
        
        @keyframes spinner {
            to { transform: rotate(360deg); }
        }
        
        .alert {
            border-radius: 6px;
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
            margin-top: 1rem;
        }
        
        .row-compact {
            margin-left: -0.5rem;
            margin-right: -0.5rem;
        }
        
        .row-compact > div {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }
        
        /* Fix for button alignment */
        .btn-container {
            display: flex;
            align-items: flex-end;
            height: 100%;
        }
        
        .btn-container .btn {
            width: 100%;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="app-header">
            <h1 class="app-title">Excel to JSON</h1>
            <p class="app-subtitle">快速将Excel文件转换为JSON格式数据</p>
        </div>
        
        <div class="card">
            <div class="card-header d-flex align-items-center">
                <i class="bi bi-file-earmark-excel me-2"></i>
                <span>上传Excel文件</span>
            </div>
            <div class="card-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label class="file-upload mb-2">
                            <i class="bi bi-upload file-upload-icon"></i>
                            <span id="file-name" class="file-name">点击选择Excel/CSV文件</span>
                            <input type="file" id="file" name="file" accept=".xlsx,.xls,.csv" required>
                        </label>
                        <div class="form-text">支持 .xlsx、.xls、.csv 格式</div>
                    </div>
                    
                    <div class="row row-compact">
                        <div class="col-md-6 mb-3">
                            <label for="startRow" class="form-label">起始行</label>
                            <input type="number" class="form-control" id="startRow" name="start_row" value="" min="0">
                            <div class="form-text">可选。Excel默认跳过2行，CSV默认跳过1行。填写0表示不跳过。</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="btn-container">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-arrow-repeat me-1"></i>
                                    <span id="submit-text">转换为JSON</span>
                                    <span id="loading" class="loading d-none"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
                
                <div id="error" class="alert alert-danger d-none"></div>
            </div>
        </div>
        
        <div id="resultCard" class="card d-none">
            <div class="card-header d-flex align-items-center">
                <i class="bi bi-braces me-2"></i>
                <span>JSON预览</span>
            </div>
            <div class="card-body">
                <div id="jsonPreview" class="json-preview"></div>
                <button id="downloadBtn" class="btn btn-success w-100">
                    <i class="bi bi-download me-1"></i>下载JSON文件
                </button>
            </div>
        </div>
    </div>
    
    <script>
        document.getElementById('file').addEventListener('change', function() {
            const fileName = this.files[0] ? this.files[0].name : '点击选择Excel/CSV文件';
            document.getElementById('file-name').textContent = fileName;
        });
        
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('file');
            if (!fileInput.files.length) {
                showError('请选择一个Excel或CSV文件');
                return;
            }
            
            const file = fileInput.files[0];
            if (!file.name.toLowerCase().endsWith('.xlsx') && !file.name.toLowerCase().endsWith('.xls') && !file.name.toLowerCase().endsWith('.csv')) {
                showError('请选择Excel或CSV文件 (.xlsx、.xls、.csv)');
                return;
            }
            
            const formData = new FormData(this);
            
            // 显示加载状态
            document.getElementById('submit-text').textContent = '处理中';
            document.getElementById('loading').classList.remove('d-none');
            document.getElementById('error').classList.add('d-none');
            document.getElementById('resultCard').classList.add('d-none');
            
            fetch('/', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // 恢复按钮状态
                document.getElementById('submit-text').textContent = '转换为JSON';
                document.getElementById('loading').classList.add('d-none');
                
                if (data.success) {
                    // 显示JSON预览
                    document.getElementById('jsonPreview').textContent = data.json_data;
                    document.getElementById('resultCard').classList.remove('d-none');
                    
                    // 设置下载按钮
                    document.getElementById('downloadBtn').onclick = function() {
                        window.location.href = '/download/' + data.filename;
                    };
                } else {
                    showError(data.error || '转换失败');
                }
            })
            .catch(error => {
                document.getElementById('submit-text').textContent = '转换为JSON';
                document.getElementById('loading').classList.add('d-none');
                showError('请求失败: ' + error.message);
            });
        });
        
        function showError(message) {
            const errorElement = document.getElementById('error');
            errorElement.textContent = message;
            errorElement.classList.remove('d-none');
        }
    </script>
</body>
</html>


