import os
import threading
from tkinter import ttk, Tk, StringVar, DoubleVar, PhotoImage
from tkinter import filedialog, messagebox
from PIL import Image
import json

class ShadowProcessor:
    def __init__(self):
        self.root = Tk()
        self.root.title("像素影子处理工具")
        
        # 设置应用图标
        try:
            self.root.iconbitmap("icon.ico")  # 如果有图标文件，请替换为实际路径
        except:
            pass  # 如果没有图标文件，忽略错误

        # 变量
        self.input_path = StringVar()
        self.output_path = StringVar()
        self.progress_var = StringVar(value="准备就绪")
        self.progress_value = DoubleVar(value=0)
        
        # 添加阴影颜色选择
        self.shadow_color = StringVar(value="#3c3c3c")

        # 加载配置
        self.config_file = os.path.join(os.path.dirname(__file__), 'shadow_config.json')
        self.load_config()

        # 设置样式
        self.setup_styles()
        # 设置UI
        self.setup_ui()
        # 窗口居中
        self.center_window(650, 400)

    def setup_styles(self):
        style = ttk.Style()
        style.theme_use('clam')

        # 更现代的主题颜色
        style.configure('TFrame', background='#f8f9fa')
        style.configure('TLabel',
                       background='#f8f9fa',
                       font=('Microsoft YaHei UI', 10))
        style.configure('Header.TLabel',
                       background='#f8f9fa',
                       font=('Microsoft YaHei UI', 14, 'bold'),
                       foreground='#2c3e50')

        # 改进的进度条样式
        style.configure('Horizontal.TProgressbar',
                       thickness=10,
                       troughcolor='#e9ecef',
                       background='#4CAF50',
                       bordercolor='#f8f9fa',
                       lightcolor='#4CAF50',
                       darkcolor='#4CAF50')

        # 更现代的按钮样式
        style.configure('TButton',
                       font=('Microsoft YaHei UI', 9),
                       padding=6,
                       background='#ffffff',
                       bordercolor='#dee2e6')

        style.map('TButton',
                 background=[('active', '#f8f9fa')],
                 bordercolor=[('active', '#ced4da')])

        style.configure('Primary.TButton',
                       background='#4CAF50',
                       foreground='white',
                       bordercolor='#43a047')

        style.map('Primary.TButton',
                 background=[('active', '#43a047')],
                 bordercolor=[('active', '#388e3c')])

        # 改进的输入框样式
        style.configure('TEntry',
                       padding=10,
                       fieldbackground='white',
                       bordercolor='#dee2e6',
                       lightcolor='#dee2e6',
                       darkcolor='#dee2e6',
                       selectbackground='#4CAF50',
                       selectforeground='white')

        style.map('TEntry',
                 bordercolor=[('focus', '#4CAF50')],
                 lightcolor=[('focus', '#4CAF50')],
                 darkcolor=[('focus', '#4CAF50')])

    def setup_ui(self):
        self.root.configure(background='#f8f9fa')
        self.root.resizable(False, False)

        # 主容器
        main_frame = ttk.Frame(self.root, padding="25 20 25 20", style='TFrame')
        main_frame.pack(fill='both', expand=True)

        # 标题区域
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill='x', pady=(0, 25))
        
        header = ttk.Label(header_frame,
                          text="像素影子处理工具",
                          style='Header.TLabel')
        header.pack(side='left')
        
        # 添加版本信息
        version_label = ttk.Label(header_frame, 
                                 text="v1.0",
                                 foreground="#6c757d",
                                 background='#f8f9fa',
                                 font=('Microsoft YaHei UI', 8))
        version_label.pack(side='right', padx=5, pady=5)

        # 输入区域容器
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill='x', pady=(0, 20))

        # 输入文件夹
        ttk.Label(input_frame, text="输入文件夹:").pack(anchor='w')
        input_entry_frame = ttk.Frame(input_frame)
        input_entry_frame.pack(fill='x', pady=(5, 15))

        input_entry = ttk.Entry(input_entry_frame,
                               textvariable=self.input_path)
        input_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))

        browse_btn = ttk.Button(input_entry_frame,
                   text="选择文件夹",
                   command=lambda: self.select_folder(self.input_path))
        browse_btn.pack(side='right')

        # 输出文件夹
        ttk.Label(input_frame, text="输出文件夹:").pack(anchor='w')
        output_entry_frame = ttk.Frame(input_frame)
        output_entry_frame.pack(fill='x', pady=5)

        output_entry = ttk.Entry(output_entry_frame,
                                textvariable=self.output_path)
        output_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))

        browse_btn2 = ttk.Button(output_entry_frame,
                    text="选择文件夹",
                    command=lambda: self.select_folder(self.output_path)).pack(side='right')

        # 进度显示区域
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill='x', pady=(10, 15))

        self.progress_bar = ttk.Progressbar(progress_frame,
                                          style='Horizontal.TProgressbar',
                                          mode='determinate',
                                          variable=self.progress_value)
        self.progress_bar.pack(fill='x', pady=(0, 10))

        self.status_label = ttk.Label(progress_frame,
                                    textvariable=self.progress_var,
                                    anchor='center')
        self.status_label.pack(fill='x')

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(15, 0))

        # 添加帮助按钮
        help_btn = ttk.Button(button_frame,
                            text="帮助",
                            command=self.show_help)
        help_btn.pack(side='left')

        process_btn = ttk.Button(button_frame,
                                text="开始处理",
                                style='Primary.TButton',
                                command=self.start_processing)
        process_btn.pack(side='right', padx=(10, 0))


    def show_help(self):
        """显示帮助信息"""
        help_text = """像素影子处理工具使用说明：

1. 选择包含图片的输入文件夹
2. 选择处理后图片的保存位置
3. 点击"开始处理"按钮
4. 处理完成后会自动打开输出文件夹

本工具会自动识别图片中的绿色阴影像素，并将其转换为深灰色。
适用于处理带有绿色阴影的截图或扫描文档。
    """
        messagebox.showinfo("使用帮助", help_text)

    def load_config(self):
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.input_path.set(config.get('last_input_path', ''))
                    self.output_path.set(config.get('last_output_path', ''))
                    self.shadow_color.set(config.get('shadow_color', '#3c3c3c'))
        except Exception as e:
            print(f"加载配置失败: {e}")

    def save_config(self):
        try:
            config = {
                'last_input_path': self.input_path.get(),
                'last_output_path': self.output_path.get(),
                'shadow_color': self.shadow_color.get()
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置失败: {e}")

    def select_folder(self, path_var):
        folder = filedialog.askdirectory()
        if folder:
            path_var.set(folder)
            self.save_config()


    def change_shadow_color(self, image_path, output_path, new_color=(60, 60, 60)):
        try:
            # 从配置中获取颜色
            color_hex = self.shadow_color.get().lstrip('#')
            if len(color_hex) == 6:
                new_color = tuple(int(color_hex[i:i+2], 16) for i in (0, 2, 4))
            
            img = Image.open(image_path)
            img = img.convert("RGBA")
            datas = img.getdata()
            new_data = []

            for item in datas:
                r, g, b, a = item
                if g > r and g > b:
                    new_data.append((new_color[0], new_color[1], new_color[2], a))
                else:
                    new_data.append(item)

            img.putdata(new_data)
            img.save(output_path)
            return True
        except Exception as e:
            print(f"处理图片失败 {image_path}: {e}")
            return False

    def process_images(self):
        input_folder = self.input_path.get()
        output_folder = self.output_path.get()

        if not input_folder or not output_folder:
            messagebox.showwarning("警告", "请选择输入和输出文件夹！")
            return

        if not os.path.exists(output_folder):
            os.makedirs(output_folder)

        image_files = [f for f in os.listdir(input_folder)
                      if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif'))]
        total_images = len(image_files)

        if total_images == 0:
            messagebox.showwarning("警告", "输入文件夹中没有找到图片文件！")
            return

        success_count = 0
        for i, filename in enumerate(image_files, 1):
            input_path = os.path.join(input_folder, filename)
            output_path = os.path.join(output_folder, f'processed_{filename}')

            if self.change_shadow_color(input_path, output_path):
                success_count += 1

            progress = (i / total_images) * 100
            self.progress_value.set(progress)
            self.progress_var.set(f"处理进度: {i}/{total_images}")
            self.root.update_idletasks()

        self.progress_var.set(f"处理完成！成功: {success_count}/{total_images}")
        messagebox.showinfo("完成", f"处理完成！\n成功处理: {success_count} 个文件\n失败: {total_images - success_count} 个文件")

        # 处理完成后打开输出文件夹
        os.startfile(output_folder)

    def start_processing(self):
        threading.Thread(target=self.process_images, daemon=True).start()

    def center_window(self, width, height):
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = ShadowProcessor()
    app.run()
