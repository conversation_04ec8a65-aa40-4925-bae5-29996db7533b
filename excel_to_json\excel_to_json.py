from flask import Flask, render_template, request, send_file, jsonify
import pandas as pd
import os
import tempfile
import json
import logging
from werkzeug.utils import secure_filename
from pathlib import Path
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('excel_to_json')

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = tempfile.gettempdir()
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 限制上传文件大小为16MB

class ExcelConverter:
    """Excel/CSV转JSON转换器类"""
    
    def __init__(self, file_path, start_row=None):
        self.file_path = file_path
        self.file_ext = os.path.splitext(file_path)[1].lower()
        self.start_row = start_row
        
    def _detect_csv_delimiter(self, sample_lines=5):
        """自动检测csv分隔符"""
        import csv
        with open(self.file_path, 'r', encoding='utf-8') as f:
            sample = ''.join([f.readline() for _ in range(sample_lines)])
        try:
            dialect = csv.Sniffer().sniff(sample)
            return dialect.delimiter
        except Exception:
            return ','  # 默认逗号

    def convert(self):
        """转换Excel或CSV文件为JSON数据"""
        try:
            if self.file_ext in ['.xlsx', '.xls']:
                df = pd.read_excel(self.file_path, header=None)
                # excel默认跳2行
                default_skip = 2
            elif self.file_ext == '.csv':
                # 先尝试utf-8编码，失败再尝试gbk
                delimiter = self._detect_csv_delimiter()
                try:
                    df = pd.read_csv(self.file_path, header=None, encoding='utf-8', delimiter=delimiter)
                except UnicodeDecodeError:
                    df = pd.read_csv(self.file_path, header=None, encoding='gbk', delimiter=delimiter)
                # csv默认跳1行
                default_skip = 1
            else:
                return None, "不支持的文件类型"
            
            if df.empty:
                return None, "文件为空"
            
            # 跳过行数逻辑
            skip = self.start_row
            if skip is None:
                skip = default_skip
            else:
                try:
                    skip = int(skip)
                except Exception:
                    skip = default_skip
            
            # 获取字段名（第一行，索引为0）
            column_names = df.iloc[0].tolist()
            # 跳过指定行数
            df = df.iloc[skip:]
            df.columns = column_names
            
            # 清理数据：移除NaN值并转换为None
            df = df.where(pd.notnull(df), None)
            
            # 转换为JSON，并用data包裹
            json_data = {'data': df.to_dict(orient='records')}
            return json_data, None
            
        except Exception as e:
            logger.error(f"转换失败: {str(e)}", exc_info=True)
            return None, str(e)

def cleanup_temp_files(file_path, delay=300):
    """延迟清理临时文件"""
    def _cleanup():
        time.sleep(delay)  # 等待5分钟
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"已删除临时文件: {file_path}")
        except Exception as e:
            logger.error(f"删除临时文件失败: {str(e)}")
    
    import threading
    threading.Thread(target=_cleanup, daemon=True).start()

@app.route('/', methods=['GET', 'POST'])
def upload_file():
    if request.method == 'POST':
        # 检查是否有文件上传
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '没有上传文件'})
            
        file = request.files['file']
        
        # 检查文件名是否为空
        if file.filename == '':
            return jsonify({'success': False, 'error': '未选择文件'})
            
        # 检查文件扩展名
        if not file.filename.lower().endswith((
            '.xlsx', '.xls', '.csv')):
            return jsonify({'success': False, 'error': '仅支持Excel或CSV文件(.xlsx, .xls, .csv)'})
        
        try:
            # 安全地获取文件名并保存
            filename = secure_filename(file.filename)
            start_row = request.form.get('start_row', None)
            
            excel_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(excel_path)
            
            # 转换Excel/CSV到JSON
            converter = ExcelConverter(excel_path, start_row)
            json_data, error = converter.convert()
            
            if error:
                return jsonify({'success': False, 'error': error})
            
            # 生成JSON文件
            json_filename = f"{Path(filename).stem}_{int(time.time())}.json"
            json_path = os.path.join(app.config['UPLOAD_FOLDER'], json_filename)
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
            
            # 安排清理临时文件
            cleanup_temp_files(excel_path)
            cleanup_temp_files(json_path, delay=3600)  # JSON文件保留1小时
            
            # 返回成功响应
            return jsonify({
                'success': True, 
                'json_data': json.dumps(json_data, ensure_ascii=False, indent=2),
                'filename': json_filename
            })
            
        except Exception as e:
            logger.error(f"处理上传文件失败: {str(e)}", exc_info=True)
            return jsonify({'success': False, 'error': f"处理文件时出错: {str(e)}"})
    
    # GET请求返回HTML页面
    return render_template('index.html')

@app.route('/download/<filename>')
def download_file(filename):
    """下载生成的JSON文件"""
    json_path = os.path.join(app.config['UPLOAD_FOLDER'], secure_filename(filename))
    if os.path.exists(json_path):
        return send_file(
            json_path,
            as_attachment=True,
            download_name=filename,
            mimetype='application/json'
        )
    return jsonify({'success': False, 'error': '文件不存在'}), 404

# 确保templates目录存在
def ensure_template_dir_exists():
    """确保templates目录存在"""
    template_dir = os.path.join(os.path.dirname(__file__), 'templates')
    if not os.path.exists(template_dir):
        os.makedirs(template_dir)
        logger.info(f"已创建templates目录: {template_dir}")

if __name__ == '__main__':
    # 确保templates目录存在
    ensure_template_dir_exists()
    app.run(host='0.0.0.0', port=6288, debug=True)
