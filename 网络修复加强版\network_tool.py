import os
import winreg
import logging
from PyQt5.QtWidgets import (QWidget, QPushButton, QLabel, QVBoxLayout,
                            QProgressBar, QHBoxLayout, QSystemTrayIcon, QMenu,
                            QAction, QComboBox, QMessageBox, QStyle)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QIcon
from worker import Worker

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('network_tool.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ProxyNetworkTool(QWidget):
    def __init__(self):
        super().__init__()
        self.worker = None
        self.tray_icon = None
        self.proxy_server = self.get_current_proxy()
        self.is_tray_action = False  # 添加标记判断是否来自托盘操作
        self.is_window_hidden = False  # 添加窗口隐藏状态标记
        self.message_queue = []  # 添加消息队列
        self.is_showing_message = False  # 添加消息显示状态标记
        self.initUI()
        self.setup_tray()

        # 初始化时检查代理状态并更新显示
        if self.proxy_server:
            self.status_label.setText(f"代理已启用: {self.proxy_server}")

        # 添加定时器检查代理状态
        self.check_timer = QTimer(self)
        self.check_timer.timeout.connect(self.check_proxy_status)
        self.check_timer.start(3000)  # 每3秒检查一次

    def check_proxy_status(self):
        """检查并更新代理状态"""
        current_proxy = self.get_current_proxy()
        if current_proxy != self.proxy_server:
            self.proxy_server = current_proxy
            self.update_proxy_ui()
            # 根据代理状态更新提示信息
            if current_proxy:
                self.status_label.setText(f"代理已启用: {current_proxy}")
            else:
                self.status_label.setText("点击按钮进行操作")

    def update_proxy_ui(self):
        """更新代理相关的UI显示"""
        self.proxy_input.clear()
        current_proxy = self.get_current_proxy()
        if current_proxy:
            self.proxy_input.addItem(current_proxy)
            self.proxy_input.setEnabled(True)
            self.disable_proxy_button.setEnabled(True)
        else:
            self.proxy_input.addItem("无代理服务器")
            self.proxy_input.setEnabled(False)
            self.disable_proxy_button.setEnabled(False) # Disable the button when no proxy

    def get_current_proxy(self):
        """获取当前系统代理设置"""
        try:
            reg_key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"Software\Microsoft\Windows\CurrentVersion\Internet Settings",
                0,
                winreg.KEY_READ
            )

            try:
                proxy_enabled = winreg.QueryValueEx(reg_key, "ProxyEnable")[0]
                proxy_server = winreg.QueryValueEx(reg_key, "ProxyServer")[0] if proxy_enabled else None
            except:
                proxy_server = None

            winreg.CloseKey(reg_key)
            return proxy_server if proxy_enabled and proxy_server else None
        except:
            return None

    def repair_network(self):
        """执行网络修复"""
        try:
            # 禁用所有按钮
            self.disable_all_buttons()
            
            # 开始修复任务
            self.start_task('repair_network')
            
        except Exception as e:
            logging.error(f"启动修复网络任务失败: {str(e)}")
            self._do_show_message("错误", f"启动修复网络任务失败: {str(e)}")
            self.enable_all_buttons()

    def disable_all_buttons(self):
        """禁用所有按钮"""
        self.disable_proxy_button.setEnabled(False)
        self.test_button.setEnabled(False)
        self.repair_button.setEnabled(False)
        self.clear_cache_button.setEnabled(False)
        self.cancel_button.setEnabled(True)

    def enable_all_buttons(self):
        """启用所有按钮"""
        self.disable_proxy_button.setEnabled(True)
        self.test_button.setEnabled(True)
        self.repair_button.setEnabled(True)
        self.clear_cache_button.setEnabled(True)
        self.cancel_button.setEnabled(False)

    def initUI(self):
        # 设置窗口属性
        self.setWindowTitle('代理与网络工具')
        self.setFixedSize(400, 450)
        self.setWindowIcon(QIcon(self.get_icon_path()))

        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)

        # 代理设置区域
        proxy_group = QVBoxLayout()

        # 代理服务器输入
        proxy_layout = QHBoxLayout()
        proxy_label = QLabel('代理服务器:')
        self.proxy_input = QComboBox()
        self.proxy_input.setEditable(False)
        self.proxy_input.setMinimumWidth(200)

        # 代理操作按钮
        self.disable_proxy_button = QPushButton('关闭代理', self)

        # 添加代理设置
        if self.proxy_server:
            self.proxy_input.addItem(self.proxy_server)
        else:
            self.proxy_input.addItem("无代理服务器")
        self.proxy_input.setEnabled(bool(self.proxy_server))

        proxy_layout.addWidget(proxy_label)
        proxy_layout.addWidget(self.proxy_input)
        proxy_group.addLayout(proxy_layout)

        # 代理操作按钮布局
        proxy_buttons = QHBoxLayout()
        proxy_buttons.addWidget(self.disable_proxy_button)
        proxy_group.addLayout(proxy_buttons)

        main_layout.addLayout(proxy_group)

        # 分隔线
        separator = QLabel()
        separator.setFrameShape(QLabel.HLine)
        separator.setFrameShadow(QLabel.Sunken)
        main_layout.addWidget(separator)

        # 网络测试区域
        self.test_button = QPushButton('测试网络连接', self)
        main_layout.addWidget(self.test_button)

        # 修复按钮
        self.repair_button = QPushButton('修复网络', self)
        main_layout.addWidget(self.repair_button)
        
        # 添加清理缓存按钮
        self.clear_cache_button = QPushButton('清理网络缓存', self)
        main_layout.addWidget(self.clear_cache_button)

        # 取消按钮
        self.cancel_button = QPushButton('取消任务', self)
        main_layout.addWidget(self.cancel_button)

        # 状态和进度
        self.status_label = QLabel('点击按钮进行操作', self)
        self.progress_bar = QProgressBar(self)
        main_layout.addWidget(self.status_label)
        main_layout.addWidget(self.progress_bar)

        main_layout.addStretch()

        # 设置字体
        font = QFont('Microsoft YaHei', 10)  # 使用微软雅黑字体
        for widget in [self.disable_proxy_button,
                    self.test_button, self.cancel_button, self.status_label,
                    self.repair_button]:
            widget.setFont(font)

        # 美化样式
        self.repair_button.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                padding: 10px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        self.disable_proxy_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                padding: 10px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #e68a00;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        self.test_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                padding: 10px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #333333; padding: 5px;")

        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #cccccc;
                border-radius: 5px;
                text-align: center;
                background-color: #f0f0f0;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)

                # 在其他按钮样式设置后添加
        self.clear_cache_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                padding: 10px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #0b7dda;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        
        # 添加字体设置
        self.clear_cache_button.setFont(font)

        # 设置布局
        self.setLayout(main_layout)
        self.cancel_button.setEnabled(False)

        # 连接信号
        self.disable_proxy_button.clicked.connect(self.disable_proxy)
        self.test_button.clicked.connect(self.test_network)
        self.cancel_button.clicked.connect(self.cancel_task)
        self.repair_button.clicked.connect(self.repair_network)
        # 添加清理缓存按钮的点击事件连接
        self.clear_cache_button.clicked.connect(self.clear_cache)

        # 设置窗口居中
        self.center()

        self.update_proxy_ui()

    def get_icon_path(self):
        """获取图标路径，如果不存在则返回空"""
        icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "network_icon.ico")
        if os.path.exists(icon_path):
            return icon_path
        return ""

        # 修改清理缓存的处理函数
    def clear_cache(self):
        self.status_label.setText("正在清理系统缓存...")
        self.progress_bar.setValue(5)
        
        # 开始清理缓存任务
        self.start_task('clear_cache')

    def setup_tray(self):
        """设置系统托盘"""
        try:
            if not QSystemTrayIcon.isSystemTrayAvailable():
                logging.error("系统托盘不可用")
                return

            # 检查系统是否支持气泡提示
            if not QSystemTrayIcon.supportsMessages():
                logging.error("系统不支持托盘气泡提示")
                return

            # 创建托盘图标
            self.tray_icon = QSystemTrayIcon(self)
            
            # 设置图标（使用系统电脑图标，更容易被看到）
            self.tray_icon.setIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))

            # 设置鼠标悬停时的提示文本
            self.tray_icon.setToolTip("🌐 网络修复测试工具")
            
            # 连接消息点击信号
            self.tray_icon.messageClicked.connect(self.on_tray_message_clicked)

            # 创建托盘菜单
            tray_menu = QMenu()

            # 添加菜单项
            show_action = QAction("显示窗口", self)
            disable_proxy_action = QAction("关闭代理", self)
            test_network_action = QAction("测试网络连接", self)
            clear_cache_action = QAction("清理网络缓存", self)
            quit_action = QAction("退出", self)

            # 连接信号
            show_action.triggered.connect(self.show_from_tray)
            disable_proxy_action.triggered.connect(self.tray_disable_proxy)
            test_network_action.triggered.connect(self.tray_test_network)
            clear_cache_action.triggered.connect(self.tray_clear_cache)
            quit_action.triggered.connect(self.close_app)

            # 添加到菜单
            tray_menu.addAction(show_action)
            tray_menu.addSeparator()
            tray_menu.addAction(disable_proxy_action)
            tray_menu.addAction(test_network_action)
            tray_menu.addAction(clear_cache_action)
            tray_menu.addSeparator()
            tray_menu.addAction(quit_action)

            # 设置托盘菜单
            self.tray_icon.setContextMenu(tray_menu)
            self.tray_icon.activated.connect(self.tray_activated)
            
            # 显示托盘图标
            self.tray_icon.show()
            logging.info("系统托盘初始化完成")

            # 延迟显示测试消息
            QTimer.singleShot(2000, self.test_tray_message)
            
        except Exception as e:
            logging.error(f"设置系统托盘时出错: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())

    def test_tray_message(self):
        """测试托盘气泡提示"""
        try:
            if not self.tray_icon:
                logging.error("托盘图标对象不存在")
                return

            if not self.tray_icon.isVisible():
                logging.error("托盘图标不可见")
                return

            logging.info("正在测试托盘气泡提示...")
            
            # 使用自定义图标
            icon = self.style().standardIcon(QStyle.SP_ComputerIcon)
            
            # 直接调用显示消息
            self.tray_icon.showMessage(
                "程序已启动",
                "程序将在系统托盘运行",
                icon,
                5000
            )
            
            logging.info("托盘气泡提示测试消息已发送")
        except Exception as e:
            logging.error(f"托盘气泡提示测试失败: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())

    def show_from_tray(self):
        """从托盘显示窗口"""
        self.show()
        self.activateWindow()
        self.is_window_hidden = False

    def tray_disable_proxy(self):
        """托盘菜单 - 关闭代理"""
        logging.info("托盘菜单 - 执行关闭代理")
        self.is_tray_action = True
        self.disable_proxy()

    def tray_test_network(self):
        """托盘菜单 - 测试网络"""
        logging.info("托盘菜单 - 执行测试网络")
        self.is_tray_action = True
        self.test_network()

    def tray_clear_cache(self):
        """托盘菜单 - 清理缓存"""
        logging.info("托盘菜单 - 执行清理缓存")
        self.is_tray_action = True
        self.clear_cache()

    def tray_activated(self, reason):
        """托盘图标被激活时的处理"""
        if reason == QSystemTrayIcon.DoubleClick:
            self.show()
            self.activateWindow()

    def hideEvent(self, event):
        """窗口隐藏事件"""
        super().hideEvent(event)
        self.is_window_hidden = True

    def showEvent(self, event):
        """窗口显示事件"""
        super().showEvent(event)
        self.is_window_hidden = False

    def closeEvent(self, event):
        """重写关闭事件，默认最小化到托盘"""
        if self.tray_icon and self.tray_icon.isVisible():
            self.hide()
            self.is_window_hidden = True
            self.tray_icon.showMessage(
                "提示",
                "程序已最小化到系统托盘，右键点击托盘图标可以退出程序。",
                self.style().standardIcon(QStyle.SP_MessageBoxInformation),
                2000
            )
            event.ignore()
        else:
            event.accept()

    def close_app(self):
        """完全退出应用"""
        if self.tray_icon:
            self.tray_icon.hide()
        from PyQt5.QtWidgets import QApplication
        QApplication.quit()

    def center(self):
        # 获取屏幕几何信息并居中窗口
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.primaryScreen().geometry()
        size = self.geometry()
        self.move((screen.width() - size.width()) // 2,
                  (screen.height() - size.height()) // 2)

    def update_status(self, text):
        """更新状态文本，只更新状态标签而不显示托盘消息"""
        self.status_label.setText(text)

    def update_progress(self, value):
        self.progress_bar.setValue(value)

    def show_tray_message(self, title, message):
        """显示托盘气泡提示"""
        try:
            if self.tray_icon and self.tray_icon.isVisible():
                # 确保在主线程中调用
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(100, lambda: self._do_show_message(title, message))
        except Exception as e:
            logging.error(f"显示托盘消息错误: {str(e)}")

    def _do_show_message(self, title, message):
        """实际显示托盘消息的方法"""
        try:
            if not self.tray_icon:
                logging.error("托盘图标对象不存在")
                return

            if not self.tray_icon.isVisible():
                logging.error("托盘图标不可见")
                return

            if not QSystemTrayIcon.supportsMessages():
                logging.error("系统不支持托盘气泡提示")
                return

            # 将消息添加到队列
            self.message_queue.append((title, message))
            
            # 如果当前没有在显示消息，开始显示
            if not self.is_showing_message:
                self._process_message_queue()

        except Exception as e:
            logging.error(f"显示托盘消息错误: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())

    def _process_message_queue(self):
        """处理消息队列"""
        if not self.message_queue:
            self.is_showing_message = False
            return

        self.is_showing_message = True
        title, message = self.message_queue.pop(0)
        
        logging.info(f"准备显示托盘消息: {title} - {message}")
        
        try:
            # 使用Windows原生图标
            icon = self.style().standardIcon(QStyle.SP_MessageBoxInformation)
            
            # 显示消息，使用较长的显示时间
            self.tray_icon.showMessage(
                f"🌐 {title}",  # 添加表情符号增加可见性
                message,
                icon,
                3500  # 显示3.5秒
            )
            
            # 确保消息显示后再处理下一条
            QTimer.singleShot(4000, lambda: self._show_next_message())
            
            logging.info(f"托盘消息已发送: {title} - {message}")
        except Exception as e:
            logging.error(f"显示消息失败: {str(e)}")
            # 如果当前消息显示失败，尝试显示下一条
            QTimer.singleShot(1000, self._show_next_message)

    def _show_next_message(self):
        """显示下一条消息"""
        if self.message_queue:
            self._process_message_queue()
        else:
            self.is_showing_message = False

    def task_finished(self):
        """任务完成时的处理"""
        try:
            logging.info("任务完成处理开始")
            
            # 保存当前任务信息
            current_task = "未知任务"
            if self.worker:
                current_task = self.worker.task
            
            try:
                # 根据当前代理状态启用/禁用按钮
                if self.worker:
                    if self.is_tray_action:  # 只在托盘操作完成时显示提示
                        if self.worker.task == 'disable_proxy':
                            self._do_show_message("✅ 代理已关闭", "系统代理已成功关闭")
                        elif self.worker.task == 'enable_proxy':
                            self._do_show_message("✅ 代理已启用", f"系统代理已设置为: {self.proxy_server}")
                        elif self.worker.task == 'test_network':
                            self._do_show_message("✅ 测试完成", "网络连接测试已完成")
                        elif self.worker.task == 'repair_network':
                            self._do_show_message("✅ 修复完成", "网络修复操作已完成")
                        elif self.worker.task == 'clear_cache':
                            self._do_show_message("✅ 清理完成", "网络缓存清理已完成")
                    
                    if self.worker.task == 'disable_proxy':
                        self.proxy_server = None
                        self.proxy_input.setEnabled(True)
                        self.disable_proxy_button.setEnabled(False)
                    elif self.worker.task == 'enable_proxy':
                        self.proxy_server = self.worker.proxy_server
                        self.proxy_input.setEnabled(True)
                        self.disable_proxy_button.setEnabled(True)
            except Exception as e:
                logging.error(f"处理任务状态时出错: {str(e)}")

            # 启用所有按钮
            self.enable_all_buttons()
            
            # 更新界面
            self.update_proxy_ui()
            
            # 清理工作线程
            if self.worker:
                try:
                    self.worker.disconnect()
                    self.worker = None
                except Exception as e:
                    logging.warning(f"清理worker时出错: {str(e)}")
            
            self.is_tray_action = False  # 重置标记
            
            logging.info(f"任务 {current_task} 完成处理结束")
            
        except Exception as e:
            error_msg = f"任务完成处理时出错: {str(e)}"
            logging.error(error_msg)
            self._do_show_message("错误", error_msg)
            self.enable_all_buttons()
            if self.worker:
                try:
                    self.worker.disconnect()
                except:
                    pass
            self.worker = None
            self.is_tray_action = False

    def show_error(self, message):
        """显示错误消息"""
        try:
            logging.error(f"错误: {message}")
            if self.is_tray_action:
                self._do_show_message("错误", message)
            else:
                QMessageBox.critical(self, "错误", message)
        except Exception as e:
            logging.error(f"显示错误消息时出错: {str(e)}")
            try:
                QMessageBox.critical(None, "错误", str(message))
            except:
                pass

    def start_task(self, task):
        """开始执行任务"""
        try:
            logging.info(f"开始执行任务: {task}")
            
            # 检查是否已有任务在运行
            if self.worker and self.worker.isRunning():
                error_msg = "已有任务正在执行，请等待当前任务完成"
                logging.warning(error_msg)
                self.show_error(error_msg)
                return
            
            # 如果有旧的worker，确保它被清理
            if self.worker:
                try:
                    self.worker.disconnect()
                    self.worker.wait()
                    self.worker = None
                except Exception as e:
                    logging.warning(f"清理旧worker时出错: {str(e)}")
            
            # 禁用所有按钮
            self.disable_all_buttons()
            self.progress_bar.setValue(0)

            try:
                # 创建工作线程
                proxy_server = self.proxy_input.currentText() if task == 'enable_proxy' else None
                self.worker = Worker(task, proxy_server)
                
                # 连接信号
                self.worker.status_signal.connect(self.update_status)
                self.worker.progress_signal.connect(self.update_progress)
                self.worker.finished_signal.connect(self.task_finished)
                self.worker.error_signal.connect(self.show_error)
                
                # 启动线程
                self.worker.start()
                logging.info(f"任务 {task} 已启动")
                
            except Exception as e:
                error_msg = f"创建工作线程失败: {str(e)}"
                logging.error(error_msg)
                self.show_error(error_msg)
                self.enable_all_buttons()
                self.worker = None
                return
            
        except Exception as e:
            error_msg = f"启动任务失败: {str(e)}"
            logging.error(error_msg)
            self.show_error(error_msg)
            self.enable_all_buttons()
            self.is_tray_action = False
            self.worker = None

    def test_network(self):
        self.start_task('test_network')

    def enable_proxy(self):
        if not self.proxy_input.currentText().strip():
            QMessageBox.warning(self, "警告", "请输入代理服务器地址")
            return
        self.start_task('enable_proxy')

    def disable_proxy(self):
        self.start_task('disable_proxy')

    def cancel_task(self):
        if self.worker and self.worker.isRunning():
            self.worker.cancel()
            self.status_label.setText("任务已取消")
            self.progress_bar.setValue(0)
            self.worker.wait()
            self.task_finished()

    def on_tray_message_clicked(self):
        """托盘消息被点击时的处理"""
        self.show()
        self.activateWindow()
