"""
现代化按钮组件
提供美观的按钮样式和交互效果
"""

from typing import Optional, Union
from PyQt5.QtWidgets import QPushButton, QGraphicsDropShadowEffect
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect, pyqtSignal, QTimer
from PyQt5.QtGui import QColor, QPainter, QPen, QBrush

from app.ui_styles import style_manager
from app.config import config


class ModernButton(QPushButton):
    """现代化按钮组件"""
    
    # 自定义信号
    longPressed = pyqtSignal()  # 长按信号
    
    def __init__(self, text: str = "", parent=None, button_type: str = "primary"):
        """
        初始化现代化按钮
        
        Args:
            text: 按钮文字
            parent: 父组件
            button_type: 按钮类型 ('primary', 'secondary', 'success', 'warning', 'danger')
        """
        super().__init__(text, parent)
        
        self.button_type = button_type
        self._is_loading = False
        self._original_text = text
        self._hover_animation = None
        self._press_animation = None
        self._loading_timer = None
        self._long_press_timer = None
        self._press_start_pos = None
        
        # 设置基本属性
        self.setCursor(Qt.PointingHandCursor)
        self.setFocusPolicy(Qt.StrongFocus)
        
        # 初始化样式和动画
        self._setup_style()
        self._setup_animations()
        self._setup_shadow_effect()
    
    def _setup_style(self):
        """设置按钮样式"""
        if self.button_type == "primary":
            self.setStyleSheet(style_manager.get_modern_button_style())
        else:
            # 其他类型的按钮样式
            self.setStyleSheet(self._get_button_style_by_type())
    
    def _get_button_style_by_type(self) -> str:
        """根据类型获取按钮样式"""
        styles = {
            "secondary": f"""
                QPushButton {{
                    background-color: {config.TEXT_COLOR_MUTED};
                    color: white;
                    border: none;
                    padding: {config.PADDING_SMALL}px;
                    border-radius: {config.BORDER_RADIUS_SMALL}px;
                    min-width: {config.BUTTON_WIDTH}px;
                    font-family: '{config.FONT_FAMILY}';
                }}
                QPushButton:hover {{
                    background-color: #555;
                }}
                QPushButton:pressed {{
                    background-color: #333;
                }}
                QPushButton:disabled {{
                    background-color: {config.BORDER_COLOR_DISABLED};
                    color: {config.TEXT_COLOR_DISABLED};
                }}
            """,
            "success": f"""
                QPushButton {{
                    background-color: #28a745;
                    color: white;
                    border: none;
                    padding: {config.PADDING_SMALL}px;
                    border-radius: {config.BORDER_RADIUS_SMALL}px;
                    min-width: {config.BUTTON_WIDTH}px;
                    font-family: '{config.FONT_FAMILY}';
                }}
                QPushButton:hover {{
                    background-color: #218838;
                }}
                QPushButton:pressed {{
                    background-color: #1e7e34;
                }}
                QPushButton:disabled {{
                    background-color: {config.BORDER_COLOR_DISABLED};
                    color: {config.TEXT_COLOR_DISABLED};
                }}
            """,
            "warning": f"""
                QPushButton {{
                    background-color: #ffc107;
                    color: #212529;
                    border: none;
                    padding: {config.PADDING_SMALL}px;
                    border-radius: {config.BORDER_RADIUS_SMALL}px;
                    min-width: {config.BUTTON_WIDTH}px;
                    font-family: '{config.FONT_FAMILY}';
                }}
                QPushButton:hover {{
                    background-color: #e0a800;
                }}
                QPushButton:pressed {{
                    background-color: #d39e00;
                }}
                QPushButton:disabled {{
                    background-color: {config.BORDER_COLOR_DISABLED};
                    color: {config.TEXT_COLOR_DISABLED};
                }}
            """,
            "danger": f"""
                QPushButton {{
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: {config.PADDING_SMALL}px;
                    border-radius: {config.BORDER_RADIUS_SMALL}px;
                    min-width: {config.BUTTON_WIDTH}px;
                    font-family: '{config.FONT_FAMILY}';
                }}
                QPushButton:hover {{
                    background-color: #c82333;
                }}
                QPushButton:pressed {{
                    background-color: #bd2130;
                }}
                QPushButton:disabled {{
                    background-color: {config.BORDER_COLOR_DISABLED};
                    color: {config.TEXT_COLOR_DISABLED};
                }}
            """
        }
        return styles.get(self.button_type, style_manager.get_modern_button_style())
    
    def _setup_animations(self):
        """设置动画效果"""
        # 悬停动画
        self._hover_animation = QPropertyAnimation(self, b"geometry")
        self._hover_animation.setDuration(config.ANIMATION_DURATION)
        self._hover_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 按压动画
        self._press_animation = QPropertyAnimation(self, b"geometry")
        self._press_animation.setDuration(50)
        self._press_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def _setup_shadow_effect(self):
        """设置阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(8)
        shadow.setColor(QColor(0, 0, 0, 60))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)
    
    def set_loading(self, loading: bool):
        """设置加载状态"""
        if self._is_loading == loading:
            return
        
        self._is_loading = loading
        
        if loading:
            self._original_text = self.text()
            self.setText("加载中...")
            self.setEnabled(False)
            
            # 启动加载动画
            self._start_loading_animation()
        else:
            self.setText(self._original_text)
            self.setEnabled(True)
            
            # 停止加载动画
            self._stop_loading_animation()
    
    def _start_loading_animation(self):
        """启动加载动画"""
        if self._loading_timer:
            self._loading_timer.stop()
        
        self._loading_timer = QTimer()
        self._loading_timer.timeout.connect(self._update_loading_text)
        self._loading_timer.start(500)  # 每500ms更新一次
        
        self._loading_dots = 0
    
    def _stop_loading_animation(self):
        """停止加载动画"""
        if self._loading_timer:
            self._loading_timer.stop()
            self._loading_timer = None
    
    def _update_loading_text(self):
        """更新加载文字"""
        if not self._is_loading:
            return
        
        self._loading_dots = (self._loading_dots + 1) % 4
        dots = "." * self._loading_dots
        self.setText(f"加载中{dots}")
    
    def set_button_type(self, button_type: str):
        """设置按钮类型"""
        if button_type != self.button_type:
            self.button_type = button_type
            self._setup_style()
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        super().enterEvent(event)
        if not self._is_loading and self.isEnabled():
            self._animate_hover(True)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        super().leaveEvent(event)
        if not self._is_loading:
            self._animate_hover(False)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton and not self._is_loading:
            self._press_start_pos = event.pos()
            self._animate_press(True)
            
            # 启动长按计时器
            if self._long_press_timer:
                self._long_press_timer.stop()
            
            self._long_press_timer = QTimer()
            self._long_press_timer.setSingleShot(True)
            self._long_press_timer.timeout.connect(self._on_long_press)
            self._long_press_timer.start(800)  # 800ms长按
        
        super().mousePressEvent(event)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and not self._is_loading:
            self._animate_press(False)
            
            # 停止长按计时器
            if self._long_press_timer:
                self._long_press_timer.stop()
                self._long_press_timer = None
        
        super().mouseReleaseEvent(event)
    
    def _on_long_press(self):
        """长按事件处理"""
        self.longPressed.emit()
    
    def _animate_hover(self, hover: bool):
        """悬停动画"""
        if not self._hover_animation:
            return
        
        current_rect = self.geometry()
        
        if hover:
            # 轻微放大
            new_rect = QRect(
                current_rect.x() - 1,
                current_rect.y() - 1,
                current_rect.width() + 2,
                current_rect.height() + 2
            )
        else:
            # 恢复原始大小
            new_rect = QRect(
                current_rect.x() + 1,
                current_rect.y() + 1,
                current_rect.width() - 2,
                current_rect.height() - 2
            )
        
        self._hover_animation.setStartValue(current_rect)
        self._hover_animation.setEndValue(new_rect)
        self._hover_animation.start()
    
    def _animate_press(self, pressed: bool):
        """按压动画"""
        if not self._press_animation:
            return
        
        current_rect = self.geometry()
        
        if pressed:
            # 轻微缩小
            new_rect = QRect(
                current_rect.x() + 1,
                current_rect.y() + 1,
                current_rect.width() - 2,
                current_rect.height() - 2
            )
        else:
            # 恢复大小
            new_rect = QRect(
                current_rect.x() - 1,
                current_rect.y() - 1,
                current_rect.width() + 2,
                current_rect.height() + 2
            )
        
        self._press_animation.setStartValue(current_rect)
        self._press_animation.setEndValue(new_rect)
        self._press_animation.start()
    
    def paintEvent(self, event):
        """自定义绘制事件"""
        super().paintEvent(event)
        
        # 如果是加载状态，可以在这里绘制额外的加载指示器
        if self._is_loading:
            painter = QPainter(self)
            painter.setRenderHint(QPainter.Antialiasing)
            
            # 绘制简单的加载指示器（可选）
            # 这里可以添加旋转的圆圈或其他加载动画
            pass
    
    def sizeHint(self):
        """建议尺寸"""
        hint = super().sizeHint()
        # 确保最小宽度
        if hint.width() < config.BUTTON_WIDTH:
            hint.setWidth(config.BUTTON_WIDTH)
        return hint
    
    def is_loading(self) -> bool:
        """获取加载状态"""
        return self._is_loading
    
    def get_button_type(self) -> str:
        """获取按钮类型"""
        return self.button_type
